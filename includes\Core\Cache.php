<?php
/**
 * Cache Class
 * 
 * High-performance caching system with multiple layers and intelligent invalidation
 * 
 * @package AiSeoOptimizerUltra
 * @since 9.0.0
 */

namespace AiSeoOptimizerUltra\Core;

use Exception;

/**
 * Cache Class
 * 
 * Multi-layer caching system for optimal performance
 */
final class Cache {
    private static $instance = null;
    private $memory_cache = [];
    private $cache_groups = [
        'options' => 'ai_seo_ultra_options',
        'api' => 'ai_seo_ultra_api',
        'scores' => 'ai_seo_ultra_scores',
        'schema' => 'ai_seo_ultra_schema',
        'meta' => 'ai_seo_ultra_meta',
        'posts' => 'ai_seo_ultra_posts'
    ];
    
    private $default_expiry = [
        'options' => HOUR_IN_SECONDS,
        'api' => DAY_IN_SECONDS,
        'scores' => DAY_IN_SECONDS,
        'schema' => WEEK_IN_SECONDS,
        'meta' => HOUR_IN_SECONDS,
        'posts' => HOUR_IN_SECONDS
    ];
    
    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Private constructor
     */
    private function __construct() {
        // Register cleanup hooks
        add_action('ai_seo_ultra_cache_cleanup', [$this, 'cleanup_expired']);
        
        // Schedule cleanup if not already scheduled
        if (!wp_next_scheduled('ai_seo_ultra_cache_cleanup')) {
            wp_schedule_event(time(), 'hourly', 'ai_seo_ultra_cache_cleanup');
        }
    }
    
    /**
     * Get cached value
     */
    public static function get($key, $group = 'default', $default = false) {
        $instance = self::get_instance();
        return $instance->get_cache($key, $group, $default);
    }
    
    /**
     * Set cached value
     */
    public static function set($key, $value, $group = 'default', $expiry = null) {
        $instance = self::get_instance();
        return $instance->set_cache($key, $value, $group, $expiry);
    }
    
    /**
     * Delete cached value
     */
    public static function delete($key, $group = 'default') {
        $instance = self::get_instance();
        return $instance->delete_cache($key, $group);
    }
    
    /**
     * Clear all cache for a group
     */
    public static function clear_group($group) {
        $instance = self::get_instance();
        return $instance->clear_cache_group($group);
    }
    
    /**
     * Clear all cache
     */
    public static function clear_all() {
        $instance = self::get_instance();
        return $instance->clear_all_cache();
    }
    
    /**
     * Clear post-specific cache
     */
    public static function clear_post_cache($post_id) {
        $instance = self::get_instance();
        return $instance->clear_post_specific_cache($post_id);
    }
    
    /**
     * Internal get cache method
     */
    private function get_cache($key, $group, $default) {
        try {
            $cache_key = $this->build_cache_key($key, $group);
            
            // Check memory cache first (fastest)
            if (isset($this->memory_cache[$cache_key])) {
                $cached_data = $this->memory_cache[$cache_key];
                
                // Check if expired
                if ($cached_data['expires'] > time()) {
                    Logger::debug("Cache hit (memory): {$cache_key}");
                    return $cached_data['data'];
                } else {
                    // Remove expired from memory
                    unset($this->memory_cache[$cache_key]);
                }
            }
            
            // Check WordPress object cache
            $wp_cache_group = $this->get_wp_cache_group($group);
            $cached_value = wp_cache_get($cache_key, $wp_cache_group);
            
            if ($cached_value !== false) {
                // Store in memory cache for faster subsequent access
                $this->store_in_memory($cache_key, $cached_value, $group);
                Logger::debug("Cache hit (WP object cache): {$cache_key}");
                return $cached_value;
            }
            
            // Check transient cache (database)
            $transient_key = $this->build_transient_key($key, $group);
            $transient_value = get_transient($transient_key);
            
            if ($transient_value !== false) {
                // Store in higher-level caches
                wp_cache_set($cache_key, $transient_value, $wp_cache_group, $this->get_expiry($group));
                $this->store_in_memory($cache_key, $transient_value, $group);
                Logger::debug("Cache hit (transient): {$cache_key}");
                return $transient_value;
            }
            
            Logger::debug("Cache miss: {$cache_key}");
            return $default;
            
        } catch (Exception $e) {
            Logger::error("Cache get error for key {$key}: " . $e->getMessage());
            return $default;
        }
    }
    
    /**
     * Internal set cache method
     */
    private function set_cache($key, $value, $group, $expiry) {
        try {
            $cache_key = $this->build_cache_key($key, $group);
            $expiry = $expiry ?: $this->get_expiry($group);
            
            // Store in all cache layers
            $this->store_in_memory($cache_key, $value, $group, $expiry);
            
            $wp_cache_group = $this->get_wp_cache_group($group);
            wp_cache_set($cache_key, $value, $wp_cache_group, $expiry);
            
            $transient_key = $this->build_transient_key($key, $group);
            set_transient($transient_key, $value, $expiry);
            
            Logger::debug("Cache set: {$cache_key} (expires in {$expiry}s)");
            return true;
            
        } catch (Exception $e) {
            Logger::error("Cache set error for key {$key}: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Internal delete cache method
     */
    private function delete_cache($key, $group) {
        try {
            $cache_key = $this->build_cache_key($key, $group);
            
            // Remove from all cache layers
            unset($this->memory_cache[$cache_key]);
            
            $wp_cache_group = $this->get_wp_cache_group($group);
            wp_cache_delete($cache_key, $wp_cache_group);
            
            $transient_key = $this->build_transient_key($key, $group);
            delete_transient($transient_key);
            
            Logger::debug("Cache deleted: {$cache_key}");
            return true;
            
        } catch (Exception $e) {
            Logger::error("Cache delete error for key {$key}: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Clear cache group
     */
    private function clear_cache_group($group) {
        try {
            // Clear memory cache for this group
            $group_prefix = $this->build_cache_key('', $group);
            foreach ($this->memory_cache as $key => $data) {
                if (strpos($key, $group_prefix) === 0) {
                    unset($this->memory_cache[$key]);
                }
            }
            
            // WordPress object cache doesn't have a direct group flush
            // We'll use a group version increment approach
            $this->increment_group_version($group);
            
            // Clear transients for this group
            $this->clear_group_transients($group);
            
            Logger::info("Cache group cleared: {$group}");
            return true;
            
        } catch (Exception $e) {
            Logger::error("Cache group clear error for group {$group}: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Clear all cache
     */
    private function clear_all_cache() {
        try {
            // Clear memory cache
            $this->memory_cache = [];
            
            // Clear all group versions
            foreach (array_keys($this->cache_groups) as $group) {
                $this->increment_group_version($group);
                $this->clear_group_transients($group);
            }
            
            // Clear WordPress object cache (if supported)
            if (function_exists('wp_cache_flush')) {
                wp_cache_flush();
            }
            
            Logger::info("All cache cleared");
            return true;
            
        } catch (Exception $e) {
            Logger::error("Clear all cache error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Clear post-specific cache
     */
    private function clear_post_specific_cache($post_id) {
        try {
            $post_id = absint($post_id);
            
            // Clear various post-related caches
            $cache_keys = [
                "post_seo_data_{$post_id}",
                "post_scores_{$post_id}",
                "post_schema_{$post_id}",
                "post_meta_tags_{$post_id}",
                "post_keywords_{$post_id}"
            ];
            
            foreach ($cache_keys as $key) {
                $this->delete_cache($key, 'posts');
            }
            
            // Clear API cache for this post
            $this->clear_post_api_cache($post_id);
            
            Logger::debug("Post cache cleared for post ID: {$post_id}");
            return true;
            
        } catch (Exception $e) {
            Logger::error("Clear post cache error for post {$post_id}: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Store value in memory cache
     */
    private function store_in_memory($cache_key, $value, $group, $expiry = null) {
        $expiry = $expiry ?: $this->get_expiry($group);
        
        $this->memory_cache[$cache_key] = [
            'data' => $value,
            'expires' => time() + $expiry,
            'group' => $group
        ];
        
        // Prevent memory cache from growing too large
        if (count($this->memory_cache) > 1000) {
            $this->cleanup_memory_cache();
        }
    }
    
    /**
     * Cleanup memory cache
     */
    private function cleanup_memory_cache() {
        $current_time = time();
        $removed = 0;
        
        foreach ($this->memory_cache as $key => $data) {
            if ($data['expires'] <= $current_time) {
                unset($this->memory_cache[$key]);
                $removed++;
            }
        }
        
        // If still too large, remove oldest entries
        if (count($this->memory_cache) > 800) {
            $sorted = $this->memory_cache;
            uasort($sorted, function($a, $b) {
                return $a['expires'] - $b['expires'];
            });
            
            $to_remove = count($sorted) - 800;
            $keys_to_remove = array_slice(array_keys($sorted), 0, $to_remove);
            
            foreach ($keys_to_remove as $key) {
                unset($this->memory_cache[$key]);
                $removed++;
            }
        }
        
        if ($removed > 0) {
            Logger::debug("Memory cache cleanup: removed {$removed} entries");
        }
    }
    
    /**
     * Build cache key
     */
    private function build_cache_key($key, $group) {
        $group_version = $this->get_group_version($group);
        return "ai_seo_ultra_{$group}_v{$group_version}_{$key}";
    }
    
    /**
     * Build transient key
     */
    private function build_transient_key($key, $group) {
        $group_version = $this->get_group_version($group);
        return "ai_seo_ultra_{$group}_v{$group_version}_{$key}";
    }
    
    /**
     * Get WordPress cache group
     */
    private function get_wp_cache_group($group) {
        return $this->cache_groups[$group] ?? 'ai_seo_ultra_default';
    }
    
    /**
     * Get cache expiry for group
     */
    private function get_expiry($group) {
        return $this->default_expiry[$group] ?? HOUR_IN_SECONDS;
    }
    
    /**
     * Get group version for cache invalidation
     */
    private function get_group_version($group) {
        $version_key = "ai_seo_ultra_cache_version_{$group}";
        $version = get_option($version_key, 1);
        return $version;
    }
    
    /**
     * Increment group version to invalidate all cache in group
     */
    private function increment_group_version($group) {
        $version_key = "ai_seo_ultra_cache_version_{$group}";
        $current_version = get_option($version_key, 1);
        update_option($version_key, $current_version + 1);
    }
    
    /**
     * Clear group transients
     */
    private function clear_group_transients($group) {
        global $wpdb;
        
        try {
            $transient_prefix = "_transient_ai_seo_ultra_{$group}_";
            $timeout_prefix = "_transient_timeout_ai_seo_ultra_{$group}_";
            
            $wpdb->query($wpdb->prepare(
                "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s OR option_name LIKE %s",
                $transient_prefix . '%',
                $timeout_prefix . '%'
            ));
            
        } catch (Exception $e) {
            Logger::error("Error clearing group transients for {$group}: " . $e->getMessage());
        }
    }
    
    /**
     * Clear API cache for specific post
     */
    private function clear_post_api_cache($post_id) {
        global $wpdb;
        
        try {
            $api_prefix = "_transient_ai_seo_ultra_api_%_post_{$post_id}_%";
            
            $wpdb->query($wpdb->prepare(
                "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
                $api_prefix
            ));
            
        } catch (Exception $e) {
            Logger::error("Error clearing API cache for post {$post_id}: " . $e->getMessage());
        }
    }
    
    /**
     * Cleanup expired cache entries
     */
    public function cleanup_expired() {
        try {
            // Cleanup memory cache
            $this->cleanup_memory_cache();
            
            // Cleanup expired transients
            $this->cleanup_expired_transients();
            
            Logger::info("Cache cleanup completed");
            
        } catch (Exception $e) {
            Logger::error("Cache cleanup error: " . $e->getMessage());
        }
    }
    
    /**
     * Cleanup expired transients
     */
    private function cleanup_expired_transients() {
        global $wpdb;
        
        try {
            // Delete expired transients
            $wpdb->query("
                DELETE a, b FROM {$wpdb->options} a, {$wpdb->options} b
                WHERE a.option_name LIKE '_transient_%'
                AND a.option_name NOT LIKE '_transient_timeout_%'
                AND b.option_name = CONCAT('_transient_timeout_', SUBSTRING(a.option_name, 12))
                AND b.option_value < UNIX_TIMESTAMP()
                AND a.option_name LIKE '_transient_ai_seo_ultra_%'
            ");
            
        } catch (Exception $e) {
            Logger::error("Error cleaning up expired transients: " . $e->getMessage());
        }
    }
    
    /**
     * Prevent cloning and unserialization
     */
    private function __clone() {}
    public function __wakeup() {
        throw new Exception('Cannot unserialize singleton');
    }
}
