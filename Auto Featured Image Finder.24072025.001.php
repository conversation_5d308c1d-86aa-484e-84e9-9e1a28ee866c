<?php
/**
 * Plugin Name: Ultra Optimized Featured Image Assigner
 * Plugin URI: https://example.com/plugins/ultra-optimized-featured-image
 * Description: Automatically assigns relevant featured images and inserts in-content images using Gemini AI
 * Version: 2.0.2
 * Author: Optimization Expert
 * Author URI: https://example.com
 * License: GPL v2 or later
 * Text Domain: ultra-optimized-featured-image
 * Requires PHP: 7.2
 * Tested up to: 6.5
 */

// Prevent direct access
if (!defined('ABSPATH')) exit;

class UltraOptimizedFeaturedImage {
    // Singleton instance
    private static $instance = null;

    // Plugin properties
    private $options;
    private $image_cache = []; // In-memory cache for image data within a request
    private $api_cache = [];   // In-memory cache for API results within a request
    private $cache_expiration = DAY_IN_SECONDS; // 24 hours transient expiration
    private $log_enabled = false;
    private $image_match_threshold = 0.65; // Threshold for AI matching confidence (if score is available)
    private $batch_size = 50;
    private $plugin_pages = [];

    // API endpoints (Ensure these are correct for the models used)
    private $gemini_api_endpoint_base = 'https://generativelanguage.googleapis.com/v1beta/models/';
    private $gemini_generation_model = 'gemini-2.0-flash-exp'; // Example generation model
    private $gemini_embedding_model = 'text-embedding-004'; // Example embedding model

    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        // Load options
        $this->load_options();

        // Define plugin pages early
        $this->plugin_pages = [
            'toplevel_page_ultra-optimized-featured-image',
            'ultra-images_page_ultra-optimized-featured-image-settings',
            'ultra-images_page_ultra-optimized-featured-image-tools'
        ];

        // Initialize hooks
        $this->init_hooks();

        // Initialize logger
        $this->init_logger();
    }

    /**
     * Load plugin options
     */
    private function load_options() {
        $default_options = array(
            'auto_assign' => true,
            'auto_insert_content_images' => true,
            'use_ai' => true,
            'api_key' => '',
            'image_sources' => array('media_library'),
            'excluded_categories' => array(),
            'included_post_types' => array('post'),
            'min_content_length' => 100,
            'max_images_per_heading' => 1, // Defaulting to 1 as per the implementation logic
            'max_images_per_post' => 6,
            'image_insertion_style' => 'single', // Defaulting to single to match max_images_per_heading=1
            'image_size' => 'medium',
            'enable_logging' => false,
            'background_processing' => true,
            'delete_duplicate_images' => false, // Default to false
        );

        $this->options = wp_parse_args(
            get_option('uofi_options', array()),
            $default_options
        );
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Admin hooks
        add_action('admin_menu', [$this, 'add_admin_menu']);
        add_action('admin_init', [$this, 'register_settings']);
        add_action('admin_enqueue_scripts', [$this, 'admin_scripts']);
        add_action('wp_dashboard_setup', [$this, 'setup_dashboard_widget']);

        // Post hooks
        add_action('save_post', [$this, 'process_post_save'], 20, 2);
        add_action('add_attachment', [$this, 'process_new_image']);

        // AJAX handlers
		add_action('wp_ajax_uofi_process_batch', [$this, 'ajax_process_batch']);
		add_action('wp_ajax_uofi_process_post', [$this, 'ajax_process_single']);
		add_action('wp_ajax_uofi_insert_content_images', [$this, 'ajax_insert_content_images']);
		add_action('wp_ajax_uofi_test_api_connection', [$this, 'ajax_test_api_connection']);
		add_action('wp_ajax_uofi_get_posts_without_images', [$this, 'ajax_get_posts_without_images']);
		add_action('wp_ajax_uofi_get_all_posts', [$this, 'ajax_get_all_posts']); // For removing duplicates
		add_action('wp_ajax_uofi_remove_duplicate_images', [$this, 'ajax_remove_duplicate_images']);  // For removing duplicates
		add_action('wp_ajax_uofi_rebuild_index', [$this, 'ajax_rebuild_index']); // For rebuilding index
		add_action('wp_ajax_uofi_clear_cache', [$this, 'ajax_clear_cache']); // For clearing cache
		add_action('wp_ajax_uofi_get_all_attachments', [$this, 'ajax_get_all_attachments']); // Add this line for fetching attachments

        // Cron jobs
        add_action('uofi_scheduled_processing', [$this, 'process_scheduled_batch']);
        add_action('uofi_process_content_images', [$this, 'process_content_images_background']);
        // Actions for single post processing scheduled from save_post
        add_action('uofi_process_single_featured', [$this, 'assign_featured_image']);
        add_action('uofi_remove_duplicates_single', [$this, 'remove_duplicate_images']);


        // REST API
        add_action('rest_api_init', [$this, 'register_rest_routes']);

        // Heartbeat optimization (Use with caution - might affect other plugins)
        // add_filter('heartbeat_settings', [$this, 'adjust_heartbeat_frequency']);

        // Automation hooks (Placeholders)
        add_action('plugins_loaded', [$this, 'init_automation_hooks']);

        // Register activation/deactivation hooks relative to __FILE__
        register_activation_hook(plugin_basename(__FILE__), [$this, 'activate']);
        register_deactivation_hook(plugin_basename(__FILE__), [$this, 'deactivate']);
    }

    /**
     * Optimize heartbeat frequency (Example - Use Carefully)
     */
    public function adjust_heartbeat_frequency($settings) {
        if (is_admin() && in_array(get_current_screen()->id, $this->plugin_pages)) {
             $settings['interval'] = 60; // Increase interval only on plugin pages
        }
        return $settings;
    }

    /**
     * Setup dashboard widget
     */
    public function setup_dashboard_widget() {
        // Optionally remove widgets - Be cautious as users might want them
        // remove_meta_box('dashboard_activity', 'dashboard', 'normal');

        wp_add_dashboard_widget(
            'uofi_performance_widget',
            __('Image Optimization Status', 'ultra-optimized-featured-image'),
            [$this, 'render_dashboard_widget']
        );
    }

    /**
     * Render dashboard widget
     */
    public function render_dashboard_widget() {
        // Use cached data for performance
        $stats = $this->get_cached_data('uofi_dashboard_stats', function() {
            $total_posts = $this->count_total_posts();
            $posts_with_featured = $this->count_posts_with_featured_image();
            $percentage = $total_posts > 0 ? round(($posts_with_featured / $total_posts) * 100) : 0;
            return [
                'total' => $total_posts,
                'with_featured' => $posts_with_featured,
                'percentage' => $percentage
            ];
        }, HOUR_IN_SECONDS); // Cache for 1 hour

        echo '<div class="uofi-dashboard-widget">';
        echo '<p><strong>' . __('Image Coverage:', 'ultra-optimized-featured-image') . '</strong> ' . $stats['percentage'] . '%</p>';
        echo '<div class="uofi-progress-bar" style="height: 8px; background: #f0f0f0; border-radius: 4px; margin-bottom: 10px; overflow: hidden;">';
        echo '<div style="width: ' . $stats['percentage'] . '%; background: #2271b1; height: 100%; border-radius: 4px;"></div>';
        echo '</div>';
        echo '<p>' . sprintf(__('%d of %d posts have featured images', 'ultra-optimized-featured-image'), $stats['with_featured'], $stats['total']) . '</p>';
        echo '<p><a href="' . admin_url('admin.php?page=ultra-optimized-featured-image') . '" class="button button-primary">' . __('Manage Images', 'ultra-optimized-featured-image') . '</a></p>';
        echo '</div>';
    }

    /**
     * Initialize automation hooks (Placeholders)
     */
    public function init_automation_hooks() {
        // Example: Hook into Uncanny Automator if available
        // if (class_exists('Uncanny_Automator\Automator_Functions')) {
        //     add_action('automator_configuration_complete', [$this, 'register_automator_triggers']);
        // }
    }

    /**
     * Register Uncanny Automator triggers (Placeholder)
     */
    public function register_automator_triggers() {
        // Implementation for Uncanny Automator triggers would go here
    }

    /**
     * Initialize logger
     */
    private function init_logger() {
        $this->log_enabled = !empty($this->options['enable_logging']);
    }

    /**
     * Plugin activation
     */
    public function activate() {
        // Create database tables if needed
        $this->create_tables();

        // Schedule cron jobs if background processing is enabled
        if (!empty($this->options['background_processing']) && !wp_next_scheduled('uofi_scheduled_processing')) {
            wp_schedule_event(time(), 'hourly', 'uofi_scheduled_processing');
        }
        // We don't schedule uofi_process_content_images here, it's scheduled per post or via bulk actions

        // Create cache directory
        $this->create_cache_directory();

        // Optionally limit post revisions - Doing this automatically is risky!
        // Consider providing instructions instead.
        // $this->add_post_revisions_limit(); // Commented out for safety

        // Clear existing transients on activation
        $this->clear_all_caches(); // Use the comprehensive clear function
    }

     /**
     * Add post revisions limit to wp-config - **Use with extreme caution!**
     * It's generally better to instruct the user to do this manually.
     */
    private function add_post_revisions_limit() {
        $this->log("Attempting to add WP_POST_REVISIONS to wp-config.php. This is potentially risky.");
        $config_file = ABSPATH . 'wp-config.php';

        // Basic check for file existence and writability
        if (!file_exists($config_file) || !is_writable($config_file)) {
             $this->log("wp-config.php not found or not writable. Cannot add WP_POST_REVISIONS automatically.");
            return;
        }

        try {
            $config_content = file_get_contents($config_file);
            if ($config_content === false) {
                $this->log("Failed to read wp-config.php.");
                return;
            }

            // Check if the constant is already defined
            if (strpos($config_content, 'WP_POST_REVISIONS') !== false) {
                 $this->log("WP_POST_REVISIONS already defined in wp-config.php.");
                return;
            }

            // Find the insertion point
            $insertion_point_marker = "/* That's all, stop editing!";
            $insertion_point = strpos($config_content, $insertion_point_marker);

            if ($insertion_point !== false) {
                $definition = "define('WP_POST_REVISIONS', 3);" . PHP_EOL . PHP_EOL;
                $modified_config = substr_replace($config_content, $definition, $insertion_point, 0);

                // Attempt to write back to the file
                if (file_put_contents($config_file, $modified_config) !== false) {
                    $this->log("Successfully added WP_POST_REVISIONS definition to wp-config.php.");
                } else {
                    $this->log("Failed to write modified content back to wp-config.php. Check file permissions.");
                }
            } else {
                $this->log("Could not find the insertion point marker '{$insertion_point_marker}' in wp-config.php.");
            }
        } catch (Exception $e) {
            $this->log("Error modifying wp-config.php: " . $e->getMessage());
        }
    }

    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clear scheduled tasks
        wp_clear_scheduled_hook('uofi_scheduled_processing');
        // Clear any single post processing hooks that might be scheduled
        wp_clear_scheduled_hook('uofi_process_content_images');
        wp_clear_scheduled_hook('uofi_process_single_featured');
        wp_clear_scheduled_hook('uofi_remove_duplicates_single');


        // Clean up transients
        $this->clean_transients(); // Only clean transients, not necessarily all caches
    }

    /**
     * Clean up plugin-specific transients
     */
    private function clean_transients() {
        global $wpdb;
        $prefix = $wpdb->prefix;
        // More specific transient deletion to avoid conflicts
        // Matches _transient_uofi_... and _transient_timeout_uofi_...
        $sql = $wpdb->prepare(
            "DELETE FROM {$prefix}options WHERE option_name LIKE %s OR option_name LIKE %s",
            $wpdb->esc_like('_transient_uofi_') . '%',
            $wpdb->esc_like('_transient_timeout_uofi_') . '%'
        );
        $deleted_rows = $wpdb->query($sql);
        $this->log("Cleaned transients: " . ($deleted_rows ?: 0) . " rows deleted.");

        // Clear WordPress object cache as well
        wp_cache_flush();
    }

     /**
     * Clear all plugin transients and caches (called via AJAX Tool)
     */
    public function clear_all_caches() {
        $this->clean_transients();
        // Optionally clear the file cache directory - use with caution!
        // $this->clear_file_cache_directory();
        $this->image_cache = []; // Clear memory cache
        $this->api_cache = [];   // Clear memory cache
        $this->log("All plugin caches cleared via Tools page.");
        return true;
    }

    /**
    * Recursively delete files and folders in the cache directory. Use carefully!
    */
    private function clear_file_cache_directory() {
        $upload_dir = wp_upload_dir();
        $cache_dir = $upload_dir['basedir'] . '/uofi-cache';

        if (!is_dir($cache_dir)) {
            $this->log("File cache directory does not exist: $cache_dir");
            return;
        }

        $this->log("Attempting to clear file cache directory: $cache_dir");
        try {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($cache_dir, FilesystemIterator::SKIP_DOTS),
                RecursiveIteratorIterator::CHILD_FIRST
            );

            foreach ($iterator as $file) {
                if ($file->isDir()) {
                    @rmdir($file->getRealPath());
                } else {
                    // Keep index.php and .htaccess
                    if ($file->getFilename() !== 'index.php' && $file->getFilename() !== '.htaccess') {
                         @unlink($file->getRealPath());
                    }
                }
            }
            $this->log("File cache directory cleared (excluding protected files).");
        } catch (Exception $e) {
             $this->log("Error clearing file cache directory: " . $e->getMessage());
        }
    }


    /**
     * Create database tables
     */
    private function create_tables() {
        global $wpdb;
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        $charset_collate = $wpdb->get_charset_collate();
        $table_name = $wpdb->prefix . 'uofi_image_vectors';
        $log_table = $wpdb->prefix . 'uofi_assignment_logs';

        // Table for image metadata and vector embeddings
        // Using TEXT for embedding as dimensions can vary. Consider LONGTEXT if embeddings are very large.
        // Indexing keywords(191) for compatibility with older MySQL/utf8mb4 setups.
        $sql_vectors = "CREATE TABLE $table_name (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            attachment_id bigint(20) unsigned NOT NULL,
            embedding longtext NULL,  -- Allow NULL if AI fails or is disabled
            keywords text NULL,       -- Allow NULL
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- Auto update timestamp
            PRIMARY KEY (id),
            UNIQUE KEY attachment_id (attachment_id),
            KEY updated_at (updated_at),
            INDEX keywords_idx (keywords(191))
        ) $charset_collate;";

        // Table for assignment logs
        $sql_logs = "CREATE TABLE $log_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            post_id bigint(20) unsigned NOT NULL,
            image_id bigint(20) unsigned NULL, -- Could fail finding image
            score float NULL,                  -- Score might not always be available
            method varchar(50) NOT NULL,
            keywords text NULL,
            status varchar(20) NOT NULL DEFAULT 'success', -- Added status (success, failed, skipped)
            message text NULL,                 -- Added message for errors/status
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY post_id (post_id),
            KEY image_id (image_id),
            KEY created_at (created_at),
            KEY method_status (method, status)
        ) $charset_collate;";

        dbDelta($sql_vectors);
        dbDelta($sql_logs);

        // Check if tables were created successfully
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
             $this->log("Failed to create/update database table: $table_name. Error: " . $wpdb->last_error);
        }
         if ($wpdb->get_var("SHOW TABLES LIKE '$log_table'") != $log_table) {
             $this->log("Failed to create/update database table: $log_table. Error: " . $wpdb->last_error);
        }
    }

    /**
     * Create cache directory
     */
    private function create_cache_directory() {
        $upload_dir = wp_upload_dir();
        $cache_dir = $upload_dir['basedir'] . '/uofi-cache';

        if (!is_dir($cache_dir)) {
            if (wp_mkdir_p($cache_dir)) {
                // Add index.php to prevent directory listing
                @file_put_contents($cache_dir . '/index.php', '<?php // Silence is golden');
                // Add .htaccess to prevent direct access
                @file_put_contents($cache_dir . '/.htaccess', 'Deny from all');
                 $this->log("Cache directory created: $cache_dir");
            } else {
                $this->log("Failed to create cache directory: $cache_dir. Please check permissions.");
                 // Optionally add an admin notice here
                 // add_action('admin_notices', function() use ($cache_dir) {
                 //     echo '<div class="notice notice-error"><p>' . sprintf(__('UOFI Plugin Warning: Could not create cache directory at %s. Please check folder permissions.', 'ultra-optimized-featured-image'), esc_html($cache_dir)) . '</p></div>';
                 // });
            }
        }
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            __('Ultra Optimized Images', 'ultra-optimized-featured-image'),
            __('Ultra Images', 'ultra-optimized-featured-image'),
            'manage_options',
            'ultra-optimized-featured-image', // Main slug
            [$this, 'render_admin_page'],
            'dashicons-format-gallery',
            30
        );

        // Settings Submenu
        add_submenu_page(
            'ultra-optimized-featured-image', // Parent slug
            __('Settings', 'ultra-optimized-featured-image'),
            __('Settings', 'ultra-optimized-featured-image'),
            'manage_options',
            'ultra-optimized-featured-image-settings', // Submenu slug
            [$this, 'render_settings_page']
        );

        // Tools Submenu
        add_submenu_page(
            'ultra-optimized-featured-image', // Parent slug
            __('Tools', 'ultra-optimized-featured-image'),
            __('Tools', 'ultra-optimized-featured-image'),
            'manage_options',
            'ultra-optimized-featured-image-tools', // Submenu slug
            [$this, 'render_tools_page']
        );
    }

    /**
     * Register settings
     */
    public function register_settings() {
        register_setting('uofi_options_group', 'uofi_options', [$this, 'sanitize_options']);
    }

    /**
     * Sanitize options before saving.
     */
    public function sanitize_options($options) {
        $sanitized_options = $this->options; // Start with existing options as default

        // Sanitize API key
        if (isset($options['api_key'])) {
            $sanitized_options['api_key'] = sanitize_text_field(trim($options['api_key']));
        }

        // Ensure booleans
        $boolean_options = [
            'auto_assign', 'auto_insert_content_images', 'use_ai',
            'enable_logging', 'background_processing', 'delete_duplicate_images'
        ];
        foreach ($boolean_options as $option) {
            $sanitized_options[$option] = !empty($options[$option]);
        }

        // Ensure arrays
        $array_options = ['image_sources', 'excluded_categories', 'included_post_types'];
        foreach ($array_options as $option) {
            if (isset($options[$option]) && is_array($options[$option])) {
                 // Use sanitize_text_field for string arrays, absint for ID arrays
                 if ($option === 'excluded_categories') {
                     $sanitized_options[$option] = array_map('absint', $options[$option]);
                 } else {
                     $sanitized_options[$option] = array_map('sanitize_key', $options[$option]); // Use sanitize_key for slugs/post types
                 }
                 $sanitized_options[$option] = array_filter($sanitized_options[$option]); // Remove empty values
            } else {
                $sanitized_options[$option] = [];
            }
        }

        // Numeric options
        $numeric_options = [
            'min_content_length' => 100,
            'max_images_per_heading' => 1,
            'max_images_per_post' => 6,
        ];
        foreach ($numeric_options as $option => $default) {
             if (isset($options[$option])) {
                 $sanitized_options[$option] = absint($options[$option]);
                 // Apply reasonable bounds
                 if ($option === 'min_content_length') $sanitized_options[$option] = max(0, $sanitized_options[$option]);
                 if ($option === 'max_images_per_heading') $sanitized_options[$option] = max(1, min(10, $sanitized_options[$option]));
                 if ($option === 'max_images_per_post') $sanitized_options[$option] = max(1, min(20, $sanitized_options[$option]));
             } else {
                 $sanitized_options[$option] = $default;
             }
        }

        // Select options
        $select_options = [
            'image_insertion_style' => ['single', 'grid', 'carousel'],
            'image_size' => array_merge(['full'], get_intermediate_image_sizes()), // Include 'full' size
        ];
        foreach ($select_options as $option => $allowed_values) {
            if (isset($options[$option]) && in_array($options[$option], $allowed_values)) {
                $sanitized_options[$option] = sanitize_key($options[$option]); // Use sanitize_key for these values
            }
            // else keep the default or previously saved value if input is invalid
        }

        // If max images per heading is > 1, grid or carousel makes more sense than single
        if ($sanitized_options['max_images_per_heading'] > 1 && $sanitized_options['image_insertion_style'] === 'single') {
            $sanitized_options['image_insertion_style'] = 'grid'; // Default to grid if multiple images per heading
        }
        // If max images per heading is 1, force style to single
        if ($sanitized_options['max_images_per_heading'] === 1) {
             $sanitized_options['image_insertion_style'] = 'single';
        }


        // Update the class property immediately after saving
        $this->options = $sanitized_options;
        $this->init_logger(); // Re-initialize logger based on new setting

        // Reschedule cron if background processing setting changed
        if (isset($options['background_processing'])) {
            $old_background_setting = $this->options['background_processing']; // Get old value before overriding
            $new_background_setting = $sanitized_options['background_processing'];
            if ($old_background_setting != $new_background_setting) {
                 if ($new_background_setting && !wp_next_scheduled('uofi_scheduled_processing')) {
                      wp_schedule_event(time(), 'hourly', 'uofi_scheduled_processing');
                      $this->log("Background processing enabled and scheduled.");
                 } elseif (!$new_background_setting) {
                      wp_clear_scheduled_hook('uofi_scheduled_processing');
                      $this->log("Background processing disabled and unscheduled.");
                 }
            }
        }


        $this->log("Plugin settings saved and sanitized.");
        return $sanitized_options;
    }

    /**
     * Get cached data using WordPress transients.
     * Optimization: Uses transients, which leverage WP's object cache. Handles 'false' results.
     */
    private function get_cached_data($key, $callback, $expiration = 0) {
        // Use a shorter expiration if not provided, fallback to default class property
        $expiration = $expiration ?: $this->cache_expiration;
        $cache_key = 'uofi_' . substr(md5($key), 0, 20); // Slightly longer key, still reasonable
        $cached = get_transient($cache_key);

        if ($cached !== false) {
            // Check if it's our placeholder for "no data found" or intentional false
             if ($cached === '__UOFINODATA__') {
                return false;
             }
             // Check for placeholder for empty array
             if ($cached === '__UOFINULLARRAY__') {
                 return [];
             }
            return $cached;
        }

        $data = $callback();

        // Store the result in the transient
        // Store a placeholder if the callback returned false/empty to prevent repeated computation
        $value_to_store = $data;
        if ($data === false) {
            $value_to_store = '__UOFINODATA__';
        } elseif ($data === []) {
            $value_to_store = '__UOFINULLARRAY__';
        }

        set_transient($cache_key, $value_to_store, $expiration);

        return $data;
    }


    /**
     * Enqueue admin scripts and styles conditionally.
     * Optimization: Loads assets only on necessary pages. Uses defer.
     */
    public function admin_scripts($hook) {
        // Only load on plugin's admin pages
        if (!in_array($hook, $this->plugin_pages)) {
             return;
        }

        $plugin_version = '2.0.2'; // Keep version consistent

        // Define relative paths
        $css_rel_path = file_exists(plugin_dir_path(__FILE__) . 'assets/css/admin.min.css') ? 'assets/css/admin.min.css' : 'assets/css/admin.css';
        $js_rel_path = file_exists(plugin_dir_path(__FILE__) . 'assets/js/admin.min.js') ? 'assets/js/admin.min.js' : 'assets/js/admin.js';

        // Use plugin_dir_url for correct URL generation
        $plugin_base_url = plugin_dir_url(__FILE__); // Base URL of the plugin directory

        wp_enqueue_style(
            'uofi-admin-styles',
            $plugin_base_url . $css_rel_path, // Correct URL generation
            [],
            $plugin_version
        );

        wp_enqueue_script(
            'uofi-admin-script',
            $plugin_base_url . $js_rel_path, // Correct URL generation
            ['jquery', 'wp-util'], // wp-util is useful for templates
            $plugin_version,
            true // Load in footer
        );

        // Add defer attribute to the script tag
        add_filter('script_loader_tag', [$this, 'add_defer_attribute'], 10, 2);

        wp_localize_script('uofi-admin-script', 'uofiData', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('uofi_admin_nonce'), // Nonce action name matches check_ajax_referer
            'processing' => __('Processing...', 'ultra-optimized-featured-image'),
            'completed' => __('Completed!', 'ultra-optimized-featured-image'),
            'error' => __('Error:', 'ultra-optimized-featured-image'),
            'confirm_clear_cache' => __('Are you sure you want to clear all plugin caches (transients, memory cache)? This cannot be undone.', 'ultra-optimized-featured-image'),
            'confirm_rebuild_index' => __('Are you sure you want to rebuild the image index? This might take a while and consume API credits if AI is enabled.', 'ultra-optimized-featured-image'),
            'confirm_remove_duplicates' => __('Are you sure you want to scan ALL posts and remove duplicate images based on image source? This cannot be undone.', 'ultra-optimized-featured-image'),
            'generic_error' => __('An unexpected error occurred. Please check the logs or try again.', 'ultra-optimized-featured-image'),
            // Add specific nonces here if needed in the future, inside the array:
            // 'rebuildNonce' => wp_create_nonce('uofi_rebuild_nonce_action'),
            // 'clearCacheNonce' => wp_create_nonce('uofi_clear_cache_nonce_action'),
        ]);
    } // End of admin_scripts function

    /**
     * Add defer attribute to script tag.
     */
    public function add_defer_attribute($tag, $handle) {
        if ('uofi-admin-script' === $handle) {
            // Ensure defer is added correctly
            if (strpos($tag, ' defer') === false) {
                 $tag = str_replace(' src=', ' defer src=', $tag);
            }
        }
        return $tag;
    }


    /**
     * Process post save hook. Schedules tasks for background processing if enabled.
     */
    public function process_post_save($post_id, $post) {
        // Basic checks
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) return;
        if (!isset($post->post_status) || $post->post_status === 'auto-draft') return;
        if (wp_is_post_revision($post_id) || wp_is_post_autosave($post_id)) return;
        if (!in_array($post->post_type, $this->options['included_post_types'])) return;

        // Check if post is published or scheduled to be published
        if (!in_array($post->post_status, ['publish', 'future'])) {
            return;
        }

        // Check excluded categories
        if (!empty($this->options['excluded_categories'])) {
             $post_categories = wp_get_post_categories($post_id);
             if (!empty(array_intersect($post_categories, $this->options['excluded_categories']))) {
                 $this->log("Skipping post {$post_id} processing on save: Excluded category found.");
                 return;
             }
        }

        // --- Featured Image Assignment ---
        if ($this->options['auto_assign'] && !has_post_thumbnail($post_id)) {
             if ($this->options['background_processing']) {
                 if (!wp_next_scheduled('uofi_process_single_featured', [$post_id])) {
                     wp_schedule_single_event(time() + 10, 'uofi_process_single_featured', [$post_id]);
                     $this->log("Scheduled background featured image assignment for post ID: $post_id");
                 }
             } else {
                 $this->assign_featured_image($post_id);
             }
        }

        // --- In-Content Image Insertion ---
        if ($this->options['auto_insert_content_images']) {
            // Check if content already has plugin images before scheduling/running
            $current_content = get_post_field('post_content', $post_id);
            if (strpos($current_content, 'class="uofi-') === false && !$this->content_has_general_images($current_content)) {
                 if ($this->options['background_processing']) {
                     if (!wp_next_scheduled('uofi_process_content_images', [$post_id])) {
                         wp_schedule_single_event(time() + 20, 'uofi_process_content_images', [$post_id]); // Slightly delayed
                         $this->log("Scheduled background content image insertion for post ID: $post_id");
                     }
                 } else {
                     $this->insert_content_images($post_id);
                 }
            } else {
                 $this->log("Skipping content image insertion for post {$post_id} on save: Images likely already present.");
            }
        }

         // --- Duplicate Image Removal ---
        if ($this->options['delete_duplicate_images']) {
             if ($this->options['background_processing']) {
                 if (!wp_next_scheduled('uofi_remove_duplicates_single', [$post_id])) {
                     wp_schedule_single_event(time() + 30, 'uofi_remove_duplicates_single', [$post_id]); // Delayed further
                     $this->log("Scheduled background duplicate image removal for post ID: $post_id");
                 }
             } else {
                 $this->remove_duplicate_images($post_id);
             }
        }
    }

    /**
     * Process new image upload - Index it.
     */
    public function process_new_image($attachment_id) {
        // Ensure it's an image
        if (!wp_attachment_is_image($attachment_id)) {
            return;
        }
        $this->log("New image detected (ID: $attachment_id). Queuing for indexing.");
        // Index the image (can be done immediately or scheduled if indexing is slow)
        // Immediate indexing is usually fine unless the API call is very slow.
        $this->index_image($attachment_id);
    }

    /**
     * Index image for AI matching with optimized queries and error handling.
     * Optimization: Uses prepare, ON DUPLICATE KEY UPDATE. Caching via get_cached_data.
     * @return bool True on success/update, False on failure.
     */
    private function index_image($attachment_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'uofi_image_vectors';

        // Use caching to avoid re-indexing recently processed images frequently
        // Make cache key specific and shorter
        $cache_key_base = "index_img_{$attachment_id}";
        return $this->get_cached_data($cache_key_base, function() use ($attachment_id, $wpdb, $table_name) {
            $image = get_post($attachment_id);
            if (!$image || $image->post_type !== 'attachment') {
                $this->log("Indexing failed: Invalid attachment ID {$attachment_id}.");
                return false;
            }

            // Gather text associated with the image
            $alt_text = get_post_meta($attachment_id, '_wp_attachment_image_alt', true);
            $file_path = get_attached_file($attachment_id);
            $filename = $file_path ? basename($file_path) : '';

            $image_text_parts = [
                $image->post_title,
                $image->post_content, // Caption
                $image->post_excerpt, // Description
                $alt_text,
                // Removing filename unless it's confirmed to be useful, often just numbers/gibberish
                // $filename
            ];
            // Clean up filename parts if included
            $clean_filename = pathinfo($filename, PATHINFO_FILENAME); // Get name without extension
            $clean_filename = preg_replace('/[\-_]/', ' ', $clean_filename); // Replace separators with space
            $image_text_parts[] = $clean_filename;

            $image_text = implode(' ', array_filter($image_text_parts)); // Combine non-empty parts

            if (empty(trim($image_text))) {
                $this->log("Indexing skipped: No significant text data found for image {$attachment_id}.");
                 // Ensure any old entry is removed if no text data
                 $wpdb->delete($table_name, ['attachment_id' => $attachment_id], ['%d']);
                 return false;
            }

            // Extract keywords
            $keywords = $this->extract_keywords($image_text);
            $keywords_str = !empty($keywords) ? implode(',', $keywords) : null;

            // Generate embedding if AI is enabled and API key is present
            $embedding_json = null;
            if ($this->options['use_ai'] && !empty($this->options['api_key'])) {
                $embedding_vector = $this->generate_embedding($image_text);
                if (!empty($embedding_vector)) {
                    $embedding_json = json_encode($embedding_vector);
                    if ($embedding_json === false) {
                        $this->log("Indexing error: Failed to encode embedding to JSON for image {$attachment_id}. Error: " . json_last_error_msg());
                        $embedding_json = null; // Prevent saving invalid JSON
                    }
                } else {
                     $this->log("Indexing notice: Failed to generate embedding for image {$attachment_id}.");
                     // Should we store NULL or keep the old value? Let's store NULL.
                }
            }

            // Store in database using prepared statement and ON DUPLICATE KEY UPDATE
            // Note: ON DUPLICATE KEY UPDATE uses VALUES() which refers to the values that *would* have been inserted.
            $sql = $wpdb->prepare(
                "INSERT INTO $table_name (attachment_id, embedding, keywords, updated_at)
                 VALUES (%d, %s, %s, %s)
                 ON DUPLICATE KEY UPDATE
                 embedding = VALUES(embedding),
                 keywords = VALUES(keywords),
                 updated_at = VALUES(updated_at)",
                $attachment_id,
                $embedding_json, // Can be NULL
                $keywords_str,   // Can be NULL
                current_time('mysql', 1) // GMT time
            );
            $result = $wpdb->query($sql);

            if ($result === false) {
                $this->log("Indexing DB error for image {$attachment_id}: " . $wpdb->last_error);
                return false;
            }

            // $result is number of rows affected: 1 for INSERT, 2 for UPDATE, 0 for no change
            $action = ($result === 1) ? 'indexed' : 'updated';
            $this->log("Image {$attachment_id} {$action} successfully.");
            return true;

        }, HOUR_IN_SECONDS); // Cache result for 1 hour to prevent rapid re-indexing
    }


    /**
     * Generate text embedding using Gemini API.
     * Optimization: Uses caching (memory + transient). Handles errors.
     * @return array|false Embedding vector on success, false on failure.
     */
    private function generate_embedding($text) {
         // Basic check
         if (empty(trim($text))) return false;

         $cache_key = "embedding_" . md5($text);

         // Check memory cache first
         if (isset($this->api_cache[$cache_key])) {
             return $this->api_cache[$cache_key];
         }

         // Use persistent cache (transient)
         return $this->get_cached_data($cache_key, function() use ($text, $cache_key) {
             if (empty($this->options['api_key'])) {
                 $this->log("Embedding generation skipped: API key not set.");
                 return false;
             }

             // Limit text length (Gemini limits vary, check documentation for the specific model)
             $max_length = 2000; // Conservative limit for many models
             $truncated_text = mb_substr(trim($text), 0, $max_length, 'UTF-8');

             // Construct endpoint URL for the embedding model
             $endpoint = $this->gemini_api_endpoint_base . $this->gemini_embedding_model . ':embedContent';
             $api_key = $this->options['api_key'];

             $request_body = [
                 'content' => [
                     'parts' => [['text' => $truncated_text]]
                 ],
                 // Optionally specify task type if the model supports it and it improves results
                 // 'taskType' => 'RETRIEVAL_DOCUMENT' // Example: if using for similarity search
             ];

             $response = wp_remote_post("{$endpoint}?key={$api_key}", [
                 'headers' => ['Content-Type' => 'application/json'],
                 'timeout' => 20, // Timeout for API calls
                 'body' => json_encode($request_body),
                 'sslverify' => apply_filters('https_local_ssl_verify', true), // Standard WP SSL verification
             ]);

             // Handle WP connection errors
             if (is_wp_error($response)) {
                 $this->log("Embedding API connection error: " . $response->get_error_message());
                 $this->api_cache[$cache_key] = false; // Cache failure in memory
                 return false;
             }

             // Handle API HTTP errors
             $status_code = wp_remote_retrieve_response_code($response);
             $body = wp_remote_retrieve_body($response);

             if ($status_code !== 200) {
                 $error_message = "HTTP Error: $status_code";
                 $result = json_decode($body, true);
                 if (isset($result['error']['message'])) {
                     $error_message .= " - " . $result['error']['message'];
                 }
                 $this->log("Embedding API error: $error_message (Text: " . substr($truncated_text, 0, 50) . "...)");
                 $this->api_cache[$cache_key] = false; // Cache failure in memory
                 return false;
             }

             // Parse successful response
             $result = json_decode($body, true);
             if (isset($result['embedding']['values']) && is_array($result['embedding']['values'])) {
                 $this->log("Embedding generated successfully for text: " . substr($truncated_text, 0, 50) . "...");
                 $this->api_cache[$cache_key] = $result['embedding']['values']; // Cache success in memory
                 return $result['embedding']['values'];
             } else {
                 $this->log("Embedding API error: Unexpected response format. Body: " . $body);
                 $this->api_cache[$cache_key] = false; // Cache failure in memory
                 return false;
             }
         }, $this->cache_expiration * 7); // Cache embeddings longer (e.g., 7 days)
    }


    /**
     * Extract keywords from text with optimization.
     * Optimization: Uses caching, efficient string/array functions, static stopwords.
     * @return array Array of keywords.
     */
    private function extract_keywords($text) {
        if (empty(trim($text))) return [];

        $cache_key = "keywords_" . md5($text);

        return $this->get_cached_data($cache_key, function() use ($text) {
            // Normalize text: lowercase, remove punctuation (allow numbers)
            $normalized_text = mb_strtolower(trim($text), 'UTF-8');
            // Keep letters, numbers, and whitespace. Remove others.
            $normalized_text = preg_replace('/[^\p{L}\p{N}\s]/u', ' ', $normalized_text);
             // Replace multiple spaces with single space
            $normalized_text = preg_replace('/\s+/', ' ', $normalized_text);

            // Split into words
            $words = explode(' ', $normalized_text);

            // Remove stopwords and short words/numbers
            $stopwords = $this->get_stopwords();
            $filtered_words = [];
            foreach ($words as $word) {
                // Use mb_strlen for multibyte characters
                // Skip purely numeric 'words' unless specifically desired
                if (!is_numeric($word) && mb_strlen($word, 'UTF-8') > 2 && !in_array($word, $stopwords)) {
                    $filtered_words[] = $word;
                }
            }

            if (empty($filtered_words)) {
                return [];
            }

            // Count word frequencies
            $word_counts = array_count_values($filtered_words);

            // Sort by frequency (highest first)
            arsort($word_counts);

            // Return top keywords (limit to e.g., 20)
            return array_slice(array_keys($word_counts), 0, 20);
        }, $this->cache_expiration);
    }

    /**
     * Get stopwords list.
     * Optimization: Uses static variable for caching within request. Filterable.
     */
    private function get_stopwords() {
        static $stopwords = null;
        if ($stopwords === null) {
            // Basic English stopwords list - consider making this language-specific or customizable
            $stopwords_list = [
                'a', 'about', 'above', 'after', 'again', 'against', 'all', 'am', 'an', 'and', 'any', 'are', 'as', 'at',
                'be', 'because', 'been', 'before', 'being', 'below', 'between', 'both', 'but', 'by', 'can', 'did', 'do',
                'does', 'doing', 'down', 'during', 'each', 'few', 'for', 'from', 'further', 'had', 'has', 'have', 'having',
                'he', 'her', 'here', 'hers', 'herself', 'him', 'himself', 'his', 'how', 'i', 'if', 'in', 'into', 'is',
                'it', 'its', 'itself', 'just', 'me', 'more', 'most', 'my', 'myself', 'no', 'nor', 'not', 'now', 'of',
                'off', 'on', 'once', 'only', 'or', 'other', 'our', 'ours', 'ourselves', 'out', 'over', 'own', 's', 'same',
                'she', 'should', 'so', 'some', 'such', 't', 'than', 'that', 'the', 'their', 'theirs', 'them', 'themselves',
                'then', 'there', 'these', 'they', 'this', 'those', 'through', 'to', 'too', 'under', 'until', 'up', 'very',
                'was', 'we', 'were', 'what', 'when', 'where', 'which', 'while', 'who', 'whom', 'why', 'will', 'with',
                'would', 'you', 'your', 'yours', 'yourself', 'yourselves',
                // Added common WP/web terms that might not be useful keywords
                'wp', 'content', 'uploads', 'image', 'post', 'page', 'click', 'view', 'read', 'more', 'comment', 'reply', 'edit',
                'jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec', 'january', 'february', 'march', 'april', 'june', 'july', 'august', 'september', 'october', 'november', 'december',
                // Numbers as words (less common but possible)
                'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine', 'ten'
             ];
             // Apply filter to allow customization
             $stopwords = apply_filters('uofi_stopwords', $stopwords_list);
             // Ensure it's an array
             $stopwords = is_array($stopwords) ? $stopwords : $stopwords_list;
        }
        return $stopwords;
    }

    /**
     * Assign featured image to post with improved matching and error logging.
     * Optimization: Uses caching.
     * @param int $post_id Post ID.
     * @return bool True if image assigned, False otherwise.
     */
    public function assign_featured_image($post_id) {
        // Re-check if already has featured image (might have been set by another process/user)
        if (has_post_thumbnail($post_id)) {
             $this->log("Assign featured (Post {$post_id}): Skipped, already has a featured image.");
             // Optionally log this skip action if detailed logging is needed
             // $this->log_assignment($post_id, get_post_thumbnail_id($post_id), [], 'skipped', 'Already has featured image');
            return false;
        }

        // Use caching for the assignment process itself to prevent retrying failed assignments too quickly
        $cache_key_base = "assign_feat_{$post_id}";
        return $this->get_cached_data($cache_key_base, function() use ($post_id) {
            $post = get_post($post_id);
            // Ensure post is valid and publishable
            if (!$post || !in_array($post->post_status, ['publish', 'future'])) {
                $this->log("Assign featured (Post {$post_id}): Invalid or non-published post object.");
                // Don't log assignment failure for invalid posts
                return false;
            }

            // Check content length requirement
            $content = strip_tags($post->post_content); // Use stripped content for length check
            if (mb_strlen($content, 'UTF-8') < $this->options['min_content_length']) {
                $this->log("Assign featured (Post {$post_id}): Content too short (Length: " . mb_strlen($content, 'UTF-8') . ", Min: {$this->options['min_content_length']}).");
                 $this->log_assignment($post_id, null, [], 'skipped', 'Content too short');
                return false;
            }

            // Extract keywords from the post
            $keywords = $this->extract_post_keywords($post);
            if (empty($keywords)) {
                $this->log("Assign featured (Post {$post_id}): No keywords extracted. Cannot find relevant image.");
                 $this->log_assignment($post_id, null, [], 'failed', 'No keywords extracted');
                return false;
            }
             $keywords_str = implode(', ', $keywords);

            // Find the best matching image ID
            $method_used = ($this->options['use_ai'] && !empty($this->options['api_key'])) ? 'ai' : 'keywords';
            $image_id = $this->find_matching_image($keywords, $post);

            if (!$image_id) {
                $this->log("Assign featured (Post {$post_id}): No suitable matching image found using {$method_used} (Keywords: {$keywords_str}).");
                 $this->log_assignment($post_id, null, $keywords, 'failed', "No matching image found ({$method_used})");
                return false;
            }

            // Verify the image ID is valid before setting
            if (!wp_attachment_is_image($image_id)) {
                 $this->log("Assign featured (Post {$post_id}): Found ID {$image_id} but it's not a valid image attachment.");
                 $this->log_assignment($post_id, $image_id, $keywords, 'failed', "Found invalid image ID ({$method_used})");
                 return false;
            }

            // Set the found image as the featured image
            if (set_post_thumbnail($post_id, $image_id)) {
                $this->log("Assigned featured image ID {$image_id} to post ID {$post_id} using {$method_used}.");
                $this->log_assignment($post_id, $image_id, $keywords, 'success', "Assigned via {$method_used}");
                // Clear specific caches related to this post's thumbnail status
                delete_post_meta($post_id, '_thumbnail_id'); // Ensure meta cache is cleared if needed
                update_post_meta($post_id, '_thumbnail_id', $image_id); // Update directly to possibly trigger actions
                wp_cache_delete('post_' . $post_id, 'posts');
                wp_cache_delete('post_meta_' . $post_id, 'post_meta');
                return true;
            } else {
                 $this->log("Assign featured (Post {$post_id}): Failed to set post thumbnail using set_post_thumbnail() for image {$image_id}.");
                 $this->log_assignment($post_id, $image_id, $keywords, 'failed', 'set_post_thumbnail failed');
                 return false;
            }
        }, 600); // Cache assignment result/failure for 10 minutes
    }

    /**
     * Extract keywords from post (title, content, excerpt, terms).
     * Optimization: Uses caching. Weights terms.
     * @param WP_Post $post Post object.
     * @return array Keywords.
     */
    private function extract_post_keywords($post) {
         if (!$post instanceof WP_Post) return [];

        $cache_key = "post_keywords_{$post->ID}";
        return $this->get_cached_data($cache_key, function() use ($post) {
            // Get post data, ensure content is stripped
            $text_parts = [
                $post->post_title,
                strip_tags($post->post_content),
                strip_tags($post->post_excerpt)
            ];

            // Get categories and tags names
             $term_names = [];
             $taxonomies = get_object_taxonomies($post->post_type);
             // Focus on common taxonomies unless configured otherwise
             $relevant_taxonomies = array_intersect(['category', 'post_tag'], $taxonomies);
             if (!empty($relevant_taxonomies)) {
                 $terms = wp_get_object_terms($post->ID, $relevant_taxonomies, ['fields' => 'names']);
                 if (!is_wp_error($terms) && !empty($terms)) {
                      $term_names = $terms;
                 }
             }

            // Add term names to text, repeating them for higher weight (simple weighting)
            $weighted_terms = [];
            foreach ($term_names as $term_name) {
                 // Repeat 3 times for emphasis
                $weighted_terms[] = $term_name;
                $weighted_terms[] = $term_name;
                $weighted_terms[] = $term_name;
            }
            $text_parts = array_merge($text_parts, $weighted_terms);

            // Combine all text parts
            $full_text = implode(' ', array_filter($text_parts));

            // Extract keywords from combined text
            return $this->extract_keywords($full_text);
        }, HOUR_IN_SECONDS); // Cache post keywords for 1 hour
    }

    /**
     * Find matching image for post - Chooses AI or Keyword method.
     * @param array $keywords Post keywords.
     * @param WP_Post $post Post object.
     * @return int|false Image ID or false.
     */
    private function find_matching_image($keywords, $post) {
        // Use AI matching if enabled, API key available, and keywords found
        if ($this->options['use_ai'] && !empty($this->options['api_key']) && !empty($keywords)) {
             $this->log("Attempting AI image match for post {$post->ID}.");
             $ai_image_id = $this->find_image_with_ai($keywords, $post);
             if ($ai_image_id) {
                 return $ai_image_id; // Return AI result if successful
             }
             $this->log("AI match failed or found no suitable image for post {$post->ID}, falling back to keyword search.");
        }

        // Fallback to keyword-based matching
        $this->log("Attempting keyword image match for post {$post->ID}.");
        return $this->find_image_with_keywords($keywords);
    }


    /**
     * Find image using AI matching (calling Gemini).
     * Optimization: Uses caching. Includes fallback check logic.
     * @param array $keywords Post keywords.
     * @param WP_Post $post Post object.
     * @return int|false Image ID or false.
     */
    private function find_image_with_ai($keywords, $post) {
        // Use cached results for this specific post + keywords combination
         $cache_key = "ai_match_{$post->ID}_" . md5(implode(',', $keywords));

         return $this->get_cached_data($cache_key, function() use ($keywords, $post) {
             // Get candidate images based on keywords (limit for efficiency)
             // Increase candidate pool slightly for better AI selection
             $candidate_images = $this->get_candidate_images($keywords, 30); // Get up to 30 candidates

             if (empty($candidate_images)) {
                 $this->log("AI Match (Post {$post->ID}): No candidate images found based on keywords.");
                 return false; // No candidates, AI can't choose
             }

             // Ensure candidates are unique by ID
             $candidate_images = array_values(array_unique($candidate_images, SORT_REGULAR));

            // Create prompt for Gemini generation model
             $prompt = $this->build_image_match_prompt($keywords, $post, $candidate_images);

             // Process with Gemini generation API
             $api_result = $this->process_with_gemini($prompt);

             // Check for API errors
             if (isset($api_result['error'])) {
                 $this->log("AI Match (Post {$post->ID}): Gemini API error: " . $api_result['error']);
                 // Don't return false immediately, allow parsing attempt below in case error was minor or intermittent
             }

             // Parse the API response to get the best image ID
             $best_image_id = null;
             if (!empty($api_result['best_match_id'])) {
                 $best_image_id = absint($api_result['best_match_id']);
             } elseif (isset($api_result['text'])) {
                 // If JSON failed, try parsing the raw text as a fallback
                 $parsed_fallback = $this->parse_gemini_text_response($api_result['text']);
                 if (!empty($parsed_fallback['best_match_id'])) {
                     $best_image_id = absint($parsed_fallback['best_match_id']);
                      $this->log("AI Match (Post {$post->ID}): Parsed ID {$best_image_id} from text response fallback.");
                 }
             }

             // Validate the chosen ID
             if ($best_image_id) {
                 // Verify the ID exists in the candidates provided to the AI
                 $found = false;
                 foreach ($candidate_images as $img) {
                     if ($img['id'] == $best_image_id) {
                         $found = true;
                         break;
                     }
                 }
                 if ($found && wp_attachment_is_image($best_image_id)) { // Double check it's still a valid image
                      $confidence = $api_result['confidence'] ?? 'N/A';
                      $this->log("AI Match (Post {$post->ID}): Found and validated best match ID: {$best_image_id} (Confidence: {$confidence}).");
                      return $best_image_id;
                 } else {
                      $this->log("AI Match (Post {$post->ID}): Gemini returned ID {$best_image_id}, but it wasn't in the valid candidate list or is not an image anymore.");
                      // Fallthrough to return false
                 }
             } else {
                 $response_summary = isset($api_result['text']) ? substr($api_result['text'], 0, 100) . '...' : (isset($api_result['error']) ? $api_result['error'] : 'No response');
                 $this->log("AI Match (Post {$post->ID}): Gemini response did not yield a valid best match ID. Response summary: " . $response_summary);
             }

             // If AI fails to provide a valid ID, return false (caller handles fallback)
             return false;

         }, $this->cache_expiration); // Cache AI match result for a day
    }


    /**
     * Build prompt for image matching using Gemini generation model.
     * Provides context and candidate images, asks for JSON output.
     */
    private function build_image_match_prompt($keywords, $post, $candidate_images) {
        $title = $post->post_title;
        // Use mb_substr for safe truncation
        $content_summary = mb_substr(strip_tags($post->post_content), 0, 500, 'UTF-8') . '...';
        $keywords_text = implode(', ', $keywords);

        $prompt = "You are an expert WordPress featured image selector.\n";
        $prompt .= "Task: Select the *single best* featured image for the following blog post.\n\n";
        $prompt .= "## Blog Post Details:\n";
        $prompt .= "**Title:** " . $title . "\n";
        $prompt .= "**Content Summary:** " . $content_summary . "\n";
        $prompt .= "**Main Keywords:** " . $keywords_text . "\n\n";
        $prompt .= "## Available Candidate Images (Choose ONE):\n";
        $prompt .= "Consider relevance to the title, summary, and keywords. Choose the most visually representative image.\n\n";

        foreach ($candidate_images as $index => $image) {
            $prompt .= ($index + 1) . ". **Image ID:** " . $image['id'] . "\n";
            $prompt .= "   - **Title:** " . ($image['title'] ?: 'N/A') . "\n";
            $prompt .= "   - **Alt Text:** " . ($image['alt_text'] ?: 'N/A') . "\n";
            // $prompt .= "   - Caption: " . ($image['caption'] ?: 'N/A') . "\n"; // Usually less critical for featured image
            $prompt .= "   - **Filename:** " . ($image['filename'] ?: 'N/A') . "\n\n";
        }

        $prompt .= "## Instructions:\n";
        $prompt .= "1. Analyze the post details and the information for each candidate image.\n";
        $prompt .= "2. Determine which image (by its ID) is the **single most relevant and visually appropriate** choice for the featured image of this specific post.\n";
        $prompt .= "3. Respond **only** with a JSON object containing the ID of your chosen image. Example format:\n";
        $prompt .= "   ```json\n";
        $prompt .= "   {\"best_match_id\": CHOSEN_IMAGE_ID}\n";
        $prompt .= "   ```\n";
        $prompt .= "   (Replace CHOSEN_IMAGE_ID with the actual number, e.g., {\"best_match_id\": 123}).\n";
        $prompt .= "4. **Do not** include any other text, explanation, markdown formatting, or introductory phrases outside the required JSON structure.";

        return $prompt;
    }


    /**
     * Get candidate images based on keywords.
     * Optimization: Uses caching, optimized SQL query with JOINs and prepare. Attempts to get diverse results.
     * @param array $keywords Keywords to search for.
     * @param int $limit Max number of candidates to return.
     * @return array Formatted image data arrays.
     */
    private function get_candidate_images($keywords, $limit = 30) {
        global $wpdb;
        $cache_key = "candidate_images_" . md5(implode(',', $keywords)) . "_limit" . $limit;

        return $this->get_cached_data($cache_key, function() use ($keywords, $limit, $wpdb) {
            $vector_table = $wpdb->prefix . 'uofi_image_vectors';
            $params = [];
            $keyword_conditions = [];

            // Build conditions for keyword filtering using our index table first
             foreach ($keywords as $keyword) {
                 if (mb_strlen($keyword, 'UTF-8') < 3) continue;
                 $keyword_conditions[] = "v.keywords LIKE %s";
                 $params[] = '%' . $wpdb->esc_like($keyword) . '%';
             }

            if (empty($keyword_conditions)) {
                 $this->log("Candidate Images: No valid keywords provided for indexed search.");
                 // Optionally, proceed to broader search immediately or return empty
                 // Let's try broader search as fallback below.
            }

             // Query 1: Prioritize images with matching keywords in our index
             $query1 = "";
             if (!empty($keyword_conditions)) {
                 $query1 = "
                     (SELECT DISTINCT v.attachment_id as ID, v.updated_at, 1 as priority
                      FROM {$vector_table} v
                      INNER JOIN {$wpdb->posts} p ON v.attachment_id = p.ID
                      WHERE p.post_type = 'attachment'
                      AND p.post_status = 'inherit'
                      AND p.post_mime_type LIKE 'image/%'
                      AND (" . implode(" OR ", $keyword_conditions) . ")
                     )
                 ";
             }

             // Query 2: Fallback - Search titles, alt text (more broadly)
             $fallback_conditions = [];
             $fallback_params = [];
             foreach ($keywords as $keyword) {
                 if (mb_strlen($keyword, 'UTF-8') < 3) continue;
                 $like_pattern = '%' . $wpdb->esc_like($keyword) . '%';
                 $fallback_conditions[] = "(a.post_title LIKE %s OR pm.meta_value LIKE %s)";
                 $fallback_params[] = $like_pattern;
                 $fallback_params[] = $like_pattern;
             }

             $query2 = "";
             if (!empty($fallback_conditions)) {
                  $query2 = "
                      (SELECT DISTINCT a.ID, a.post_date as updated_at, 2 as priority
                       FROM {$wpdb->posts} a
                       LEFT JOIN {$wpdb->postmeta} pm ON a.ID = pm.post_id AND pm.meta_key = '_wp_attachment_image_alt'
                       WHERE a.post_type = 'attachment'
                       AND a.post_status = 'inherit'
                       AND a.post_mime_type LIKE 'image/%'
                       AND (" . implode(" OR ", $fallback_conditions) . ")
                      )
                  ";
             }

             // Combine queries with UNION
             $full_query = "";
             $full_params = [];

             if ($query1 && $query2) {
                 $full_query = $query1 . " UNION " . $query2;
                 $full_params = array_merge($params, $fallback_params);
             } elseif ($query1) {
                 $full_query = $query1;
                 $full_params = $params;
             } elseif ($query2) {
                 $full_query = $query2;
                 $full_params = $fallback_params;
             } else {
                  $this->log("Candidate Images: No valid keywords for any search.");
                  return []; // No keywords, no search
             }

             // Add final ordering and limit
             $full_query = "SELECT q.ID FROM ({$full_query}) as q ORDER BY q.priority ASC, q.updated_at DESC LIMIT %d";
             $full_params[] = intval($limit);

             $image_ids = $wpdb->get_col($wpdb->prepare($full_query, $full_params));

             if (empty($image_ids)) {
                 $this->log("Candidate Images: No images found matching keywords: " . implode(', ', $keywords));
                 return [];
             }

            // Get full data for the selected IDs
            $formatted_images = [];
             // Use get_posts for efficiency retrieving multiple attachments
             $attachments = get_posts([
                 'post_type' => 'attachment',
                 'post__in' => $image_ids,
                 'post_status' => 'inherit',
                 'posts_per_page' => $limit,
                 'orderby' => 'post__in', // Keep the order from the SQL query
             ]);

            foreach ($attachments as $image) {
                 if (!$image) continue;

                 $alt_text = get_post_meta($image->ID, '_wp_attachment_image_alt', true);
                 $file_path = get_attached_file($image->ID);
                 $filename = $file_path ? basename($file_path) : null;

                $formatted_images[] = [
                    'id' => $image->ID,
                    'title' => $image->post_title,
                    'alt_text' => $alt_text,
                    'caption' => $image->post_excerpt,
                    'filename' => $filename,
                ];
            }

             $this->log("Candidate Images: Found " . count($formatted_images) . " candidates for keywords: " . implode(', ', $keywords));
            return $formatted_images;
        }, HOUR_IN_SECONDS); // Cache candidate list for 1 hour
    }


    /**
     * Process prompt with Gemini API (Generation Model).
     * Optimization: Uses caching, robust error handling, requests JSON.
     * @param string $prompt The prompt for the AI.
     * @return array ['best_match_id' => id, 'confidence' => score, 'text' => raw_text] or ['error' => message]
     */
    private function process_with_gemini($prompt) {
        $cache_key = "gemini_gen_" . md5($prompt);

        // Check memory cache
        if (isset($this->api_cache[$cache_key])) {
             return $this->api_cache[$cache_key];
        }

        // Use persistent cache (transient)
        return $this->get_cached_data($cache_key, function() use ($prompt, $cache_key) {
            if (empty($this->options['api_key'])) {
                 $this->log("Gemini generation skipped: API key not set.");
                return ['error' => 'API key not set'];
            }

            $endpoint = $this->gemini_api_endpoint_base . $this->gemini_generation_model . ':generateContent';
            $api_key = $this->options['api_key'];

            // Prepare request body for generation model, requesting JSON output
            $body = [
                'contents' => [['role' => 'user', 'parts' => [['text' => $prompt]]]],
                'generationConfig' => [
                    'temperature' => 0.2,      // Lower temp for more deterministic JSON
                    'topP' => 0.9,
                    'topK' => 40,
                    'maxOutputTokens' => 150, // Slightly more tokens for safety, but keep it low
                    'responseMimeType' => 'application/json', // Explicitly request JSON
                ],
                 'safetySettings' => [ // Standard safety settings
                     ['category' => 'HARM_CATEGORY_HARASSMENT', 'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'],
                     ['category' => 'HARM_CATEGORY_HATE_SPEECH', 'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'],
                     ['category' => 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'],
                     ['category' => 'HARM_CATEGORY_DANGEROUS_CONTENT', 'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'],
                 ],
            ];


            $response = wp_remote_post("{$endpoint}?key={$api_key}", [
                'headers' => ['Content-Type' => 'application/json', 'Accept' => 'application/json'],
                'timeout' => 30, // Increased timeout
                'body' => json_encode($body),
                'sslverify' => apply_filters('https_local_ssl_verify', true),
            ]);

            // Handle WP connection errors
            if (is_wp_error($response)) {
                $error_message = $response->get_error_message();
                $this->log('Gemini API connection error: ' . $error_message);
                $result = ['error' => $error_message];
                $this->api_cache[$cache_key] = $result; // Cache error in memory
                return $result;
            }

            // Handle API HTTP errors
            $status_code = wp_remote_retrieve_response_code($response);
            $response_body = wp_remote_retrieve_body($response);
            $result_data = json_decode($response_body, true); // Decode once

            if ($status_code !== 200) {
                 $error_message = "HTTP Error: $status_code";
                 if (isset($result_data['error']['message'])) {
                     $error_message .= " - " . $result_data['error']['message'];
                 }
                 $this->log("Gemini API error: $error_message. Prompt: " . substr($prompt, 0, 100) . "...");
                 $result = ['error' => $error_message];
                 $this->api_cache[$cache_key] = $result; // Cache error in memory
                 return $result;
            }

             // Check for safety blocks or empty candidates in the successful response
            if (empty($result_data['candidates'])) {
                 $block_reason = $result_data['promptFeedback']['blockReason'] ?? 'Unknown reason (empty candidates array)';
                 $safety_ratings = isset($result_data['promptFeedback']['safetyRatings']) ? json_encode($result_data['promptFeedback']['safetyRatings']) : 'N/A';
                 $this->log("Gemini API: Response valid (200 OK) but blocked or empty. Reason: $block_reason. Safety Ratings: $safety_ratings");
                 $result = ['error' => 'Response blocked or empty. Reason: ' . $block_reason];
                 $this->api_cache[$cache_key] = $result; // Cache error in memory
                 return $result;
            }

            // Extract text content - should be JSON formatted string now
            if (isset($result_data['candidates'][0]['content']['parts'][0]['text'])) {
                 $raw_text = $result_data['candidates'][0]['content']['parts'][0]['text'];
                 $this->log("Gemini API Success. Raw Response Text: " . $raw_text);

                 // Attempt to parse the text as JSON (it should be JSON due to responseMimeType)
                 $parsed_json = json_decode($raw_text, true);
                 if (json_last_error() === JSON_ERROR_NONE && isset($parsed_json['best_match_id'])) {
                     $final_result = [
                         'best_match_id' => absint($parsed_json['best_match_id']),
                         'confidence' => $parsed_json['confidence'] ?? null, // Optional confidence
                         'text' => $raw_text // Include raw text for logging/debugging
                     ];
                 } else {
                     // Fallback: Try regex if JSON parsing failed (model didn't follow instructions strictly)
                     $this->log("Gemini API Warning: Response was not valid JSON despite request. Falling back to regex parsing. Text: " . $raw_text);
                     $parsed_fallback = $this->parse_gemini_text_response($raw_text);
                     // Ensure fallback result has the expected structure
                     $final_result = [
                         'best_match_id' => $parsed_fallback['best_match_id'] ?? null,
                         'confidence' => $parsed_fallback['confidence'] ?? null,
                         'text' => $raw_text // Include raw text
                     ];
                 }
                 $this->api_cache[$cache_key] = $final_result; // Cache final result in memory
                 return $final_result;

            } else {
                 $this->log("Gemini API error: Invalid response format. No text part found in candidate. Body: " . $response_body);
                 $result = ['error' => 'Invalid API response format'];
                 $this->api_cache[$cache_key] = $result; // Cache error in memory
                 return $result;
            }
        }, $this->cache_expiration); // Cache generation result for a day
    }

    /**
     * Parse text response from Gemini API (Fallback if JSON fails).
     * Uses Regex to find image ID and optional score. More robust regex.
     * @param string $text The raw text response from AI.
     * @return array ['best_match_id' => id, 'confidence' => score] or empty array.
     */
    private function parse_gemini_text_response($text) {
        // Regex to find "best_match_id": 123 or ID: 123 etc. Handles optional quotes/spaces.
        if (preg_match('/(?:best_match_id\"?\s*[:=]?\s*|ID\s*[:=]?\s*)(\d+)/is', $text, $matches)) {
            $result = ['best_match_id' => absint($matches[1])];

            // Try to extract confidence/score if present nearby (0-1 or 0-100)
            if (preg_match('/(?:score|confidence)\s*[:=]?\s*([0-9]+(?:\.[0-9]+)?)/i', $text, $score_matches)) {
                $score_value = floatval($score_matches[1]);
                // Normalize score to 0.0 - 1.0
                if ($score_value > 1) { // Assume it's 0-100 scale
                     $result['confidence'] = min(1.0, max(0.0, $score_value / 100.0));
                } else { // Assume it's 0-1 scale
                     $result['confidence'] = min(1.0, max(0.0, $score_value));
                }
            }
            return $result;
        }

        // More general fallback: find first standalone number with 2+ digits if no specific pattern matches
        if (preg_match('/\b(\d{2,})\b/', $text, $matches)) {
             $this->log("Gemini Parsing Fallback: Found standalone number {$matches[1]} in response.");
             return ['best_match_id' => absint($matches[1])];
        }

        return []; // No match found
    }

    /**
     * Find image using keyword matching (Fallback method).
     * Optimization: Uses caching, optimized SQL query prioritizing indexed data.
     * @param array $keywords Keywords to search for.
     * @return int|false Image ID or false.
     */
    private function find_image_with_keywords($keywords) {
        global $wpdb;
        if (empty($keywords)) return false;

        $cache_key = "keyword_image_" . md5(implode(',', $keywords));

        return $this->get_cached_data($cache_key, function() use ($keywords, $wpdb) {
            $vector_table = $wpdb->prefix . 'uofi_image_vectors';
            $params = [];
            $keyword_conditions = [];

            // --- Stage 1: Prioritize matches in our indexed keywords table ---
            foreach ($keywords as $keyword) {
                if (mb_strlen($keyword, 'UTF-8') < 3) continue;
                $keyword_conditions[] = "v.keywords LIKE %s";
                $params[] = '%' . $wpdb->esc_like($keyword) . '%';
            }

            if (!empty($keyword_conditions)) {
                 $query_indexed = "
                     SELECT v.attachment_id as ID, COUNT(v.attachment_id) as matches
                     FROM {$vector_table} v
                     INNER JOIN {$wpdb->posts} p ON v.attachment_id = p.ID
                     WHERE p.post_type = 'attachment'
                     AND p.post_status = 'inherit'
                     AND p.post_mime_type LIKE 'image/%'
                     AND (" . implode(" OR ", $keyword_conditions) . ")
                     GROUP BY v.attachment_id
                     ORDER BY matches DESC, v.updated_at DESC
                     LIMIT 10"; // Limit initial candidates

                $results_indexed = $wpdb->get_results($wpdb->prepare($query_indexed, $params));

                if (!empty($results_indexed)) {
                    $top_id = absint($results_indexed[0]->ID);
                    if (wp_attachment_is_image($top_id)) { // Verify it's still a valid image
                         $this->log("Keyword Match: Found potential matches via indexed keywords. Top ID: {$top_id}");
                         return $top_id;
                    } else {
                         $this->log("Keyword Match: Top indexed ID {$top_id} is not a valid image. Continuing search.");
                    }
                }
            }

            // --- Stage 2: Fallback - Search post title/alt/filename ---
             $this->log("Keyword Match: No valid match in indexed keywords, broadening search to title/alt/filename...");
             $conditions = [];
             $params = [];
             $select_scores = [];
             $score_param_count = 0;

             foreach ($keywords as $i => $keyword) {
                 if (mb_strlen($keyword, 'UTF-8') < 3) continue;
                 $like_pattern = '%' . $wpdb->esc_like($keyword) . '%';

                 // Build WHERE conditions
                 $conditions[] = "(a.post_title LIKE %s OR pm.meta_value LIKE %s OR SUBSTRING_INDEX(a.guid, '/', -1) LIKE %s)";
                 $params[] = $like_pattern; // param for title in WHERE
                 $params[] = $like_pattern; // param for alt in WHERE
                 $params[] = $like_pattern; // param for filename in WHERE

                 // Build SELECT scoring conditions (more weight for title/alt)
                 $select_scores[] = "WHEN a.post_title LIKE %s THEN 3"; // Score 3 for title match
                 $params[] = $like_pattern; // param for title in SELECT CASE
                 $select_scores[] = "WHEN pm.meta_value LIKE %s THEN 2"; // Score 2 for alt match
                 $params[] = $like_pattern; // param for alt in SELECT CASE
                 $select_scores[] = "WHEN SUBSTRING_INDEX(a.guid, '/', -1) LIKE %s THEN 1"; // Score 1 for filename match
                 $params[] = $like_pattern; // param for filename in SELECT CASE
                 $score_param_count += 3;
             }

             if (empty($conditions)) {
                  $this->log("Keyword Match: No valid keywords for fallback search.");
                  return false;
             }

             // Combine WHERE conditions with OR
             $where_clause = "(" . implode(" OR ", $conditions) . ")";
             // Combine SELECT scoring with CASE
             $select_clause = "SUM(CASE " . implode(" ", $select_scores) . " ELSE 0 END)";

             $fallback_query = "
                 SELECT a.ID, {$select_clause} as relevance_score
                 FROM {$wpdb->posts} a
                 LEFT JOIN {$wpdb->postmeta} pm ON a.ID = pm.post_id AND pm.meta_key = '_wp_attachment_image_alt'
                 WHERE a.post_type = 'attachment'
                 AND a.post_status = 'inherit'
                 AND a.post_mime_type LIKE 'image/%'
                 AND {$where_clause}
                 GROUP BY a.ID
                 ORDER BY relevance_score DESC, a.post_date DESC
                 LIMIT 1";

             $fallback_result_id = $wpdb->get_var($wpdb->prepare($fallback_query, $params)); // Params array includes WHERE and SELECT params

             if ($fallback_result_id) {
                 $top_id = absint($fallback_result_id);
                 if (wp_attachment_is_image($top_id)) { // Verify
                     $this->log("Keyword Match: Found fallback match via title/alt/filename. ID: {$top_id}");
                     return $top_id;
                 } else {
                      $this->log("Keyword Match: Fallback found ID {$top_id}, but it's not a valid image.");
                 }
             }

            // --- Final Fallback: No suitable image found ---
            $this->log("Keyword Match: No specific match found after all searches.");
            return false;

        }, HOUR_IN_SECONDS); // Cache keyword match result for 1 hour
    }


    /**
     * Insert images into post content under H2 headings.
     * Schedules background processing or runs immediately. Checks lock.
     * @param int $post_id Post ID.
     * @return bool True if scheduled/processed successfully, False on failure/skip.
     */
    public function insert_content_images($post_id) {
        // Skip if feature not enabled
        if (!$this->options['auto_insert_content_images']) {
            return false;
        }

        // Prevent duplicate processing using a transient lock
        $lock_key = 'uofi_processing_content_' . $post_id;
        if (get_transient($lock_key)) {
             $this->log("Content image insertion skipped for post {$post_id}: Already processing or recently processed (lock active).");
            return false;
        }
        // Set lock immediately before deciding background/foreground
        set_transient($lock_key, time(), MINUTE_IN_SECONDS * 5); // Lock for 5 minutes

        // Schedule background processing or run directly
        if ($this->options['background_processing']) {
            // Schedule if not already scheduled
            if (!wp_next_scheduled('uofi_process_content_images', [$post_id])) {
                wp_schedule_single_event(time() + 15, 'uofi_process_content_images', [$post_id]);
                $this->log("Scheduled background content image processing for post {$post_id}.");
                // Note: Lock is active, background task should release it
                return true;
            } else {
                $this->log("Content image processing for post {$post_id} is already scheduled.");
                 // Don't delete the lock, let the scheduled event run, but return false to indicate it wasn't newly scheduled now
                 return false;
            }
        } else {
            // Process immediately
             $result = $this->process_content_images_background($post_id);
             // Delete lock after immediate processing completes (regardless of success/failure)
             delete_transient($lock_key);
             return $result;
        }
    }

    /**
     * Process content images (Background Task or Immediate).
     * Inserts images under H2 headings, respects limits, handles duplicates. Releases lock.
     * @param int $post_id Post ID.
     * @return bool True if content was modified, False otherwise.
     */
    public function process_content_images_background($post_id) {
         $lock_key = 'uofi_processing_content_' . $post_id; // Key for the processing lock
         $this->log("Starting content image processing for post {$post_id}.");
         $final_result = false; // Default result

        try {
            $post = get_post($post_id);
            if (!$post || !in_array($post->post_status, ['publish', 'future'])) {
                $this->log("Content Img (Post {$post_id}): Invalid or non-published post.");
                // Release lock handled in finally block
                return false;
            }

            $original_content = $post->post_content;
            $modified_content = $original_content;

            // Check if content *already* has images that look like ours or general images
            if (strpos($modified_content, 'class="uofi-') !== false || $this->content_has_general_images($modified_content)) {
                 $this->log("Content Img (Post {$post_id}): Skipping, content appears to already contain images.");
                 // Release lock handled in finally block
                 return false;
            }

            // Get H2 headings and their positions
            // Ensure UTF-8 mode for preg_match_all offsets
            $headings = $this->extract_h2_headings($modified_content);

            if (empty($headings)) {
                $this->log("Content Img (Post {$post_id}): No H2 headings found to insert images after.");
                // Release lock handled in finally block
                return false;
            }

            $changed = false;
            $byte_offset_adjustment = 0; // Track changes in byte length
            $used_image_ids = []; // Track used image IDs within this post processing
            $max_images_per_post = $this->options['max_images_per_post'];
            $image_count_this_post = 0;
            $skip_next_heading = false; // For alternating insertion

            foreach ($headings as $heading) {
                 // Check max images per post limit
                if ($image_count_this_post >= $max_images_per_post) {
                    $this->log("Content Img (Post {$post_id}): Reached max images per post limit ({$max_images_per_post}). Stopping insertion.");
                    break;
                }

                // Implement alternating pattern: skip every other heading where an image WAS inserted
                if ($skip_next_heading) {
                    $skip_next_heading = false; // Reset flag
                    $this->log("Content Img (Post {$post_id}): Skipping heading '{$heading['text']}' due to alternating pattern.");
                    continue;
                }

                $heading_text = trim(strip_tags($heading['text'])); // Ensure text is clean
                if (empty($heading_text)) {
                    $this->log("Content Img (Post {$post_id}): Skipping empty H2 heading.");
                    continue;
                }

                $this->log("Content Img (Post {$post_id}): Processing heading: '{$heading_text}'");

                // Find unique image(s) for this heading
                 $num_images_for_heading = max(1, $this->options['max_images_per_heading']);
                 $num_images_to_find = min($num_images_for_heading, $max_images_per_post - $image_count_this_post);

                 if ($num_images_to_find <= 0) continue;

                $images = $this->find_unique_images_for_heading($heading_text, $num_images_to_find, $used_image_ids);

                if (!empty($images)) {
                    // Build image HTML based on settings
                    $image_html = "\n" . $this->build_image_html($images) . "\n";

                    // Calculate insertion position carefully using byte offsets
                    // $heading['pos'] is the byte offset of the *start* of the full <h2> tag in the *original* content
                    $insertion_byte_pos = $heading['pos'] + strlen($heading['full']) + $byte_offset_adjustment;

                    // Insert the HTML using substr_replace (byte-safe)
                    $modified_content = substr_replace($modified_content, $image_html, $insertion_byte_pos, 0);

                    // Update offset adjustment based on the byte length of the inserted HTML
                    $byte_offset_adjustment += strlen($image_html);
                    $changed = true;

                    // Track used images and count
                    $num_inserted = count($images);
                    foreach ($images as $image) {
                        $used_image_ids[] = $image['id'];
                        $this->log("Content Img (Post {$post_id}): Inserted image ID {$image['id']} after heading '{$heading_text}'.");
                    }
                    $image_count_this_post += $num_inserted;

                    // Set flag to skip the next heading (alternating pattern)
                    $skip_next_heading = true;

                } else {
                    $this->log("Content Img (Post {$post_id}): No unique images found for heading '{$heading_text}'.");
                     // Don't set skip_next_heading = true if no image was inserted here
                }
            } // End foreach heading

            // Update the post only if content was actually changed
            if ($changed) {
                 // Remove revisions hook temporarily to prevent triggering this logic again recursively
                 remove_action('save_post', [$this, 'process_post_save'], 20);

                 $update_args = [
                     'ID' => $post_id,
                     'post_content' => $modified_content
                 ];
                 // Prevent modifying the post date if it was 'future'
                 if ($post->post_status === 'future') {
                      $update_args['edit_date'] = true; // Keep the future date
                 }

                 $update_result = wp_update_post($update_args, true); // Pass true for WP_Error return

                 add_action('save_post', [$this, 'process_post_save'], 20, 2); // Re-add hook

                 if (is_wp_error($update_result)) {
                     $this->log("Content Img (Post {$post_id}): FAILED to update post content. Error: " . $update_result->get_error_message());
                     $final_result = false;
                 } else {
                     $this->log("Content Img (Post {$post_id}): Successfully updated post content with {$image_count_this_post} inserted images.");
                     $final_result = true;

                     // Optionally clear related caches for this post
                     wp_cache_delete('post_' . $post_id, 'posts');
                     wp_cache_delete('post_meta_' . $post_id, 'post_meta');

                     // Clean up duplicate images *after* successful update, if enabled
                     if ($final_result && $this->options['delete_duplicate_images']) {
                          $this->log("Content Img (Post {$post_id}): Running duplicate image removal after insertion.");
                          // Schedule or run immediately based on config? For simplicity run immediately here.
                          $this->remove_duplicate_images($post_id);
                     }
                 }
            } else {
                 $this->log("Content Img (Post {$post_id}): No changes made to content.");
                 $final_result = false;
            }

        } catch (Exception $e) {
             $this->log("Content Img (Post {$post_id}): CRITICAL ERROR during processing: " . $e->getMessage() . " Stack: " . $e->getTraceAsString());
             $final_result = false;
        } finally {
             // Ensure the lock is always released, regardless of success, failure, or exception
             delete_transient($lock_key);
             $this->log("Content Img (Post {$post_id}): Processing finished. Lock released. Result: " . ($final_result ? 'Success' : 'NoChange/Failure'));
        }

        return $final_result;
    }


    /**
     * Find unique images for a heading, excluding specified IDs.
     * Optimization: Uses caching, tries AI then keywords.
     * @param string $heading_text Text of the H2 heading.
     * @param int $count Number of images needed.
     * @param array $excluded_ids IDs of images already used in this post.
     * @return array Array of image data arrays (id, url, title, alt, caption).
     */
    private function find_unique_images_for_heading($heading_text, $count = 1, $excluded_ids = []) {
         if (empty(trim($heading_text)) || $count <= 0) return [];

         $excluded_ids_key = !empty($excluded_ids) ? implode('_', $excluded_ids) : 'none';
         // Shorten cache key components
         $heading_hash = substr(md5($heading_text), 0, 10);
         $exclude_hash = substr(md5($excluded_ids_key), 0, 10);
         $cache_key = "head_img_{$heading_hash}_c{$count}_e{$exclude_hash}";

        return $this->get_cached_data($cache_key, function() use ($heading_text, $count, $excluded_ids) {
            // Extract keywords from heading
            $keywords = $this->extract_keywords($heading_text);
            if (empty($keywords)) {
                $this->log("Heading Img: No keywords for '{$heading_text}'.");
                return [];
            }

            $found_images = [];
            $ids_to_exclude = $excluded_ids; // Keep track of IDs to exclude in subsequent searches

            // Try AI if enabled
            if ($this->options['use_ai'] && !empty($this->options['api_key'])) {
                 try {
                     $this->log("Heading Img AI: Finding images for '{$heading_text}' (Need: {$count}, Excl: " . count($ids_to_exclude) . ")");
                     // Ask AI for slightly more images to allow for filtering exclusions
                     $num_ai_candidates = $count + count($ids_to_exclude) + 5; // Ask for more to increase chance
                     $ai_candidates = $this->find_heading_images_with_ai($heading_text, $keywords, $num_ai_candidates);

                     if (!empty($ai_candidates)) {
                         // Filter out excluded IDs and add to found images until count is met
                         foreach ($ai_candidates as $image_data) {
                             if (count($found_images) >= $count) break; // Stop if we have enough
                             if (!in_array($image_data['id'], $ids_to_exclude)) {
                                 $found_images[] = $image_data;
                                 $ids_to_exclude[] = $image_data['id']; // Add to exclusion list for potential keyword fallback
                             }
                         }
                         $this->log("Heading Img AI: Found " . count($found_images) . " unique images via AI for '{$heading_text}'.");
                     }
                 } catch (Exception $e) {
                      $this->log("Heading Img AI: Error finding images with AI for '{$heading_text}': " . $e->getMessage());
                      // Continue to keyword fallback
                 }
            }

            // Fallback to keyword matching if AI is disabled or didn't find enough images
            if (count($found_images) < $count) {
                $needed = $count - count($found_images);
                $this->log("Heading Img KW: Need {$needed} more images via keywords for '{$heading_text}'.");

                $keyword_images = $this->find_heading_images_with_keywords_excluding($keywords, $needed, $ids_to_exclude);

                if (!empty($keyword_images)) {
                    $this->log("Heading Img KW: Found " . count($keyword_images) . " unique images via keywords.");
                    // Merge results (ensure no duplicates added if AI partially succeeded)
                    foreach ($keyword_images as $img) {
                        if (count($found_images) < $count && !in_array($img['id'], $ids_to_exclude)) {
                             $found_images[] = $img;
                             $ids_to_exclude[] = $img['id']; // Should already be excluded by query, but double-check
                        }
                    }
                } else {
                     $this->log("Heading Img KW: No additional images found via keywords.");
                }
            }

            // Ensure we only return the number requested
            return array_slice($found_images, 0, $count);

        }, HOUR_IN_SECONDS); // Cache results for 1 hour
    }

    /**
     * Find heading images using keywords with optimized query, excluding specified IDs.
     * Optimization: Uses JOINs, prepare, exclusion. Returns full image data.
     * @param array $keywords Keywords to search for.
     * @param int $count Number of images needed.
     * @param array $excluded_ids IDs to exclude.
     * @return array Array of image data arrays.
     */
    private function find_heading_images_with_keywords_excluding($keywords, $count, $excluded_ids = []) {
        global $wpdb;
        if (empty($keywords) || $count <= 0) return [];

        $params = [];
        $conditions = [];
        $select_scores = [];

        // Build keyword matching parts (similar to find_image_with_keywords fallback)
        foreach ($keywords as $keyword) {
            if (mb_strlen($keyword, 'UTF-8') < 3) continue;
            $like_pattern = '%' . $wpdb->esc_like($keyword) . '%';

            // WHERE conditions
            $conditions[] = "(a.post_title LIKE %s OR pm.meta_value LIKE %s OR SUBSTRING_INDEX(a.guid, '/', -1) LIKE %s)";
            $params[] = $like_pattern; $params[] = $like_pattern; $params[] = $like_pattern;

            // SELECT scoring
            $select_scores[] = "WHEN a.post_title LIKE %s THEN 3"; $params[] = $like_pattern;
            $select_scores[] = "WHEN pm.meta_value LIKE %s THEN 2"; $params[] = $like_pattern;
            $select_scores[] = "WHEN SUBSTRING_INDEX(a.guid, '/', -1) LIKE %s THEN 1"; $params[] = $like_pattern;
        }

        if (empty($conditions)) return []; // No valid keywords

        // Base query
        $query = "
            SELECT a.ID, SUM(CASE " . implode(" ", $select_scores) . " ELSE 0 END) as relevance_score
            FROM {$wpdb->posts} a
            LEFT JOIN {$wpdb->postmeta} pm ON a.ID = pm.post_id AND pm.meta_key = '_wp_attachment_image_alt'
            WHERE a.post_type = 'attachment'
            AND a.post_status = 'inherit'
            AND a.post_mime_type LIKE 'image/%'
        ";

        // Add exclusion for already used image IDs
        if (!empty($excluded_ids)) {
            // Sanitize IDs just in case
            $excluded_ids = array_map('absint', $excluded_ids);
            $placeholders = implode(',', array_fill(0, count($excluded_ids), '%d'));
            $query .= " AND a.ID NOT IN ($placeholders)";
            // Prepend excluded IDs to the start of the params array for NOT IN
            $params = array_merge($excluded_ids, $params);
        }

        // Add keyword WHERE conditions
        $query .= " AND (" . implode(" OR ", $conditions) . ")";

        // Complete query with GROUP BY and ORDER BY
        $query .= "
            GROUP BY a.ID
            ORDER BY relevance_score DESC, a.post_date DESC
            LIMIT %d"; // Limit to the count needed

        $params[] = intval($count);

        // Execute query
        $image_ids = $wpdb->get_col($wpdb->prepare($query, $params));

        // Get full image data for the found IDs
        $images = [];
        if (!empty($image_ids)) {
            foreach ($image_ids as $id) {
                $image_data = $this->get_image_data($id); // Uses caching
                if ($image_data) {
                    $images[] = $image_data;
                }
            }
        }

        return $images;
    }


    /**
     * Detect and remove duplicate images from a post based on image src attribute.
     * Handles images wrapped in optional divs or figures.
     * @param int $post_id Post ID.
     * @return bool True if duplicates were removed, False otherwise.
     */
    public function remove_duplicate_images($post_id) {
        // Skip if not enabled in settings
        if (empty($this->options['delete_duplicate_images'])) {
            return false;
        }

        $post = get_post($post_id);
        if (!$post || !in_array($post->post_status, ['publish', 'future', 'draft', 'pending'])) { // Process more statuses?
             $this->log("Remove Duplicates (Post {$post_id}): Invalid post or status.");
            return false;
        }

        $original_content = $post->post_content;
        $modified_content = $original_content;
        $duplicates_removed_count = 0;

        // Regex to find all <img> tags and capture their src attribute
        // This regex is more robust, handling different quote types and attributes before src
        $img_pattern = '/<img[^>]+src=([\'"])(.*?)\1[^>]*>/i';
        if (preg_match_all($img_pattern, $modified_content, $matches, PREG_SET_ORDER | PREG_OFFSET_CAPTURE)) {

            $image_occurrences = []; // Track src and full tag/offset

            foreach ($matches as $match) {
                $full_tag = $match[0][0];
                $src = $match[2][0]; // The src attribute value
                $offset = $match[0][1];

                 // Normalize src? Remove query strings? Basic normalization:
                 $normalized_src = strtok($src, '?'); // Remove query string

                if (isset($image_occurrences[$normalized_src])) {
                    // This src is a duplicate, mark the tag for removal
                     $image_occurrences[$normalized_src]['duplicates'][] = ['tag' => $full_tag, 'offset' => $offset];
                } else {
                    // First time seeing this src
                    $image_occurrences[$normalized_src] = [
                        'first' => ['tag' => $full_tag, 'offset' => $offset],
                        'duplicates' => []
                    ];
                }
            }

            // Now process removals, working backwards from the end of the content
            // Collect all duplicate tags with offsets
            $tags_to_remove = [];
            foreach ($image_occurrences as $src => $data) {
                if (!empty($data['duplicates'])) {
                    $tags_to_remove = array_merge($tags_to_remove, $data['duplicates']);
                }
            }

            // Sort tags by offset descending to avoid messing up subsequent offsets
            usort($tags_to_remove, function($a, $b) {
                return $b['offset'] - $a['offset'];
            });

            // Remove the tags from the content string
            foreach ($tags_to_remove as $item) {
                 $tag_to_remove = $item['tag'];
                 $offset = $item['offset'];

                 // Check for simple parent wrappers (div, figure, p) containing only this image (and maybe whitespace)
                 // This regex is complex and might need refinement based on actual content structure
                 $wrapper_pattern = '/<(?<tag>div|figure|p)[^>]*>\s*' . preg_quote($tag_to_remove, '/') . '\s*<\/\k<tag>>/i';

                 // Look for the wrapper immediately around the tag's offset
                 $search_start = max(0, $offset - 100); // Look back a bit
                 $search_len = strlen($tag_to_remove) + 200; // Look around the tag

                 if (preg_match($wrapper_pattern, substr($modified_content, $search_start, $search_len), $wrapper_match, PREG_OFFSET_CAPTURE)) {
                      // Found a wrapper, remove it entirely
                      $wrapper_full = $wrapper_match[0][0];
                      $wrapper_offset = $search_start + $wrapper_match[0][1];
                      $modified_content = substr_replace($modified_content, '', $wrapper_offset, strlen($wrapper_full));
                      $this->log("Remove Duplicates (Post {$post_id}): Removed duplicate image tag WITH wrapper: " . esc_html($tag_to_remove));
                 } else {
                      // No simple wrapper found, just remove the image tag
                      $modified_content = substr_replace($modified_content, '', $offset, strlen($tag_to_remove));
                       $this->log("Remove Duplicates (Post {$post_id}): Removed duplicate image tag: " . esc_html($tag_to_remove));
                 }
                 $duplicates_removed_count++;
            }
        } else {
            // No images found at all
            // $this->log("Remove Duplicates (Post {$post_id}): No img tags found.");
            return false;
        }

        // Update post only if changes were made
        if ($duplicates_removed_count > 0) {
             // Remove hooks temporarily
             remove_action('save_post', [$this, 'process_post_save'], 20);

             $update_args = [
                 'ID' => $post_id,
                 'post_content' => $modified_content
             ];
             if ($post->post_status === 'future') {
                  $update_args['edit_date'] = true;
             }
             $update_result = wp_update_post($update_args, true);

             add_action('save_post', [$this, 'process_post_save'], 20, 2); // Re-add hook

            if (is_wp_error($update_result)) {
                $this->log("Remove Duplicates (Post {$post_id}): FAILED to update post content. Error: " . $update_result->get_error_message());
                return false;
            } else {
                $this->log("Remove Duplicates (Post {$post_id}): Successfully removed {$duplicates_removed_count} duplicate images.");
                wp_cache_delete('post_' . $post_id, 'posts'); // Clear cache
                return true;
            }
        } else {
             $this->log("Remove Duplicates (Post {$post_id}): No duplicate images found to remove.");
            return false;
        }
    }


    /**
     * Check if content already has general image tags. More lenient check.
     * Optimization: Uses strpos for speed.
     * @param string $content Post content.
     * @return bool True if images likely exist.
     */
    private function content_has_general_images($content) {
        // Check for common image indicators
        return strpos($content, '<img') !== false ||
               strpos($content, '[gallery') !== false ||
               strpos($content, 'wp-block-image') !== false ||
               strpos($content, 'class="wp-block-gallery') !== false ||
               // Add checks for other common image/gallery blocks or shortcodes if necessary
               strpos($content, 'class="wp-image-') !== false;
    }

    /**
     * Extract H2 headings from content with byte offsets.
     * Optimization: Uses caching and preg_match_all with PREG_OFFSET_CAPTURE.
     * @param string $content Post content.
     * @return array Array of headings ['full' => tag, 'text' => inner text, 'pos' => byte offset].
     */
    private function extract_h2_headings($content) {
         $cache_key = "h2_headings_" . md5($content);
        return $this->get_cached_data($cache_key, function() use ($content) {
            $headings = [];
            // Regex to capture full H2 tag and its content, with byte offsets
            $pattern = '/<h2[^>]*>(.*?)<\/h2>/is';

            // Using PREG_OFFSET_CAPTURE to get byte positions
            if (preg_match_all($pattern, $content, $matches, PREG_OFFSET_CAPTURE)) {
                foreach ($matches[0] as $index => $heading_match) {
                    $headings[] = [
                        'full' => $heading_match[0], // Full <h2>...</h2> tag
                        'text' => $matches[1][$index][0], // Inner content of the H2
                        'pos' => $heading_match[1] // Byte offset of the start of the full tag
                    ];
                }
            }
            return $headings;
        }, HOUR_IN_SECONDS); // Cache extracted headings for an hour
    }


    /**
     * Find heading images using AI. Asks AI to select multiple images.
     * @param string $heading_text Text of the heading.
     * @param array $keywords Keywords from the heading.
     * @param int $count Number of images requested.
     * @return array Array of image data arrays. Returns empty if AI fails.
     */
    private function find_heading_images_with_ai($heading_text, $keywords, $count) {
        $this->log("Heading Img AI: Requesting {$count} images for '{$heading_text}'.");

        // Get a larger pool of candidates for AI to choose from
        $num_candidates = max(20, $count * 3); // Get at least 20, or 3x the needed count
        $candidate_images = $this->get_candidate_images($keywords, $num_candidates);

        if (empty($candidate_images)) {
            $this->log("Heading Img AI: No candidate images found for keywords: " . implode(', ', $keywords));
            return [];
        }

        // Build prompt asking for multiple IDs in JSON format
        $prompt = "You are an expert image selector for blog content.\n";
        $prompt .= "Task: Select the best " . $count . " images that visually illustrate or relate to the following heading.\n\n";
        $prompt .= "## Heading Text:\n" . $heading_text . "\n\n";
        $prompt .= "## Related Keywords:\n" . implode(', ', $keywords) . "\n\n";
        $prompt .= "## Available Candidate Images:\n";
        $prompt .= "Choose the " . $count . " most relevant and visually distinct images from this list.\n\n";

        foreach ($candidate_images as $index => $image) {
            $prompt .= ($index + 1) . ". **Image ID:** " . $image['id'] . "\n";
            $prompt .= "   - **Title:** " . ($image['title'] ?: 'N/A') . "\n";
            $prompt .= "   - **Alt Text:** " . ($image['alt_text'] ?: 'N/A') . "\n";
            $prompt .= "   - **Filename:** " . ($image['filename'] ?: 'N/A') . "\n\n";
        }

        $prompt .= "## Instructions:\n";
        $prompt .= "1. Analyze the heading, keywords, and candidate images.\n";
        $prompt .= "2. Select exactly " . $count . " images (by ID) that best fit the heading's topic.\n";
        $prompt .= "3. Respond **only** with a JSON object containing an array of the chosen image IDs. Example format:\n";
        $prompt .= "   ```json\n";
        $prompt .= "   {\"image_ids\": [ID1, ID2, ...] }\n";
        $prompt .= "   ```\n";
        $prompt .= "   (Replace ID1, ID2, etc., with the actual numbers, e.g., {\"image_ids\": [123, 456]}).\n";
        $prompt .= "4. **Do not** include any other text, explanation, or formatting outside the JSON object.";

        // Process with Gemini
        $api_result = $this->process_with_gemini($prompt);

        // Check for API errors or lack of expected data
        if (isset($api_result['error']) || empty($api_result['text'])) {
            $error_msg = $api_result['error'] ?? 'Empty response from API';
            $this->log("Heading Img AI: Gemini API error or empty response for '{$heading_text}': " . $error_msg);
            return [];
        }

        // Parse the response (expecting JSON in 'text' field)
        $image_ids = [];
        $raw_text = $api_result['text'];
        $parsed_json = json_decode($raw_text, true);

        if (json_last_error() === JSON_ERROR_NONE && isset($parsed_json['image_ids']) && is_array($parsed_json['image_ids'])) {
            // Successfully parsed JSON with expected key
            $image_ids = array_map('absint', $parsed_json['image_ids']);
            $this->log("Heading Img AI: Parsed JSON response for '{$heading_text}'. Found IDs: " . implode(', ', $image_ids));
        } else {
            // Fallback: Try regex on the raw text if JSON parsing failed
            $this->log("Heading Img AI: Failed to parse JSON response for '{$heading_text}'. Trying regex fallback. Text: " . $raw_text);
            if (preg_match_all('/"?image_ids"?\s*:\s*\[([^\]]*)\]/is', $raw_text, $match)) {
                 $ids_str = $match[1][0];
                 if(preg_match_all('/(\d+)/', $ids_str, $id_matches)) {
                     $image_ids = array_map('absint', $id_matches[0]);
                     $this->log("Heading Img AI: Extracted IDs via regex fallback: " . implode(', ', $image_ids));
                 }
            }
             // Even more basic fallback: just find all numbers in the response
             if (empty($image_ids)) {
                  if (preg_match_all('/\b(\d{2,})\b/', $raw_text, $num_matches)) {
                       $image_ids = array_map('absint', $num_matches[0]);
                       $this->log("Heading Img AI: Extracted IDs via general number regex fallback: " . implode(', ', $image_ids));
                  }
             }
        }

        // Filter IDs to ensure they were among the candidates and are valid images
        $valid_image_ids = [];
        $candidate_id_map = array_flip(wp_list_pluck($candidate_images, 'id')); // Map for quick lookup

        foreach ($image_ids as $id) {
            if (isset($candidate_id_map[$id]) && wp_attachment_is_image($id)) {
                $valid_image_ids[] = $id;
                if (count($valid_image_ids) >= $count) {
                     break; // Stop once we have enough valid IDs
                }
            } else {
                 $this->log("Heading Img AI: ID {$id} returned by AI is invalid or wasn't a candidate.");
            }
        }

        // Get full image data for the validated IDs
        $selected_images = [];
        if (!empty($valid_image_ids)) {
            foreach ($valid_image_ids as $id) {
                $image_data = $this->get_image_data($id); // Uses caching
                if ($image_data) {
                    $selected_images[] = $image_data;
                }
            }
        }

        $this->log("Heading Img AI: Returning " . count($selected_images) . " validated images for '{$heading_text}'.");
        return $selected_images;
    }

    /**
     * Get image data (URL, alt, caption, etc.) with caching.
     * @param int $image_id Attachment ID.
     * @return array|false Image data array or false if invalid ID.
     */
    private function get_image_data($image_id) {
        $image_id = absint($image_id);
        if (!$image_id) return false;

        // Check memory cache first
        if (isset($this->image_cache[$image_id])) {
            return $this->image_cache[$image_id];
        }

        // Use transient cache for persistence across requests
        $cache_key = "image_data_{$image_id}";
        return $this->get_cached_data($cache_key, function() use ($image_id) {
            $image = get_post($image_id);
            // Ensure it's a valid attachment and an image
            if (!$image || $image->post_type !== 'attachment' || !wp_attachment_is_image($image_id)) {
                 $this->log("Get Image Data: Invalid attachment ID requested: {$image_id}");
                return false;
            }

            // Get image details
            $image_size = $this->options['image_size'] ?? 'medium';
            $url = wp_get_attachment_image_url($image_id, $image_size);
            if (!$url) {
                 // Fallback size if requested size doesn't exist
                 $url = wp_get_attachment_image_url($image_id, 'medium');
                 if (!$url) $url = wp_get_attachment_image_url($image_id, 'full'); // Absolute fallback
            }

            $alt = get_post_meta($image_id, '_wp_attachment_image_alt', true);
            // Try title as fallback alt text if alt is empty
            $alt_text = !empty($alt) ? $alt : $image->post_title;
            $caption = $image->post_excerpt; // Caption is stored in post_excerpt

            // Build image data array
            $image_data = [
                'id' => $image_id,
                'url' => $url,
                'title' => $image->post_title,
                'alt' => $alt_text,
                'caption' => $caption
            ];

            // Store in memory cache for this request
            $this->image_cache[$image_id] = $image_data;

            return $image_data;
        }, $this->cache_expiration); // Cache image data for a day
    }

    /**
     * Build HTML for images based on insertion style setting. Includes lazy loading.
     * @param array $images Array of image data arrays.
     * @return string HTML string.
     */
    private function build_image_html($images) {
        if (empty($images)) {
            return '';
        }

        // Ensure 'max_images_per_heading' doesn't exceed the actual number of images found
        $max_images = max(1, $this->options['max_images_per_heading']);
        $images_to_display = array_slice($images, 0, $max_images);
        $style = (count($images_to_display) > 1) ? ($this->options['image_insertion_style'] ?? 'grid') : 'single';

        // Generate HTML based on style
        switch ($style) {
            case 'grid':
                return $this->build_grid_html($images_to_display);
            case 'carousel':
                // Enqueue carousel script/style if not already done (better approach than inline JS)
                // For simplicity here, we keep the inline JS version from the original code.
                // wp_enqueue_script('uofi-carousel-js'); wp_enqueue_style('uofi-carousel-css');
                return $this->build_carousel_html($images_to_display);
            case 'single':
            default:
                return $this->build_single_image_html($images_to_display[0]);
        }
    }

    /**
     * Build HTML for grid layout with lazy loading and basic styling.
     */
    private function build_grid_html($images) {
        $columns = count($images) >= 3 ? 3 : count($images); // Simple logic for 1, 2, or 3 columns
        $grid_style = "display:grid; grid-template-columns:repeat({$columns}, 1fr); gap: 15px; margin: 20px 0;";
        $html = '<div class="uofi-image-grid" style="' . esc_attr($grid_style) . '">';

        foreach ($images as $image) {
            $html .= '<figure class="uofi-grid-item" style="margin: 0;">'; // Use figure for semantics
            $html .= sprintf(
                '<img src="%s" alt="%s" class="uofi-image wp-image-%d" loading="lazy" decoding="async" style="width:100%%; height:auto; display:block; border-radius: 4px;" />',
                esc_url($image['url']),
                esc_attr($image['alt']),
                esc_attr($image['id'])
            );
            if (!empty($image['caption'])) {
                $html .= '<figcaption class="uofi-caption wp-element-caption" style="font-size:0.9em; margin-top: 5px; color: #555; text-align: center;">' . esc_html($image['caption']) . '</figcaption>';
            }
            $html .= '</figure>';
        }

        $html .= '</div>';
        return $html;
    }

    /**
     * Build HTML for single image with lazy loading and basic styling.
     */
    private function build_single_image_html($image) {
        $html = '<figure class="uofi-single-image wp-block-image size-large" style="margin: 20px 0;">'; // Use figure, match block editor style
        $html .= sprintf(
            '<img src="%s" alt="%s" class="uofi-image wp-image-%d" loading="lazy" decoding="async" style="max-width:100%%; height:auto; display:block; margin-left: auto; margin-right: auto; border-radius: 4px;" />',
            esc_url($image['url']),
            esc_attr($image['alt']),
            esc_attr($image['id'])
        );
        if (!empty($image['caption'])) {
            $html .= '<figcaption class="uofi-caption wp-element-caption" style="font-size:0.9em; margin-top: 5px; color:#555; text-align: center;">' . esc_html($image['caption']) . '</figcaption>';
        }
        $html .= '</figure>';
        return $html;
    }

    /**
     * Build HTML for carousel layout with lazy loading and inline JS.
     * Note: Inline JS is generally discouraged; a separate enqueued file is better practice.
     */
    private function build_carousel_html($images) {
        // Unique ID for each carousel instance
        $id = 'uofi-carousel-' . uniqid();

        $html = '<div id="' . esc_attr($id) . '" class="uofi-carousel" style="position:relative; margin:20px 0; overflow:hidden; max-width: 100%;">';
        $html .= '<div class="uofi-carousel-inner" style="display:flex; transition:transform 0.5s ease-in-out; will-change: transform;">';

        foreach ($images as $index => $image) {
            // Use figure for each item
            $html .= '<figure class="uofi-carousel-item" style="margin:0; flex:0 0 100%; max-width:100%; box-sizing: border-box;">';
            $html .= sprintf(
                '<img src="%s" alt="%s" class="uofi-image wp-image-%d" loading="%s" decoding="async" style="width:100%%; height:auto; display:block; border-radius: 4px;" />',
                esc_url($image['url']),
                esc_attr($image['alt']),
                esc_attr($image['id']),
                ($index === 0) ? 'eager' : 'lazy' // Load first image eagerly, others lazily
            );
            if (!empty($image['caption'])) {
                $html .= '<figcaption class="uofi-caption wp-element-caption" style="font-size:0.9em; padding: 8px 0; color:#555; text-align:center;">' . esc_html($image['caption']) . '</figcaption>';
            }
             $html .= '</figure>';
        }

        $html .= '</div>'; // End inner

        // Add navigation buttons if more than one image
        if (count($images) > 1) {
            $btn_style = 'position:absolute; top:50%; transform:translateY(-50%); background:rgba(0,0,0,0.5); color:white; border:none; border-radius:50%; width:40px; height:40px; font-size:20px; cursor:pointer; display:flex; align-items:center; justify-content:center; padding:0; line-height:1; z-index:10;';
            $html .= '<button class="uofi-carousel-prev" aria-label="' . esc_attr__('Previous', 'ultra-optimized-featured-image') . '" style="' . esc_attr($btn_style . ' left:10px;') . '">❮</button>';
            $html .= '<button class="uofi-carousel-next" aria-label="' . esc_attr__('Next', 'ultra-optimized-featured-image') . '" style="' . esc_attr($btn_style . ' right:10px;') . '">❯</button>';
        }

        $html .= '</div>'; // End carousel wrap

        // Add inline JavaScript for carousel functionality. Consider moving to a separate file.
        if (count($images) > 1) {
             // Only add script if needed
             ob_start();
             ?>
             <script>
             (function() {
                 function initUOFI Carousel(carouselId) {
                     const carousel = document.getElementById(carouselId);
                     if (!carousel) return;
                     const inner = carousel.querySelector('.uofi-carousel-inner');
                     const items = carousel.querySelectorAll('.uofi-carousel-item');
                     const prevBtn = carousel.querySelector('.uofi-carousel-prev');
                     const nextBtn = carousel.querySelector('.uofi-carousel-next');
                     const totalItems = items.length;
                     let currentIndex = 0;

                     function goToSlide(index) {
                         if (index < 0) index = totalItems - 1;
                         if (index >= totalItems) index = 0;
                         inner.style.transform = `translateX(-${index * 100}%)`;
                         currentIndex = index;
                         // Optional: Update aria attributes or active classes if needed
                     }

                     if (prevBtn) {
                         prevBtn.addEventListener('click', function() {
                             goToSlide(currentIndex - 1);
                         });
                     }
                     if (nextBtn) {
                         nextBtn.addEventListener('click', function() {
                             goToSlide(currentIndex + 1);
                         });
                     }

                     // Optional: Add swipe support or auto-play here if desired
                 }

                 // Initialize this specific carousel instance
                 if (document.readyState === "loading") {
                     document.addEventListener("DOMContentLoaded", function() { initUOFI Carousel('<?php echo esc_js($id); ?>'); });
                 } else {
                     initUOFI Carousel('<?php echo esc_js($id); ?>');
                 }
             })();
             </script>
             <?php
             $html .= ob_get_clean();
        }

        return $html;
    }


    /**
     * Log assignment/failure/skip action to the database.
     * @param int $post_id Post ID.
     * @param int|null $image_id Assigned image ID, or null if failed/skipped.
     * @param array $keywords Keywords used for matching.
     * @param string $status 'success', 'failed', 'skipped'.
     * @param string $message Optional message detailing reason for failure/skip or method.
     * @param float|null $score Optional confidence score (e.g., from AI).
     */
    private function log_assignment($post_id, $image_id, $keywords, $status, $message = '', $score = null) {
        global $wpdb;

        if (!$this->log_enabled) {
            return;
        }

        // Log to error log as well for easier debugging during development/issues
        $log_level = ($status === 'failed') ? 'ERROR' : 'INFO';
        $log_message = "[UOFI Assign Log - {$log_level}] Post: {$post_id}, Image: " . ($image_id ?: 'None') . ", Status: {$status}, Method: " . ($this->options['use_ai'] ? 'ai' : 'keywords') . ", Message: {$message}";
        error_log($log_message);


        // Log to database
        $log_table = $wpdb->prefix . 'uofi_assignment_logs';
        $keywords_str = is_array($keywords) ? implode(',', $keywords) : $keywords;

        // Check if table exists before logging
         static $log_table_exists = null;
         if ($log_table_exists === null) {
             $log_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$log_table'") === $log_table;
         }
         if (!$log_table_exists) {
              error_log("[UOFI] Log table {$log_table} does not exist. Cannot log assignment.");
              return;
         }


        $wpdb->insert(
            $log_table,
            [
                'post_id' => $post_id,
                'image_id' => $image_id, // Can be null
                'score' => $score,       // Can be null
                'method' => ($this->options['use_ai'] && !empty($this->options['api_key'])) ? 'ai' : 'keywords',
                'keywords' => $keywords_str, // Can be null/empty
                'status' => sanitize_key($status),
                'message' => sanitize_text_field($message),
                'created_at' => current_time('mysql', 1) // GMT time
            ],
            [
                '%d', // post_id
                '%d', // image_id
                '%f', // score
                '%s', // method
                '%s', // keywords
                '%s', // status
                '%s', // message
                '%s'  // created_at
            ]
        );

         if ($wpdb->last_error) {
              error_log("[UOFI] DB Error logging assignment: " . $wpdb->last_error);
         }
    }

    /**
     * Log general message if logging is enabled.
     * @param string $message Message to log.
     */
    private function log($message) {
        if ($this->log_enabled) {
            // Log to standard PHP error log
            error_log('[UOFI] ' . $message);
            // Optionally implement more advanced logging (e.g., to a file or dedicated DB table)
        }
    }

    /**
     * Process batch of posts in the background (Cron task).
     * Handles featured image and optionally content image insertion.
     */
    public function process_scheduled_batch() {
        $this->log("Starting scheduled batch processing...");
        $start_time = microtime(true);
        // Set a time limit slightly less than common PHP execution limits (e.g., 30s or 60s)
        $time_limit = apply_filters('uofi_batch_time_limit', 45); // 45 seconds
        $processed_count = 0;
        $failed_count = 0;
        $skipped_count = 0;
        $batch_limit = $this->batch_size; // How many to fetch in one go

        // Get posts that need featured images
        $posts_to_process = $this->get_posts_without_featured_image(0, $batch_limit);

        if (empty($posts_to_process)) {
             $this->log("Scheduled batch: No posts found needing featured images.");
             return;
        }

        $this->log("Scheduled batch: Found " . count($posts_to_process) . " posts to process (Limit: {$batch_limit}).");

        foreach ($posts_to_process as $post) {
            // Check time limit to avoid timeouts
            if ((microtime(true) - $start_time) > $time_limit) {
                $this->log("Scheduled batch stopped due to time limit ({$time_limit}s). Processed: {$processed_count}, Failed: {$failed_count}, Skipped: {$skipped_count}");
                break;
            }

            $post_id = $post->ID;
            $this->log("Scheduled batch: Processing post {$post_id}...");

            try {
                 // --- 1. Assign Featured Image ---
                 // Check again if it has one now (could have been added manually)
                 if (has_post_thumbnail($post_id)) {
                      $this->log("Scheduled batch (Post {$post_id}): Skipped featured image - already present.");
                      $skipped_count++;
                 } else {
                     $featured_success = $this->assign_featured_image($post_id);
                     if ($featured_success) {
                         $processed_count++;
                     } else {
                         // assign_featured_image logs its own failures/skips
                         $failed_count++; // Count as failed if assignment didn't happen
                     }
                 }

                 // --- 2. Insert Content Images (if enabled) ---
                 if ($this->options['auto_insert_content_images']) {
                     // Check if content already has images before processing
                     $current_content = get_post_field('post_content', $post_id);
                     if (strpos($current_content, 'class="uofi-') === false && !$this->content_has_general_images($current_content)) {
                          // Use the main function which handles locking and background/foreground logic
                          // Since we are already in a background task, it should run immediately.
                          $content_inserted = $this->insert_content_images($post_id);
                          if ($content_inserted) {
                               $this->log("Scheduled batch (Post {$post_id}): Content images processed.");
                               // Optionally count this separately?
                          } else {
                               // insert_content_images logs its own reasons
                          }
                     } else {
                          $this->log("Scheduled batch (Post {$post_id}): Skipped content image insertion - images likely present.");
                          // Optionally count skips here too
                     }
                 }

                 // Brief pause to avoid overwhelming server/API
                 usleep(100000); // 100 milliseconds

            } catch (Exception $e) {
                $this->log("Scheduled batch (Post {$post_id}): CRITICAL ERROR during processing: " . $e->getMessage());
                $failed_count++;
            }
        }

        $duration = round(microtime(true) - $start_time, 2);
        $this->log("Scheduled batch finished in {$duration}s. Processed: {$processed_count}, Failed: {$failed_count}, Skipped: {$skipped_count}");
    }

    /**
     * Get posts without featured image. Optimized query.
     * @param int $offset Offset for pagination.
     * @param int $limit Number of posts to retrieve.
     * @return array Array of WP_Post objects.
     */
    private function get_posts_without_featured_image($offset = 0, $limit = 20) {
        global $wpdb;

        // Ensure options are loaded and valid
        $included_post_types = $this->options['included_post_types'] ?? ['post'];
        $excluded_categories = $this->options['excluded_categories'] ?? [];
        $min_content_length = absint($this->options['min_content_length'] ?? 100);

        if (empty($included_post_types)) {
             $this->log("Get Posts Without Featured: No included post types specified.");
             return [];
        }

        // Prepare post types for IN clause
        $post_types_placeholders = implode(', ', array_fill(0, count($included_post_types), '%s'));
        $params = $included_post_types;

        // Base query
        $query = "
            SELECT DISTINCT p.ID, p.post_title, p.post_content
            FROM {$wpdb->posts} p
            LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_thumbnail_id'
        ";

        // Join for category exclusion if needed
        $term_join = "";
        $term_where = "";
        if (!empty($excluded_categories)) {
            $term_join = "
                LEFT JOIN {$wpdb->term_relationships} tr ON p.ID = tr.object_id
                LEFT JOIN {$wpdb->term_taxonomy} tt ON tr.term_taxonomy_id = tt.term_taxonomy_id AND tt.taxonomy = 'category'
            ";
            $cat_placeholders = implode(', ', array_fill(0, count($excluded_categories), '%d'));
            $term_where = " AND (tt.term_id IS NULL OR tt.term_id NOT IN ($cat_placeholders))";
            $params = array_merge($params, $excluded_categories); // Add category IDs to params
        }

        $query .= $term_join;

        // WHERE clause
        $query .= "
            WHERE p.post_type IN ($post_types_placeholders)
            AND p.post_status = 'publish'
            AND (pm.meta_value IS NULL OR pm.meta_value = '' OR pm.meta_key IS NULL) /* Check for null or empty meta value */
            {$term_where} /* Add category exclusion where clause */
        ";

        // Add minimum content length check (check byte length for performance in SQL)
        if ($min_content_length > 0) {
            // Using LENGTH which is bytes, usually okay as approximation unless dealing heavily with multi-byte chars at the boundary
            $query .= $wpdb->prepare(" AND LENGTH(p.post_content) > %d", $min_content_length);
        }

        // Complete query with ordering and limit
        $query .= " ORDER BY p.post_date DESC LIMIT %d, %d";
        $params[] = absint($offset);
        $params[] = absint($limit);

        return $wpdb->get_results($wpdb->prepare($query, $params));
    }

    /**
     * Optimized function to get posts without plugin-inserted content images.
     * Checks for H2s and lack of our specific classes.
     * @param int $limit Max number of posts to return.
     * @return array Array of WP_Post objects.
     */
    public function get_posts_without_content_images($limit = 100) {
        global $wpdb;

        $included_post_types = $this->options['included_post_types'] ?? ['post'];
        $min_content_length = absint($this->options['min_content_length'] ?? 100);

        if (empty($included_post_types)) return [];

        $post_types_placeholders = implode(', ', array_fill(0, count($included_post_types), '%s'));
        $params = $included_post_types;

        // Find posts that:
        // - Are in included post types & published
        // - Have H2 tags (likely candidates for insertion)
        // - DO NOT have our plugin's image classes (uofi-image-grid, uofi-single-image, uofi-carousel)
        // - Meet minimum content length
        $query = "
            SELECT ID, post_title, post_content
            FROM {$wpdb->posts}
            WHERE post_type IN ($post_types_placeholders)
            AND post_status = 'publish'
            AND post_content LIKE '%<h2%' /* Must have H2 tags */
            AND post_content NOT LIKE '%class=\"uofi-%' /* Not already processed by us */
            AND post_content NOT LIKE '%<img%' /* Basic check for any existing images - adjust if too strict */
        ";

        if ($min_content_length > 0) {
            $query .= $wpdb->prepare(" AND LENGTH(post_content) > %d", $min_content_length);
        }

        $query .= " ORDER BY post_date DESC LIMIT %d";
        $params[] = absint($limit);

        return $wpdb->get_results($wpdb->prepare($query, $params));
    }

    /**
     * AJAX handler for getting posts without content images (for bulk tool).
     */
    public function ajax_get_posts_without_images() {
        check_ajax_referer('uofi_admin_nonce', 'nonce');
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('Permission denied', 'ultra-optimized-featured-image')]);
            return; // Use return after wp_send_json_error
        }

        // Get a larger batch for the tool to iterate through
        $posts = $this->get_posts_without_content_images(500); // Get up to 500 IDs
        $post_ids = wp_list_pluck($posts, 'ID');

        wp_send_json_success([
            'posts' => $post_ids,
            'count' => count($post_ids),
            'message' => sprintf(__('Found %d posts potentially needing content images.', 'ultra-optimized-featured-image'), count($post_ids))
        ]);
    }

     /**
     * AJAX handler for getting all applicable posts (for duplicate removal tool).
     */
    public function ajax_get_all_posts() {
        check_ajax_referer('uofi_admin_nonce', 'nonce');
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('Permission denied', 'ultra-optimized-featured-image')]);
            return;
        }

        global $wpdb;

        $included_post_types = $this->options['included_post_types'] ?? ['post'];
        if (empty($included_post_types)) {
             wp_send_json_success(['posts' => [], 'count' => 0, 'message' => __('No included post types configured.', 'ultra-optimized-featured-image')]);
             return;
        }

        $post_types_placeholders = implode(', ', array_fill(0, count($included_post_types), '%s'));
        $params = $included_post_types;

        // Get IDs of all published posts of the included types
        // Add other relevant statuses? 'private'?
        $query = "
            SELECT ID
            FROM {$wpdb->posts}
            WHERE post_type IN ($post_types_placeholders)
            AND post_status IN ('publish', 'future', 'private') /* Include statuses that might have content */
            ORDER BY post_date DESC
        ";

        $posts = $wpdb->get_col($wpdb->prepare($query, $params));
        $count = count($posts);

        wp_send_json_success([
            'posts' => $posts,
            'count' => $count,
            'message' => sprintf(__('Found %d posts to scan for duplicate images.', 'ultra-optimized-featured-image'), $count)
        ]);
    }

    /**
     * AJAX handler for removing duplicate images from a single post.
     */
    public function ajax_remove_duplicate_images() {
        check_ajax_referer('uofi_admin_nonce', 'nonce');
        if (!current_user_can('edit_posts')) { // Allow editors to run this? Or manage_options?
            wp_send_json_error(['message' => __('Permission denied', 'ultra-optimized-featured-image')]);
            return;
        }

        $post_id = isset($_POST['post_id']) ? absint($_POST['post_id']) : 0;

        if (!$post_id) {
            wp_send_json_error(['message' => __('Invalid post ID provided.', 'ultra-optimized-featured-image')]);
            return;
        }

        // Ensure the feature is enabled before running via AJAX too
        if (empty($this->options['delete_duplicate_images'])) {
             wp_send_json_error(['message' => __('Duplicate image removal feature is disabled in settings.', 'ultra-optimized-featured-image')]);
             return;
        }

        $result = $this->remove_duplicate_images($post_id);

        wp_send_json_success([
            'success' => $result, // True if duplicates were removed, false otherwise
            'post_id' => $post_id,
            'message' => $result ? __('Duplicate images removed.', 'ultra-optimized-featured-image') : __('No duplicate images found or removed.', 'ultra-optimized-featured-image')
        ]);
    }

    /**
     * AJAX handler for testing API connection.
     */
    public function ajax_test_api_connection() {
        check_ajax_referer('uofi_admin_nonce', 'nonce');
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('Permission denied', 'ultra-optimized-featured-image')]);
            return;
        }

        // Use the key provided in the AJAX request, not the saved one
        $api_key = isset($_POST['api_key']) ? sanitize_text_field(trim($_POST['api_key'])) : '';

        if (empty($api_key)) {
            wp_send_json_error(['message' => __('API Key is required.', 'ultra-optimized-featured-image')]);
            return;
        }

        // Simple test: List available models (less prone to content blocking than generateContent)
        $endpoint = $this->gemini_api_endpoint_base; // Base URL for listing models

        $response = wp_remote_get("{$endpoint}?key={$api_key}", [
            'timeout' => 15,
            'headers' => ['Accept' => 'application/json'],
            'sslverify' => apply_filters('https_local_ssl_verify', true),
        ]);

        // Handle WP connection errors
        if (is_wp_error($response)) {
            wp_send_json_error([
                'message' => __('Connection error:', 'ultra-optimized-featured-image') . ' ' . $response->get_error_message()
            ]);
            return;
        }

        // Handle API HTTP errors
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        $result = json_decode($body, true);

        if ($status_code !== 200) {
            $error_message = isset($result['error']['message']) ? $result['error']['message'] : "HTTP Error: $status_code";
            wp_send_json_error(['message' => __('API Error:', 'ultra-optimized-featured-image') . ' ' . $error_message]);
            return;
        }

        // Check if the response contains a list of models
        if (!isset($result['models']) || !is_array($result['models'])) {
            wp_send_json_error(['message' => __('Invalid API response format received.', 'ultra-optimized-featured-image')]);
            return;
        }

        // Check if desired models are supported (optional but helpful)
        $supported_generation = false;
        $supported_embedding = false;
        foreach ($result['models'] as $model) {
             if (strpos($model['name'], $this->gemini_generation_model) !== false) {
                 $supported_generation = true;
             }
             if (strpos($model['name'], $this->gemini_embedding_model) !== false) {
                 $supported_embedding = true;
             }
        }
        $extra_info = [];
        if($this->options['use_ai'] && !$supported_generation) $extra_info[] = sprintf(__("Warning: Configured generation model '%s' might not be listed.", 'ultra-optimized-featured-image'), $this->gemini_generation_model);
        if($this->options['use_ai'] && !$supported_embedding) $extra_info[] = sprintf(__("Warning: Configured embedding model '%s' might not be listed.", 'ultra-optimized-featured-image'), $this->gemini_embedding_model);


        wp_send_json_success([
            'message' => __('Connection successful! API key is valid.', 'ultra-optimized-featured-image') . ( !empty($extra_info) ? '<br/>' . implode('<br/>', $extra_info) : '')
        ]);
    }

    /**
     * AJAX handler for processing batch of featured images.
     */
    public function ajax_process_batch() {
        check_ajax_referer('uofi_admin_nonce', 'nonce');
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('Permission denied', 'ultra-optimized-featured-image')]);
            return;
        }

        $offset = isset($_POST['offset']) ? absint($_POST['offset']) : 0;
        // Smaller limit for AJAX to provide faster feedback per step
        $limit = isset($_POST['limit']) ? absint($_POST['limit']) : 10;
        $limit = max(1, min($limit, 20)); // Clamp limit

        $processed_in_batch = 0;
        $failed_in_batch = 0;
        $skipped_in_batch = 0;
        $batch_messages = [];

        // Get total count for progress calculation (use cache)
        $total_needing_featured = $this->get_cached_data('uofi_count_no_featured', [$this, 'count_posts_without_featured_image'], 300); // Cache for 5 mins

        // Get posts for this specific batch
        $posts = $this->get_posts_without_featured_image($offset, $limit);

        if (empty($posts)) {
            wp_send_json_success([
                'processed' => $processed_in_batch,
                'failed' => $failed_in_batch,
                'skipped' => $skipped_in_batch,
                'total_remaining' => 0, // No more posts found
                'total_overall' => $total_needing_featured,
                'message' => __('No more posts found needing featured images in this batch.', 'ultra-optimized-featured-image'),
                'log' => $batch_messages
            ]);
            return;
        }

        foreach ($posts as $post) {
            $post_id = $post->ID;
            if (has_post_thumbnail($post_id)) {
                 $skipped_in_batch++;
                 $batch_messages[] = "Post {$post_id}: Skipped (already has thumbnail)";
                 continue;
            }
            try {
                $success = $this->assign_featured_image($post_id); // This function logs details internally
                if ($success) {
                    $processed_in_batch++;
                    $batch_messages[] = "Post {$post_id}: Success";

                    // Optionally process content images immediately after successful featured image assignment
                    // if ($this->options['auto_insert_content_images']) {
                    //    $this->insert_content_images($post_id); // Runs based on its own background/foreground setting
                    // }
                } else {
                    $failed_in_batch++;
                    // Reason should be logged by assign_featured_image
                    $batch_messages[] = "Post {$post_id}: Failed/Skipped (see logs)";
                }
            } catch (Exception $e) {
                 $this->log("AJAX Batch Error (Post {$post_id}): " . $e->getMessage());
                 $failed_in_batch++;
                 $batch_messages[] = "Post {$post_id}: Error - " . $e->getMessage();
            }
             // Small delay? Might not be needed if API calls are cached well.
             // usleep(50000); // 50ms
        }

        // Recalculate remaining after processing this batch
        // This count might be slightly off if posts were manually updated during batch run
        $current_remaining = $this->count_posts_without_featured_image();

        wp_send_json_success([
            'processed' => $processed_in_batch,
            'failed' => $failed_in_batch,
            'skipped' => $skipped_in_batch,
            'total_remaining' => $current_remaining,
            'total_overall' => $total_needing_featured, // Initial total for progress bar
            'message' => sprintf(__('Processed %d posts in this batch.', 'ultra-optimized-featured-image'), count($posts)),
            'log' => $batch_messages
        ]);
    }

    /**
     * AJAX handler for processing a single post's featured image.
     */
    public function ajax_process_single() {
        check_ajax_referer('uofi_admin_nonce', 'nonce');
        // Allow editors to trigger single assignment?
        if (!current_user_can('edit_posts')) {
            wp_send_json_error(['message' => __('Permission denied', 'ultra-optimized-featured-image')]);
            return;
        }

        $post_id = isset($_POST['post_id']) ? absint($_POST['post_id']) : 0;

        if (!$post_id) {
            wp_send_json_error(['message' => __('Invalid post ID provided.', 'ultra-optimized-featured-image')]);
            return;
        }

        if (has_post_thumbnail($post_id)) {
             wp_send_json_error([
                'message' => __('Post already has a featured image.', 'ultra-optimized-featured-image')
             ]);
             return;
        }

        // Process featured image assignment
        $success = $this->assign_featured_image($post_id);

        if ($success) {
            $image_id = get_post_thumbnail_id($post_id);
            $image_url = $image_id ? wp_get_attachment_image_url($image_id, 'thumbnail') : null;

            wp_send_json_success([
                'message' => __('Featured image assigned successfully!', 'ultra-optimized-featured-image'),
                'image_id' => $image_id,
                'image_url' => $image_url
            ]);
        } else {
            // Failure reason should be in logs from assign_featured_image
            wp_send_json_error([
                'message' => __('Failed to assign featured image. No suitable image found or an error occurred (check logs).', 'ultra-optimized-featured-image')
            ]);
        }
    }

    /**
     * AJAX handler for inserting content images into a single post.
     */
    public function ajax_insert_content_images() {
        check_ajax_referer('uofi_admin_nonce', 'nonce');
        if (!current_user_can('edit_posts')) {
            wp_send_json_error(['message' => __('Permission denied', 'ultra-optimized-featured-image')]);
            return;
        }

        $post_id = isset($_POST['post_id']) ? absint($_POST['post_id']) : 0;

        if (!$post_id) {
            wp_send_json_error(['message' => __('Invalid post ID provided.', 'ultra-optimized-featured-image')]);
            return;
        }

        // Check if feature is enabled
        if (!$this->options['auto_insert_content_images']) {
             wp_send_json_error(['message' => __('Content image insertion is disabled in settings.', 'ultra-optimized-featured-image')]);
             return;
        }

        // Check if API is required and valid (needed if AI is used for heading images)
        if ($this->options['use_ai'] && empty($this->options['api_key'])) {
            wp_send_json_error(['message' => __('AI is enabled but no API key is configured in settings.', 'ultra-optimized-featured-image')]);
            return;
        }

        // Check if post already has images (basic check)
        $current_content = get_post_field('post_content', $post_id);
        if (strpos($current_content, 'class="uofi-') !== false || $this->content_has_general_images($current_content)) {
            wp_send_json_error(['message' => __('Post content appears to already contain images. Skipping insertion.', 'ultra-optimized-featured-image')]);
            return;
        }

        // Process content images (run immediately as this is an explicit user action)
        // The function handles locking internally if needed, but shouldn't be necessary here
        $success = $this->process_content_images_background($post_id); // Use the background function for consistency

        if ($success) {
            wp_send_json_success([
                'message' => __('Content images inserted successfully!', 'ultra-optimized-featured-image'),
                'post_id' => $post_id
            ]);
        } else {
             // Failure reason should be in logs
            wp_send_json_error([
                'message' => __('Could not insert content images. No suitable headings found, no images matched, or an error occurred (check logs).', 'ultra-optimized-featured-image'),
                'post_id' => $post_id
            ]);
        }
    }

    /**
     * Count posts without featured image. Uses optimized query. Cached.
     * @return int Count.
     */
    public function count_posts_without_featured_image() {
        global $wpdb;

        // Use cached data for counts
        return $this->get_cached_data('uofi_count_no_featured', function() use ($wpdb) {
            $included_post_types = $this->options['included_post_types'] ?? ['post'];
            $excluded_categories = $this->options['excluded_categories'] ?? [];
            $min_content_length = absint($this->options['min_content_length'] ?? 0);

            if (empty($included_post_types)) return 0;

            $post_types_placeholders = implode(', ', array_fill(0, count($included_post_types), '%s'));
            $params = $included_post_types;

            $query = "
                SELECT COUNT(DISTINCT p.ID)
                FROM {$wpdb->posts} p
                LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_thumbnail_id'
            ";

            $term_join = "";
            $term_where = "";
            if (!empty($excluded_categories)) {
                $term_join = "
                    LEFT JOIN {$wpdb->term_relationships} tr ON p.ID = tr.object_id
                    LEFT JOIN {$wpdb->term_taxonomy} tt ON tr.term_taxonomy_id = tt.term_taxonomy_id AND tt.taxonomy = 'category'
                ";
                $cat_placeholders = implode(', ', array_fill(0, count($excluded_categories), '%d'));
                $term_where = " AND (tt.term_id IS NULL OR tt.term_id NOT IN ($cat_placeholders))";
                $params = array_merge($params, $excluded_categories);
            }

            $query .= $term_join;
            $query .= "
                WHERE p.post_type IN ($post_types_placeholders)
                AND p.post_status = 'publish'
                AND (pm.meta_value IS NULL OR pm.meta_value = '' OR pm.meta_key IS NULL)
                {$term_where}
            ";

            if ($min_content_length > 0) {
                 $query .= $wpdb->prepare(" AND LENGTH(p.post_content) > %d", $min_content_length);
            }

            return (int) $wpdb->get_var($wpdb->prepare($query, $params));
        }, 300); // Cache count for 5 minutes
    }

    /**
     * Count posts with featured images. Optimized query. Cached.
     * @return int Count.
     */
    public function count_posts_with_featured_image() {
        global $wpdb;
        return $this->get_cached_data('uofi_count_with_featured', function() use ($wpdb) {
            $included_post_types = $this->options['included_post_types'] ?? ['post'];
            if (empty($included_post_types)) return 0;

            $post_types_placeholders = implode(', ', array_fill(0, count($included_post_types), '%s'));
            $params = $included_post_types;

            $query = "
                SELECT COUNT(DISTINCT p.ID)
                FROM {$wpdb->posts} p
                INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_thumbnail_id'
                WHERE p.post_type IN ($post_types_placeholders)
                AND p.post_status = 'publish'
                AND pm.meta_value IS NOT NULL AND pm.meta_value != ''
            ";

             // Exclude categories if specified (less common to exclude here, but for consistency)
             $excluded_categories = $this->options['excluded_categories'] ?? [];
             if (!empty($excluded_categories)) {
                 $term_join = "
                     LEFT JOIN {$wpdb->term_relationships} tr ON p.ID = tr.object_id
                     LEFT JOIN {$wpdb->term_taxonomy} tt ON tr.term_taxonomy_id = tt.term_taxonomy_id AND tt.taxonomy = 'category'
                 ";
                 $cat_placeholders = implode(', ', array_fill(0, count($excluded_categories), '%d'));
                 $term_where = " AND (tt.term_id IS NULL OR tt.term_id NOT IN ($cat_placeholders))";
                 $query = str_replace("WHERE", $term_join . " WHERE", $query); // Insert join
                 $query .= $term_where;
                 $params = array_merge($params, $excluded_categories);
             }


            return (int) $wpdb->get_var($wpdb->prepare($query, $params));
        }, 300); // Cache count for 5 minutes
    }

    /**
     * Count total applicable posts. Optimized query. Cached.
     * @return int Count.
     */
    public function count_total_posts() {
        global $wpdb;
         return $this->get_cached_data('uofi_count_total_posts', function() use ($wpdb) {
            $included_post_types = $this->options['included_post_types'] ?? ['post'];
            if (empty($included_post_types)) return 0;

            $post_types_placeholders = implode(', ', array_fill(0, count($included_post_types), '%s'));
            $params = $included_post_types;

            $query = "
                SELECT COUNT(DISTINCT p.ID)
                FROM {$wpdb->posts} p
                WHERE p.post_type IN ($post_types_placeholders)
                AND p.post_status = 'publish'
            ";

             // Exclude categories if specified
             $excluded_categories = $this->options['excluded_categories'] ?? [];
             if (!empty($excluded_categories)) {
                 $term_join = "
                     LEFT JOIN {$wpdb->term_relationships} tr ON p.ID = tr.object_id
                     LEFT JOIN {$wpdb->term_taxonomy} tt ON tr.term_taxonomy_id = tt.term_taxonomy_id AND tt.taxonomy = 'category'
                 ";
                 $cat_placeholders = implode(', ', array_fill(0, count($excluded_categories), '%d'));
                 $term_where = " AND (tt.term_id IS NULL OR tt.term_id NOT IN ($cat_placeholders))";
                 $query = str_replace("WHERE", $term_join . " WHERE", $query); // Insert join
                 $query .= $term_where;
                 $params = array_merge($params, $excluded_categories);
             }

            return (int) $wpdb->get_var($wpdb->prepare($query, $params));
         }, HOUR_IN_SECONDS); // Cache total count longer (1 hour)
    }

    /**
     * Register REST API routes.
     */
    public function register_rest_routes() {
        $namespace = 'ultra-optimized-featured-image/v1';

        // Assign featured image to a single post
        register_rest_route($namespace, '/assign/(?P<id>\d+)', [
            'methods' => WP_REST_Server::CREATABLE, // POST
            'callback' => [$this, 'rest_assign_featured_image'],
            'permission_callback' => function() {
                return current_user_can('edit_post', $request['id'] ?? 0); // Check capability for specific post
            },
            'args' => [
                'id' => [
                    'validate_callback' => function($param, $request, $key) { return is_numeric($param) && $param > 0; },
                    'required' => true,
                    'description' => __('The ID of the post to assign image to.', 'ultra-optimized-featured-image'),
                ],
            ],
        ]);

        // Insert content images into a single post
        register_rest_route($namespace, '/insert-content/(?P<id>\d+)', [
            'methods' => WP_REST_Server::CREATABLE, // POST
            'callback' => [$this, 'rest_insert_content_images'],
            'permission_callback' => function($request) {
                 return current_user_can('edit_post', $request['id'] ?? 0);
            },
             'args' => [
                'id' => [
                    'validate_callback' => function($param, $request, $key) { return is_numeric($param) && $param > 0; },
                    'required' => true,
                    'description' => __('The ID of the post to insert content images into.', 'ultra-optimized-featured-image'),
                ],
            ],
        ]);

        // Get plugin stats
        register_rest_route($namespace, '/stats', [
            'methods' => WP_REST_Server::READABLE, // GET
            'callback' => [$this, 'rest_get_stats'],
            'permission_callback' => function() {
                return current_user_can('manage_options'); // Only admins can see overall stats?
            }
        ]);

         // Remove duplicate images from a single post
        register_rest_route($namespace, '/remove-duplicates/(?P<id>\d+)', [
            'methods' => WP_REST_Server::CREATABLE, // POST
            'callback' => [$this, 'rest_remove_duplicate_images'],
            'permission_callback' => function($request) {
                 return current_user_can('edit_post', $request['id'] ?? 0);
            },
             'args' => [
                'id' => [
                    'validate_callback' => function($param, $request, $key) { return is_numeric($param) && $param > 0; },
                    'required' => true,
                    'description' => __('The ID of the post to remove duplicate images from.', 'ultra-optimized-featured-image'),
                ],
            ],
        ]);
    }

    /**
     * REST API handler for assigning featured image.
     * @param WP_REST_Request $request Request object.
     * @return WP_REST_Response|WP_Error Response object or error.
     */
    public function rest_assign_featured_image($request) {
        $post_id = absint($request['id']);
        $post = get_post($post_id);

        if (!$post || !in_array($post->post_type, $this->options['included_post_types'])) {
             return new WP_Error('rest_post_invalid', __('Invalid post ID or type.', 'ultra-optimized-featured-image'), ['status' => 404]);
        }
         if (has_post_thumbnail($post_id)) {
             return new WP_Error('rest_already_has_thumbnail', __('Post already has a featured image.', 'ultra-optimized-featured-image'), ['status' => 400]);
         }


        $success = $this->assign_featured_image($post_id);

        if ($success) {
            $image_id = get_post_thumbnail_id($post_id);
            $image_url = $image_id ? wp_get_attachment_image_url($image_id, 'thumbnail') : null;
            return new WP_REST_Response([
                'success' => true,
                'message' => __('Featured image assigned successfully.', 'ultra-optimized-featured-image'),
                'image_id' => $image_id,
                'image_url' => $image_url
            ], 200);
        } else {
             // Failure reason logged internally
            return new WP_Error('rest_assign_failed', __('Failed to assign featured image. No suitable image found or error occurred.', 'ultra-optimized-featured-image'), ['status' => 500]);
        }
    }

    /**
     * REST API handler for inserting content images.
     * @param WP_REST_Request $request Request object.
     * @return WP_REST_Response|WP_Error Response object or error.
     */
    public function rest_insert_content_images($request) {
        $post_id = absint($request['id']);
        $post = get_post($post_id);

        if (!$post || !in_array($post->post_type, $this->options['included_post_types'])) {
             return new WP_Error('rest_post_invalid', __('Invalid post ID or type.', 'ultra-optimized-featured-image'), ['status' => 404]);
        }

         // Check feature enabled
        if (!$this->options['auto_insert_content_images']) {
             return new WP_Error('rest_feature_disabled', __('Content image insertion is disabled.', 'ultra-optimized-featured-image'), ['status' => 400]);
        }

        // Check if content has images already
        $content = $post->post_content;
        if (strpos($content, 'class="uofi-') !== false || $this->content_has_general_images($content)) {
             return new WP_Error('rest_content_has_images', __('Post content appears to already contain images.', 'ultra-optimized-featured-image'), ['status' => 400]);
        }

        // Run the insertion logic (foreground for REST API call)
        $success = $this->process_content_images_background($post_id);

        if ($success) {
            return new WP_REST_Response([
                'success' => true,
                'message' => __('Content images inserted successfully.', 'ultra-optimized-featured-image'),
                'post_id' => $post_id
            ], 200);
        } else {
             // Failure reason logged internally
            return new WP_Error('rest_insert_failed', __('Could not insert content images. Check logs for details.', 'ultra-optimized-featured-image'), ['status' => 500]);
        }
    }

    /**
     * REST API handler for removing duplicate images.
     * @param WP_REST_Request $request Request object.
     * @return WP_REST_Response|WP_Error Response object or error.
     */
    public function rest_remove_duplicate_images($request) {
        $post_id = absint($request['id']);
        $post = get_post($post_id);

        if (!$post || !in_array($post->post_type, $this->options['included_post_types'])) {
             return new WP_Error('rest_post_invalid', __('Invalid post ID or type.', 'ultra-optimized-featured-image'), ['status' => 404]);
        }

         // Check feature enabled
        if (empty($this->options['delete_duplicate_images'])) {
             return new WP_Error('rest_feature_disabled', __('Duplicate image removal is disabled.', 'ultra-optimized-featured-image'), ['status' => 400]);
        }

        $success = $this->remove_duplicate_images($post_id);

        if ($success) {
            return new WP_REST_Response([
                'success' => true,
                'message' => __('Duplicate images removed successfully (if any were found).', 'ultra-optimized-featured-image'),
                'post_id' => $post_id
            ], 200);
        } else {
             // Failure reason logged internally (or no duplicates found)
            return new WP_REST_Response([ // Not necessarily an error if no duplicates were found
                'success' => false, // Indicate no changes were made
                'message' => __('No duplicate images found or failed to update post.', 'ultra-optimized-featured-image'),
                'post_id' => $post_id
            ], 200); // Return 200 OK even if no duplicates found
        }
    }


    /**
     * REST API handler for getting stats.
     * @return WP_REST_Response Response object.
     */
    public function rest_get_stats() {
        // Use cached counts for performance via the helper functions
        $total_posts = $this->count_total_posts();
        $posts_with_featured = $this->count_posts_with_featured_image();
        $posts_without_featured = $this->count_posts_without_featured_image(); // Recalculates based on current state
        $percentage = $total_posts > 0 ? round(($posts_with_featured / $total_posts) * 100) : 0;

        return new WP_REST_Response([
            'total_posts' => $total_posts,
            'posts_with_featured' => $posts_with_featured,
            'posts_without_featured' => $posts_without_featured,
            'percentage' => $percentage
        ], 200);
    }

    /**
     * Render main admin page (Dashboard/Bulk Actions).
     */
    public function render_admin_page() {
        // Get fresh stats for the page load
        $total_posts = $this->count_total_posts();
        $posts_with_featured = $this->count_posts_with_featured_image();
        $posts_without_featured = $this->count_posts_without_featured_image();
        $percentage = $total_posts > 0 ? round(($posts_with_featured / $total_posts) * 100) : 0;

        ?>
        <div class="wrap uofi-admin">
            <h1><?php esc_html_e('Ultra Optimized Featured Image Assigner', 'ultra-optimized-featured-image'); ?></h1>

            <div class="uofi-dashboard">
                <!-- Stats Section -->
                <div class="uofi-section uofi-stats-section">
                    <h2 class="uofi-section-title"><?php esc_html_e('Image Coverage Status', 'ultra-optimized-featured-image'); ?></h2>
                    <div class="uofi-stats-cards">
                        <div class="uofi-stat-card">
                            <div class="uofi-stat-number"><?php echo number_format_i18n($total_posts); ?></div>
                            <div class="uofi-stat-label"><?php esc_html_e('Total Posts (Applicable Types)', 'ultra-optimized-featured-image'); ?></div>
                        </div>
                        <div class="uofi-stat-card">
                            <div class="uofi-stat-number"><?php echo number_format_i18n($posts_with_featured); ?></div>
                            <div class="uofi-stat-label"><?php esc_html_e('With Featured Images', 'ultra-optimized-featured-image'); ?></div>
                        </div>
                        <div class="uofi-stat-card">
                            <div class="uofi-stat-number"><?php echo number_format_i18n($posts_without_featured); ?></div>
                            <div class="uofi-stat-label"><?php esc_html_e('Missing Featured Images', 'ultra-optimized-featured-image'); ?></div>
                        </div>
                         <div class="uofi-stat-card">
                             <div class="uofi-stat-label"><?php esc_html_e('Coverage', 'ultra-optimized-featured-image'); ?></div>
                             <div class="uofi-progress-bar-container" style="width: 100%; background: #eee; border-radius: 4px; height: 10px; margin-top: 5px; overflow:hidden;">
                                 <div class="uofi-progress-bar-fill" style="width: <?php echo $percentage; ?>%; background: #2271b1; height: 100%;"></div>
                             </div>
                             <div class="uofi-stat-number" style="text-align: center; margin-top: 5px; font-size: 1.2em;"><?php echo $percentage; ?>%</div>
                         </div>
                    </div>
                </div>

                <!-- Bulk Actions Section -->
                <div class="uofi-section uofi-action-section">
                     <h2 class="uofi-section-title"><?php esc_html_e('Bulk Actions', 'ultra-optimized-featured-image'); ?></h2>
                     <div class="uofi-action-cards">
                        <div class="uofi-card">
                            <h3><?php esc_html_e('Bulk Assign Featured Images', 'ultra-optimized-featured-image'); ?></h3>
                            <p><?php esc_html_e('Scan posts missing featured images and attempt to assign the best match based on your settings (AI or keywords).', 'ultra-optimized-featured-image'); ?></p>
                             <p><strong><?php echo number_format_i18n($posts_without_featured); ?></strong> <?php esc_html_e('posts currently need featured images.', 'ultra-optimized-featured-image'); ?></p>

                            <button id="uofi-process-featured" class="button button-primary" <?php disabled($posts_without_featured === 0); ?>
                                <?php esc_html_e('Start Bulk Assignment', 'ultra-optimized-featured-image'); ?>
                            </button>
                            <button id="uofi-stop-featured" class="button button-secondary" style="display:none;">
                                <?php esc_html_e('Stop Processing', 'ultra-optimized-featured-image'); ?>
                            </button>

                            <div id="uofi-featured-progress" class="uofi-progress-container" style="display: none; margin-top: 15px;">
                                <div class="uofi-progress-bar">
                                    <div class="uofi-progress-fill"></div>
                                </div>
                                <div class="uofi-progress-text">0%</div>
                                <div class="uofi-progress-log" style="font-size: 0.9em; max-height: 100px; overflow-y: auto; background: #f9f9f9; border: 1px solid #eee; padding: 5px; margin-top: 5px;"></div>
                            </div>
                        </div>

                        <div class="uofi-card">
                            <h3><?php esc_html_e('Bulk Insert Content Images', 'ultra-optimized-featured-image'); ?></h3>
                            <p><?php esc_html_e('Scan posts that contain H2 headings but lack images, and insert relevant images based on your settings.', 'ultra-optimized-featured-image'); ?></p>
                             <p><?php esc_html_e('Note: This only processes posts without existing images to avoid duplication.', 'ultra-optimized-featured-image'); ?></p>

                            <button id="uofi-process-content" class="button button-primary">
                                <?php esc_html_e('Start Content Image Insertion', 'ultra-optimized-featured-image'); ?>
                            </button>
                             <button id="uofi-stop-content" class="button button-secondary" style="display:none;">
                                <?php esc_html_e('Stop Processing', 'ultra-optimized-featured-image'); ?>
                            </button>

                            <div id="uofi-content-progress" class="uofi-progress-container" style="display: none; margin-top: 15px;">
                                <div class="uofi-progress-bar">
                                    <div class="uofi-progress-fill"></div>
                                </div>
                                <div class="uofi-progress-text">0%</div>
                                <div class="uofi-progress-log" style="font-size: 0.9em; max-height: 100px; overflow-y: auto; background: #f9f9f9; border: 1px solid #eee; padding: 5px; margin-top: 5px;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity Section -->
                <div class="uofi-section uofi-recent-section">
                    <h2 class="uofi-section-title"><?php esc_html_e('Recent Activity Log', 'ultra-optimized-featured-image'); ?></h2>
                     <div class="uofi-card">
                        <?php $this->render_recent_activity(); ?>
                     </div>
                </div>
            </div>

            <?php // Add styles directly here or enqueue properly ?>
            <style>
                .uofi-admin .uofi-dashboard { display: grid; grid-template-columns: 1fr; gap: 20px; }
                .uofi-section { margin-bottom: 20px; }
                .uofi-section-title { font-size: 1.2em; margin-bottom: 15px; padding-bottom: 5px; border-bottom: 1px solid #ddd; }
                .uofi-card { background: #fff; border: 1px solid #ccd0d4; border-radius: 4px; padding: 20px; }
                .uofi-stats-cards, .uofi-action-cards { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; }
                .uofi-stat-card { padding: 15px; text-align: center; background: #f9f9f9; border: 1px solid #eee; border-radius: 4px; }
                .uofi-stat-number { font-size: 1.8em; font-weight: 600; color: #2271b1; }
                .uofi-stat-label { font-size: 0.9em; color: #555; margin-top: 5px; }
                .uofi-progress-container { margin-top: 15px; }
                .uofi-progress-bar { height: 10px; background-color: #f0f0f0; border-radius: 5px; overflow: hidden; }
                .uofi-progress-fill { height: 100%; background-color: #2271b1; width: 0%; transition: width 0.3s ease; border-radius: 5px; }
                .uofi-progress-text { text-align: center; margin-top: 5px; font-weight: bold; }
                .uofi-progress-log { font-size: 0.9em; max-height: 100px; overflow-y: auto; background: #f9f9f9; border: 1px solid #eee; padding: 5px; margin-top: 5px; line-height: 1.4; }
                .uofi-activity-table { margin-top: 0; }
                .uofi-activity-table th { text-align: left; }
                .uofi-activity-table td { vertical-align: middle; }
                .uofi-log-status-success { color: green; }
                .uofi-log-status-failed { color: red; }
                .uofi-log-status-skipped { color: orange; }
                @media (min-width: 960px) {
                    .uofi-admin .uofi-dashboard { grid-template-columns: 1fr 1fr; }
                    .uofi-stats-section { grid-column: 1 / -1; } /* Span full width */
                    .uofi-action-section { grid-column: 1 / 2; }
                    .uofi-recent-section { grid-column: 2 / 3; }
                }
            </style>

            <?php // Inline JS for AJAX processing - Consider moving to admin.js ?>
            <script>
                jQuery(document).ready(function($) {
                    var stopProcessingFeatured = false;
                    var stopProcessingContent = false;
                    var xhrFeatured, xhrContent; // To hold AJAX requests

                    // --- Featured Images Bulk Processing ---
                    $('#uofi-process-featured').on('click', function() {
                        var $button = $(this);
                        var $stopButton = $('#uofi-stop-featured');
                        var $progressContainer = $('#uofi-featured-progress');
                        var $fill = $progressContainer.find('.uofi-progress-fill');
                        var $text = $progressContainer.find('.uofi-progress-text');
                        var $log = $progressContainer.find('.uofi-progress-log');

                        $button.prop('disabled', true).hide();
                        $stopButton.prop('disabled', false).show();
                        $progressContainer.show();
                        $log.html(''); // Clear log
                        stopProcessingFeatured = false;
                        var initialTotal = parseInt($('.uofi-stat-card:contains("Missing") .uofi-stat-number').text().replace(/,/g, ''), 10) || 0;
                        var processedCount = 0;
                        var failedCount = 0;
                        var skippedCount = 0;

                        function updateProgress() {
                            var processedTotal = processedCount + failedCount + skippedCount;
                            var percent = initialTotal > 0 ? Math.min(100, Math.round((processedTotal / initialTotal) * 100)) : 100;
                            $fill.css('width', percent + '%');
                            $text.text(percent + '% (' + processedTotal + '/' + initialTotal + ')');
                        }

                        function addLog(message) {
                            $log.append(message + '<br>');
                            $log.scrollTop($log[0].scrollHeight); // Scroll to bottom
                        }

                        function processBatch(offset) {
                            if (stopProcessingFeatured) {
                                addLog('Processing stopped by user.');
                                finalizeProcessing();
                                return;
                            }

                            addLog('Requesting batch starting at offset ' + offset + '...');
                            xhrFeatured = $.ajax({
                                url: uofiData.ajaxUrl,
                                type: 'POST',
                                data: {
                                    action: 'uofi_process_batch',
                                    nonce: uofiData.nonce,
                                    offset: offset,
                                    limit: 10 // Process in batches of 10 via AJAX
                                },
                                success: function(response) {
                                    if (response.success) {
                                        processedCount += response.data.processed || 0;
                                        failedCount += response.data.failed || 0;
                                        skippedCount += response.data.skipped || 0;
                                        var batchProcessedTotal = (response.data.processed || 0) + (response.data.failed || 0) + (response.data.skipped || 0);

                                        if(response.data.log && Array.isArray(response.data.log)) {
                                            response.data.log.forEach(function(msg) { addLog('- ' + msg); });
                                        } else {
                                            addLog('Processed ' + batchProcessedTotal + ' items in batch.');
                                        }

                                        updateProgress();

                                        // Check if there are more posts remaining based on API response
                                        if (response.data.total_remaining > 0 && batchProcessedTotal > 0) {
                                            // Continue with the next batch (offset should be managed server-side ideally,
                                            // but client-side approx is ok for simple progress)
                                            processBatch(offset + batchProcessedTotal);
                                        } else {
                                            addLog('Finished processing or no more posts found.');
                                            finalizeProcessing();
                                        }
                                    } else {
                                        addLog(uofiData.error + ' ' + (response.data.message || uofiData.generic_error));
                                        finalizeProcessing();
                                    }
                                },
                                error: function(jqXHR, textStatus, errorThrown) {
                                     if (textStatus === 'abort') {
                                         addLog('Processing aborted.');
                                     } else {
                                         addLog(uofiData.error + ' Server error: ' + textStatus);
                                         console.error("AJAX Error:", textStatus, errorThrown, jqXHR.responseText);
                                     }
                                    finalizeProcessing();
                                }
                            });
                        }

                        function finalizeProcessing() {
                            $button.prop('disabled', false).show();
                            $stopButton.hide();
                            stopProcessingFeatured = true; // Ensure it's stopped
                            // Optionally reload page or update stats after a delay
                            // setTimeout(function(){ window.location.reload(); }, 3000);
                        }

                        updateProgress(); // Initial display
                        processBatch(0); // Start the first batch
                    });

                    $('#uofi-stop-featured').on('click', function() {
                        $(this).prop('disabled', true);
                        stopProcessingFeatured = true;
                        if (xhrFeatured) {
                             xhrFeatured.abort(); // Attempt to abort current AJAX request
                        }
                        $('#uofi-featured-progress .uofi-progress-log').append('Stopping processing...<br>');
                    });

                    // --- Content Images Bulk Processing ---
                    $('#uofi-process-content').on('click', function() {
                        var $button = $(this);
                        var $stopButton = $('#uofi-stop-content');
                        var $progressContainer = $('#uofi-content-progress');
                        var $fill = $progressContainer.find('.uofi-progress-fill');
                        var $text = $progressContainer.find('.uofi-progress-text');
                        var $log = $progressContainer.find('.uofi-progress-log');
                        var allPosts = [];
                        var totalPosts = 0;
                        var currentPostIndex = 0;
                        var processedCount = 0;
                        var failedCount = 0;
                        var skippedCount = 0; // Count skips (e.g., already has images)

                        $button.prop('disabled', true).hide();
                        $stopButton.prop('disabled', false).show();
                        $progressContainer.show();
                        $log.html('');
                        stopProcessingContent = false;

                         function updateProgress() {
                            var processedTotal = currentPostIndex; // Track index as progress marker
                            var percent = totalPosts > 0 ? Math.min(100, Math.round((processedTotal / totalPosts) * 100)) : 100;
                            $fill.css('width', percent + '%');
                            $text.text(percent + '% (' + processedTotal + '/' + totalPosts + ')');
                         }

                         function addLog(message) {
                            $log.append(message + '<br>');
                            $log.scrollTop($log[0].scrollHeight);
                         }

                        function processNextPost() {
                            if (stopProcessingContent || currentPostIndex >= totalPosts) {
                                addLog(stopProcessingContent ? 'Processing stopped by user.' : 'Finished processing all posts.');
                                finalizeProcessing();
                                return;
                            }

                            var postId = allPosts[currentPostIndex];
                            addLog('Processing post ID: ' + postId + ' (' + (currentPostIndex + 1) + '/' + totalPosts + ')');

                            xhrContent = $.ajax({
                                url: uofiData.ajaxUrl,
                                type: 'POST',
                                data: {
                                    action: 'uofi_insert_content_images',
                                    nonce: uofiData.nonce,
                                    post_id: postId
                                },
                                success: function(response) {
                                    if (response.success) {
                                        addLog('- Post ' + postId + ': Success - ' + (response.data.message || 'Images inserted.'));
                                        processedCount++;
                                    } else {
                                         // Distinguish errors from skips (like already having images)
                                         if (response.data && response.data.message && response.data.message.includes('already contain images')) {
                                              addLog('- Post ' + postId + ': Skipped - ' + response.data.message);
                                              skippedCount++;
                                         } else {
                                              addLog('- Post ' + postId + ': Failed/Skipped - ' + (response.data.message || uofiData.generic_error));
                                              failedCount++;
                                         }
                                    }
                                },
                                error: function(jqXHR, textStatus, errorThrown) {
                                    if (textStatus === 'abort') {
                                         addLog('- Post ' + postId + ': Aborted.');
                                     } else {
                                         addLog('- Post ' + postId + ': Server Error - ' + textStatus);
                                         console.error("AJAX Error:", textStatus, errorThrown, jqXHR.responseText);
                                         failedCount++;
                                     }
                                },
                                complete: function() {
                                    currentPostIndex++;
                                    updateProgress();
                                    // Process next post immediately (can add delay if needed)
                                    setTimeout(processNextPost, 50); // Small delay between posts
                                }
                            });
                        }

                        function finalizeProcessing() {
                            $button.prop('disabled', false).show();
                            $stopButton.hide();
                            stopProcessingContent = true; // Ensure stopped
                             addLog('Summary: Processed ' + processedCount + ', Failed/Other Skip ' + failedCount + ', Skipped (Existing Img) ' + skippedCount);
                        }

                        // 1. Get the list of posts first
                        addLog('Fetching list of posts needing content images...');
                        $.ajax({
                            url: uofiData.ajaxUrl,
                            type: 'POST',
                            data: {
                                action: 'uofi_get_posts_without_images',
                                nonce: uofiData.nonce
                            },
                            success: function(response) {
                                if (response.success) {
                                    allPosts = response.data.posts;
                                    totalPosts = response.data.count;
                                    addLog('Found ' + totalPosts + ' posts to process.');
                                    if (totalPosts > 0) {
                                        updateProgress();
                                        processNextPost(); // Start processing the first post
                                    } else {
                                        addLog('No posts found requiring content images.');
                                        finalizeProcessing();
                                    }
                                } else {
                                    addLog(uofiData.error + ' ' + (response.data.message || 'Could not fetch post list.'));
                                    finalizeProcessing();
                                }
                            },
                            error: function() {
                                addLog(uofiData.error + ' Failed to fetch post list.');
                                finalizeProcessing();
                            }
                        });
                    });

                     $('#uofi-stop-content').on('click', function() {
                        $(this).prop('disabled', true);
                        stopProcessingContent = true;
                        if (xhrContent) {
                            xhrContent.abort(); // Attempt to abort current AJAX request
                        }
                        $('#uofi-content-progress .uofi-progress-log').append('Stopping processing...<br>');
                    });

                });
            </script>
        </div>
        <?php
    }

    /**
     * Render recent activity log in the dashboard. Optimized query.
     */
    private function render_recent_activity() {
        global $wpdb;

        $log_table = $wpdb->prefix . 'uofi_assignment_logs';

        // Check if table exists
        static $log_table_exists_render = null;
         if ($log_table_exists_render === null) {
             $log_table_exists_render = $wpdb->get_var("SHOW TABLES LIKE '$log_table'") === $log_table;
         }

        if (!$log_table_exists_render) {
            echo '<p>' . esc_html__('Log table not found. Activity logging might be disabled or installation failed.', 'ultra-optimized-featured-image') . '</p>';
            return;
        }

        // Get recent logs (Limit to e.g., 15)
        $logs = $wpdb->get_results( $wpdb->prepare(
            "SELECT l.*, p.post_title, COALESCE(a.post_title, 'N/A') as image_title
            FROM $log_table l
            LEFT JOIN {$wpdb->posts} p ON l.post_id = p.ID
            LEFT JOIN {$wpdb->posts} a ON l.image_id = a.ID /* Left join in case image was deleted */
            ORDER BY l.created_at DESC
            LIMIT %d",
            15 // Number of log entries to show
        ));

        if (empty($logs)) {
            echo '<p>' . esc_html__('No recent activity recorded.', 'ultra-optimized-featured-image') . '</p>';
            return;
        }

        echo '<table class="widefat striped uofi-activity-table">';
        echo '<thead>';
        echo '<tr>';
        echo '<th>' . esc_html__('Post', 'ultra-optimized-featured-image') . '</th>';
        echo '<th>' . esc_html__('Image', 'ultra-optimized-featured-image') . '</th>';
        echo '<th>' . esc_html__('Status', 'ultra-optimized-featured-image') . '</th>';
        echo '<th>' . esc_html__('Method', 'ultra-optimized-featured-image') . '</th>';
        echo '<th>' . esc_html__('Date', 'ultra-optimized-featured-image') . '</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';

        foreach ($logs as $log) {
             $status_class = 'uofi-log-status-' . sanitize_html_class($log->status);
             $post_link = $log->post_title ? get_edit_post_link($log->post_id) : '#';
             $post_title_display = $log->post_title ? esc_html($log->post_title) : ('Post ID: ' . $log->post_id . ' (Deleted?)');
             $image_link = $log->image_id ? get_edit_post_link($log->image_id) : '#'; // Link to edit image?
             $image_title_display = ($log->image_id && $log->image_title !== 'N/A') ? esc_html($log->image_title) : ($log->image_id ? 'Image ID: ' . $log->image_id : 'N/A');

            echo '<tr>';
            echo '<td><a href="' . esc_url($post_link) . '">' . $post_title_display . '</a></td>';
            echo '<td>' . ($log->image_id ? ('<a href="' . esc_url($image_link) . '">' . $image_title_display . '</a>') : 'N/A') . '</td>';
            echo '<td class="' . esc_attr($status_class) . '">' . esc_html(ucfirst($log->status)) . '</td>';
            echo '<td>' . esc_html(strtoupper($log->method)) . '</td>';
            echo '<td title="' . esc_attr(get_date_from_gmt($log->created_at, 'Y-m-d H:i:s')) . '">' . human_time_diff(strtotime($log->created_at), current_time('timestamp')) . ' ' . esc_html__('ago', 'ultra-optimized-featured-image') . '</td>';
            echo '</tr>';
            // Optionally add row for message on failure/skip
            if ($log->status !== 'success' && !empty($log->message)) {
                 echo '<tr class="uofi-log-details"><td colspan="5" style="font-size:0.9em; color:#777; padding-left: 20px;">↳ ' . esc_html($log->message) . '</td></tr>';
            }
        }

        echo '</tbody>';
        echo '</table>';
         // Link to tools page for full log viewer? (Not implemented here)
         // echo '<p><a href="' . admin_url('admin.php?page=ultra-optimized-featured-image-tools#logs') . '">' . __('View Full Log', 'ultra-optimized-featured-image') . '</a></p>';
    }


    /**
     * Render settings page with improved organization and help text.
     */
    public function render_settings_page() {
        // Check if settings were updated to show notice
        $updated = isset($_GET['settings-updated']) && $_GET['settings-updated'] == 'true';

        ?>
        <div class="wrap uofi-admin">
            <h1><?php esc_html_e('Ultra Optimized Featured Image - Settings', 'ultra-optimized-featured-image'); ?></h1>

            <?php if ($updated): ?>
                <div id="setting-error-settings_updated" class="notice notice-success settings-error is-dismissible">
                    <p><strong><?php esc_html_e('Settings saved.', 'ultra-optimized-featured-image'); ?></strong></p>
                </div>
            <?php endif; ?>

            <form method="post" action="options.php" class="uofi-settings-form">
                <?php settings_fields('uofi_options_group'); ?>
                <?php // No need for do_settings_sections here as we render manually ?>

                <div class="uofi-settings-grid">
                    <!-- General Settings -->
                    <div class="uofi-settings-card">
                        <h2><span class="dashicons dashicons-admin-generic"></span> <?php esc_html_e('General Settings', 'ultra-optimized-featured-image'); ?></h2>
                        <table class="form-table" role="presentation">
                            <tr>
                                <th scope="row"><?php esc_html_e('Auto Assign Featured Images', 'ultra-optimized-featured-image'); ?></th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="uofi_options[auto_assign]" value="1" <?php checked(1, $this->options['auto_assign']); ?> />
                                        <?php esc_html_e('Automatically assign featured images when publishing/updating posts that lack one.', 'ultra-optimized-featured-image'); ?>
                                    </label>
                                     <p class="description"><?php esc_html_e('Uses selected method (AI/Keyword) to find and set an image.', 'ultra-optimized-featured-image'); ?></p>
                                </td>
                            </tr>
                             <tr>
                                <th scope="row"><?php esc_html_e('Auto Insert Content Images', 'ultra-optimized-featured-image'); ?></th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="uofi_options[auto_insert_content_images]" value="1" <?php checked(1, $this->options['auto_insert_content_images']); ?> />
                                        <?php esc_html_e('Automatically insert images into post content when publishing/updating.', 'ultra-optimized-featured-image'); ?>
                                    </label>
                                     <p class="description"><?php esc_html_e('Only affects posts with H2 headings and no existing images. Uses settings below.', 'ultra-optimized-featured-image'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e('Remove Duplicate Images', 'ultra-optimized-featured-image'); ?></th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="uofi_options[delete_duplicate_images]" value="1" <?php checked(1, $this->options['delete_duplicate_images']); ?> />
                                        <?php esc_html_e('Automatically remove duplicate images within post content on save/update.', 'ultra-optimized-featured-image'); ?>
                                    </label>
                                    <p class="description"><?php esc_html_e('Compares image `src` attributes. Keeps the first instance, removes subsequent identical ones.', 'ultra-optimized-featured-image'); ?></p>
                                </td>
                            </tr>
                             <tr>
                                <th scope="row"><?php esc_html_e('Background Processing', 'ultra-optimized-featured-image'); ?></th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="uofi_options[background_processing]" value="1" <?php checked(1, $this->options['background_processing']); ?> />
                                        <?php esc_html_e('Use background tasks (WP Cron) for automatic assignments/insertions.', 'ultra-optimized-featured-image'); ?>
                                    </label>
                                     <p class="description"><?php esc_html_e('Recommended for performance. Tasks run shortly after post save. Disable for immediate processing (can slow down saving).', 'ultra-optimized-featured-image'); ?></p>
                                </td>
                            </tr>
                             <tr>
                                <th scope="row"><?php esc_html_e('Enable Logging', 'ultra-optimized-featured-image'); ?></th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="uofi_options[enable_logging]" value="1" <?php checked(1, $this->options['enable_logging']); ?> />
                                        <?php esc_html_e('Log detailed plugin activities (assignments, errors) to PHP error log and database.', 'ultra-optimized-featured-image'); ?>
                                    </label>
                                     <p class="description"><?php esc_html_e('Useful for debugging issues. Can generate many logs.', 'ultra-optimized-featured-image'); ?></p>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <!-- AI Settings -->
                    <div class="uofi-settings-card">
                        <h2><span class="dashicons dashicons-networking"></span> <?php esc_html_e('AI Settings (Google Gemini)', 'ultra-optimized-featured-image'); ?></h2>
                        <table class="form-table" role="presentation">
                            <tr>
                                <th scope="row"><?php esc_html_e('Use AI Matching', 'ultra-optimized-featured-image'); ?></th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="uofi_options[use_ai]" value="1" <?php checked(1, $this->options['use_ai']); ?> />
                                        <?php esc_html_e('Use Gemini AI for more relevant image selection (requires API key).', 'ultra-optimized-featured-image'); ?>
                                    </label>
                                     <p class="description"><?php esc_html_e('If disabled or API key is missing, falls back to basic keyword matching.', 'ultra-optimized-featured-image'); ?></p>
                                </td>
                            </tr>
                             <tr class="uofi-ai-setting <?php echo $this->options['use_ai'] ? '' : 'hidden'; ?>">
                                <th scope="row"><label for="uofi-api-key"><?php esc_html_e('Gemini API Key', 'ultra-optimized-featured-image'); ?></label></th>
                                <td>
                                    <input type="password" name="uofi_options[api_key]" id="uofi-api-key" value="<?php echo esc_attr($this->options['api_key']); ?>" class="regular-text" />
                                    <button type="button" id="uofi-toggle-api-key" class="button button-small" aria-label="<?php esc_attr_e('Show/Hide API Key', 'ultra-optimized-featured-image'); ?>"><span class="dashicons dashicons-visibility"></span></button>
                                    <button type="button" id="uofi-test-api" class="button" style="margin-left: 5px;"><?php esc_html_e('Test Connection', 'ultra-optimized-featured-image'); ?></button>
                                    <p class="description"><?php printf(__('Enter your API key from %s. Ensure it has access to the Gemini API.', 'ultra-optimized-featured-image'), '<a href="https://aistudio.google.com/app/apikey" target="_blank" rel="noopener">Google AI Studio</a>'); ?></p>
                                    <div id="uofi-api-result" style="margin-top: 10px;"></div>
                                </td>
                            </tr>
                             <?php /* Future: Add model selection dropdowns here */ ?>
                        </table>
                    </div>

                    <!-- Content Settings -->
                    <div class="uofi-settings-card">
                        <h2><span class="dashicons dashicons-admin-post"></span> <?php esc_html_e('Content Targeting', 'ultra-optimized-featured-image'); ?></h2>
                        <table class="form-table" role="presentation">
                             <tr>
                                <th scope="row"><?php esc_html_e('Included Post Types', 'ultra-optimized-featured-image'); ?></th>
                                <td>
                                    <?php
                                    $post_types = get_post_types(['public' => true], 'objects');
                                    $included_types = $this->options['included_post_types'];
                                    foreach ($post_types as $post_type) {
                                        if ($post_type->name == 'attachment') continue; // Skip attachments
                                        ?>
                                        <label style="display: block; margin-bottom: 5px;">
                                            <input type="checkbox" name="uofi_options[included_post_types][]" value="<?php echo esc_attr($post_type->name); ?>" <?php checked(in_array($post_type->name, $included_types)); ?> />
                                            <?php echo esc_html($post_type->labels->name); ?> (<code><?php echo esc_html($post_type->name); ?></code>)
                                        </label>
                                        <?php
                                    }
                                    ?>
                                     <p class="description"><?php esc_html_e('Select post types where the plugin should operate.', 'ultra-optimized-featured-image'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e('Excluded Categories', 'ultra-optimized-featured-image'); ?></th>
                                <td>
                                    <div style="max-height: 150px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">
                                    <?php
                                    $categories = get_categories(['hide_empty' => false, 'orderby' => 'name']);
                                     $excluded_cats = $this->options['excluded_categories'];
                                    if (empty($categories)) {
                                         echo '<p>' . esc_html__('No categories found.', 'ultra-optimized-featured-image') . '</p>';
                                    } else {
                                        foreach ($categories as $category) {
                                            ?>
                                            <label style="display: block; margin-bottom: 5px;">
                                                <input type="checkbox" name="uofi_options[excluded_categories][]" value="<?php echo esc_attr($category->term_id); ?>" <?php checked(in_array($category->term_id, $excluded_cats)); ?> />
                                                <?php echo esc_html($category->name); ?> (ID: <?php echo esc_html($category->term_id); ?>)
                                            </label>
                                            <?php
                                        }
                                    }
                                    ?>
                                    </div>
                                     <p class="description"><?php esc_html_e('Posts in selected categories will be ignored by the plugin.', 'ultra-optimized-featured-image'); ?></p>
                                </td>
                            </tr>
                             <tr>
                                <th scope="row"><label for="uofi_min_content_length"><?php esc_html_e('Minimum Content Length', 'ultra-optimized-featured-image'); ?></label></th>
                                <td>
                                    <input type="number" name="uofi_options[min_content_length]" id="uofi_min_content_length" value="<?php echo esc_attr($this->options['min_content_length']); ?>" class="small-text" min="0" step="10" />
                                    <p class="description"><?php esc_html_e('Minimum character count (approx.) in post content required to process for images (0 to disable check).', 'ultra-optimized-featured-image'); ?></p>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <!-- Image Settings -->
                    <div class="uofi-settings-card">
                         <h2><span class="dashicons dashicons-format-image"></span> <?php esc_html_e('Image Insertion Settings', 'ultra-optimized-featured-image'); ?></h2>
                         <p class="description"><?php esc_html_e('These settings apply only when "Auto Insert Content Images" is enabled.', 'ultra-optimized-featured-image'); ?></p>
                         <table class="form-table <?php echo $this->options['auto_insert_content_images'] ? '' : 'hidden'; ?>" role="presentation">
                             <tr>
                                <th scope="row"><?php esc_html_e('Image Source', 'ultra-optimized-featured-image'); ?></th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="uofi_options[image_sources][]" value="media_library" <?php checked(in_array('media_library', $this->options['image_sources'])); ?> disabled />
                                        <?php esc_html_e('WordPress Media Library', 'ultra-optimized-featured-image'); ?>
                                    </label>
                                     <p class="description"><?php esc_html_e('(Currently only source supported)', 'ultra-optimized-featured-image'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><label for="uofi_image_size"><?php esc_html_e('Inserted Image Size', 'ultra-optimized-featured-image'); ?></label></th>
                                <td>
                                    <select name="uofi_options[image_size]" id="uofi_image_size">
                                        <?php
                                        // Get all registered image sizes including 'full'
                                        $sizes = array_merge(['full'], get_intermediate_image_sizes());
                                        $current_size = $this->options['image_size'];
                                        foreach ($sizes as $size) {
                                             echo '<option value="' . esc_attr($size) . '" ' . selected($current_size, $size, false) . '>' . esc_html(ucfirst(str_replace(['_', '-'], ' ', $size))) . '</option>';
                                        }
                                        ?>
                                    </select>
                                    <p class="description"><?php esc_html_e('Select the image size to use for images inserted into content.', 'ultra-optimized-featured-image'); ?></p>
                                </td>
                            </tr>
                             <tr>
                                <th scope="row"><label for="uofi_insertion_style"><?php esc_html_e('Image Insertion Style', 'ultra-optimized-featured-image'); ?></label></th>
                                <td>
                                    <select name="uofi_options[image_insertion_style]" id="uofi_insertion_style" <?php disabled($this->options['max_images_per_heading'] <= 1); ?>
                                        <option value="single" <?php selected($this->options['image_insertion_style'], 'single'); ?>><?php esc_html_e('Single Image', 'ultra-optimized-featured-image'); ?></option>
<option value="grid" <?php selected($this->options['image_insertion_style'], 'grid'); ?>><?php esc_html_e('Grid Layout', 'ultra-optimized-featured-image'); ?></option>
<option value="carousel" <?php selected($this->options['image_insertion_style'], 'carousel'); ?>><?php esc_html_e('Carousel', 'ultra-optimized-featured-image'); ?></option>
                                        <option value="grid" <?php selected($this->options['image_insertion_style'], 'grid'); ?><?php esc_html_e('Grid Layout', 'ultra-optimized-featured-image'); ?></option>
                                        <option value="carousel" <?php selected($this->options['image_insertion_style'], 'carousel'); ?><?php esc_html_e('Carousel', 'ultra-optimized-featured-image'); ?></option>
                                    </select>
                                     <p class="description"><?php esc_html_e('How to display images when multiple are inserted under one heading. Forced to "Single" if Max Images Per Heading is 1.', 'ultra-optimized-featured-image'); ?></p>
                                </td>
                            </tr>
                             <tr>
                                <th scope="row"><label for="uofi_max_per_heading"><?php esc_html_e('Max Images Per Heading', 'ultra-optimized-featured-image'); ?></label></th>
                                <td>
                                    <input type="number" name="uofi_options[max_images_per_heading]" id="uofi_max_per_heading" value="<?php echo esc_attr($this->options['max_images_per_heading']); ?>" class="small-text" min="1" max="10" step="1" />
                                    <p class="description"><?php esc_html_e('Maximum number of images to insert under each matched H2 heading (1-10).', 'ultra-optimized-featured-image'); ?></p>
                                </td>
                            </tr>
                             <tr>
                                <th scope="row"><label for="uofi_max_per_post"><?php esc_html_e('Max Images Per Post', 'ultra-optimized-featured-image'); ?></label></th>
                                <td>
                                     <input type="number" name="uofi_options[max_images_per_post]" id="uofi_max_per_post" value="<?php echo esc_attr($this->options['max_images_per_post']); ?>" class="small-text" min="1" max="20" step="1"/>
                                     <p class="description"><?php esc_html_e('Maximum total number of images to insert throughout a single post (1-20).', 'ultra-optimized-featured-image'); ?></p>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div><!-- /.uofi-settings-grid -->

                <p class="submit">
                    <input type="submit" name="submit" id="submit" class="button button-primary" value="<?php esc_attr_e('Save Changes', 'ultra-optimized-featured-image'); ?>" />
                </p>
            </form>
        </div><!-- /.wrap -->

        <?php // Add styles and JS for settings page enhancements ?>
        <style>
            .uofi-settings-grid { display: grid; grid-template-columns: 1fr; gap: 20px; margin-bottom: 20px; }
            .uofi-settings-card { background: #fff; border: 1px solid #ccd0d4; border-radius: 4px; padding: 0 20px 20px 20px; }
            .uofi-settings-card h2 { margin: 20px 0; padding-bottom: 10px; border-bottom: 1px solid #eee; font-size: 1.1em; display: flex; align-items: center; gap: 8px; }
            .uofi-settings-card h2 .dashicons { margin: 0; }
            .form-table th { padding-top: 15px; padding-bottom: 15px; }
            .form-table td { padding: 10px 10px 10px 0; }
            .form-table p.description { margin-top: 5px; }
            .hidden { display: none; }
            #uofi-api-result .notice { margin: 10px 0 0 0; padding: 5px 10px; }
            @media (min-width: 960px) {
                 .uofi-settings-grid { grid-template-columns: repeat(2, 1fr); }
                 /* Adjust card span if needed */
                 /* .uofi-settings-card:nth-child(odd) { grid-column: 1 / 2; } */
                 /* .uofi-settings-card:nth-child(even) { grid-column: 2 / 3; } */
            }
        </style>
         <script>
            jQuery(document).ready(function($) {
                 // Toggle AI settings visibility based on checkbox
                 function toggleAiSettings() {
                     if ($('input[name="uofi_options[use_ai]"]').is(':checked')) {
                         $('.uofi-ai-setting').removeClass('hidden');
                     } else {
                         $('.uofi-ai-setting').addClass('hidden');
                     }
                 }
                 $('input[name="uofi_options[use_ai]"]').on('change', toggleAiSettings);
                 toggleAiSettings(); // Run on page load

                 // Toggle Image Insertion settings visibility
                  function toggleInsertionSettings() {
                     if ($('input[name="uofi_options[auto_insert_content_images]"]').is(':checked')) {
                         $('.uofi-settings-card:has(#uofi_image_size)').find('.form-table').removeClass('hidden');
                         $('.uofi-settings-card:has(#uofi_image_size)').find('p.description').first().show();
                     } else {
                         $('.uofi-settings-card:has(#uofi_image_size)').find('.form-table').addClass('hidden');
                          $('.uofi-settings-card:has(#uofi_image_size)').find('p.description').first().hide();
                     }
                 }
                 $('input[name="uofi_options[auto_insert_content_images]"]').on('change', toggleInsertionSettings);
                 toggleInsertionSettings(); // Run on page load

                 // API Key visibility toggle
                 $('#uofi-toggle-api-key').on('click', function() {
                    var $apiKeyInput = $('#uofi-api-key');
                    var $icon = $(this).find('.dashicons');
                    if ($apiKeyInput.attr('type') === 'password') {
                        $apiKeyInput.attr('type', 'text');
                        $icon.removeClass('dashicons-visibility').addClass('dashicons-hidden');
                    } else {
                        $apiKeyInput.attr('type', 'password');
                        $icon.removeClass('dashicons-hidden').addClass('dashicons-visibility');
                    }
                 });

                // Test API connection
                $('#uofi-test-api').on('click', function() {
                    var $button = $(this);
                    var $resultDiv = $('#uofi-api-result');
                    var apiKey = $('#uofi-api-key').val();

                    $button.prop('disabled', true).text('<?php echo esc_js(__('Testing...', 'ultra-optimized-featured-image')); ?>');
                    $resultDiv.html('<span class="spinner is-active" style="float: none; vertical-align: middle;"></span>');

                    $.ajax({
                        url: uofiData.ajaxUrl,
                        type: 'POST',
                        data: {
                            action: 'uofi_test_api_connection',
                            nonce: uofiData.nonce,
                            api_key: apiKey // Send the current value in the input
                        },
                        success: function(response) {
                            if (response.success) {
                                $resultDiv.html('<div class="notice notice-success inline"><p>' + response.data.message + '</p></div>');
                            } else {
                                $resultDiv.html('<div class="notice notice-error inline"><p>' + (response.data.message || uofiData.generic_error) + '</p></div>');
                            }
                        },
                        error: function() {
                            $resultDiv.html('<div class="notice notice-error inline"><p>' + uofiData.error + ' Server error occurred.</p></div>');
                        },
                        complete: function() {
                            $button.prop('disabled', false).text('<?php echo esc_js(__('Test Connection', 'ultra-optimized-featured-image')); ?>');
                        }
                    });
                });

                 // Update insertion style dropdown based on max images per heading
                 $('#uofi_max_per_heading').on('change input', function() {
                    var maxPerHeading = parseInt($(this).val(), 10);
                    var $styleSelect = $('#uofi_insertion_style');
                    if (maxPerHeading === 1) {
                        $styleSelect.val('single').prop('disabled', true);
                    } else {
                        $styleSelect.prop('disabled', false);
                         // If it was single, maybe default to grid?
                         if ($styleSelect.val() === 'single') {
                              $styleSelect.val('grid');
                         }
                    }
                 }).trigger('change'); // Trigger on load

            });
        </script>
        <?php
    }

    /**
     * Render tools page with actions like rebuild index, clear cache, etc.
     */
    public function render_tools_page() {
        ?>
        <div class="wrap uofi-admin">
            <h1><?php esc_html_e('Ultra Optimized Featured Image - Tools', 'ultra-optimized-featured-image'); ?></h1>

            <div class="uofi-tools-grid">
                <!-- Rebuild Index -->
                <div class="uofi-tool-card">
                    <h2><span class="dashicons dashicons-images-alt"></span> <?php esc_html_e('Rebuild Image Index', 'ultra-optimized-featured-image'); ?></h2>
                    <p><?php esc_html_e('Re-scans all images in the Media Library, extracts keywords, and generates AI embeddings (if enabled). Required if images were modified outside WordPress or after changing AI settings.', 'ultra-optimized-featured-image'); ?></p>
                    <p class="description"><?php esc_html_e('This can take a long time and consume API credits if AI is active.', 'ultra-optimized-featured-image'); ?></p>
                    <button id="uofi-rebuild-index" class="button button-secondary"><?php esc_html_e('Start Rebuild', 'ultra-optimized-featured-image'); ?></button>
                     <button id="uofi-stop-rebuild" class="button button-secondary" style="display:none;"><?php esc_html_e('Stop Rebuild', 'ultra-optimized-featured-image'); ?></button>
                    <div id="uofi-rebuild-progress" class="uofi-progress-container" style="display: none; margin-top: 10px;">
                        <div class="uofi-progress-bar"><div class="uofi-progress-fill"></div></div>
                        <div class="uofi-progress-text">0%</div>
                        <div class="uofi-progress-log"></div>
                    </div>
                </div>

                <!-- Clear Cache -->
                <div class="uofi-tool-card">
                    <h2><span class="dashicons dashicons-trash"></span> <?php esc_html_e('Clear Plugin Cache', 'ultra-optimized-featured-image'); ?></h2>
                    <p><?php esc_html_e('Clears all plugin-specific cached data (transients for API results, keyword extraction, image data, etc.).', 'ultra-optimized-featured-image'); ?></p>
                    <p class="description"><?php esc_html_e('Useful if image matching seems stuck or outdated. Does not delete logs or image index.', 'ultra-optimized-featured-image'); ?></p>
                    <button id="uofi-clear-cache" class="button button-secondary"><?php esc_html_e('Clear Cache', 'ultra-optimized-featured-image'); ?></button>
                    <div id="uofi-cache-result" style="margin-top: 10px;"></div>
                </div>

                <!-- Remove Duplicates Tool -->
                 <div class="uofi-tool-card">
                    <h2><span class="dashicons dashicons-image-filter"></span> <?php esc_html_e('Remove Duplicate Images (All Posts)', 'ultra-optimized-featured-image'); ?></h2>
                    <p><?php esc_html_e('Scans content of ALL selected post types and removes duplicate images based on their source URL.', 'ultra-optimized-featured-image'); ?></p>
                    <p class="description"><strong><?php esc_html_e('Warning:', 'ultra-optimized-featured-image'); ?></strong> <?php esc_html_e('This action cannot be undone. Backup your database first!', 'ultra-optimized-featured-image'); ?></p>
                     <?php if (empty($this->options['delete_duplicate_images'])) : ?>
                         <p style="color:red;"><?php esc_html_e('This feature is currently disabled in the General Settings.', 'ultra-optimized-featured-image'); ?></p>
                     <?php endif; ?>
                    <button id="uofi-remove-duplicates" class="button button-danger" <?php disabled(empty($this->options['delete_duplicate_images'])); ?>><?php esc_html_e('Start Duplicate Removal', 'ultra-optimized-featured-image'); ?></button>
                     <button id="uofi-stop-duplicates" class="button button-secondary" style="display:none;"><?php esc_html_e('Stop Removal', 'ultra-optimized-featured-image'); ?></button>
                     <div id="uofi-duplicates-progress" class="uofi-progress-container" style="display: none; margin-top: 10px;">
                        <div class="uofi-progress-bar"><div class="uofi-progress-fill"></div></div>
                         <div class="uofi-progress-text">0%</div>
                         <div class="uofi-progress-log"></div>
                    </div>
                </div>


                <!-- System Information -->
                <div class="uofi-tool-card">
                    <h2><span class="dashicons dashicons-info"></span> <?php esc_html_e('System Information', 'ultra-optimized-featured-image'); ?></h2>
                    <table class="widefat fixed striped" style="margin-top: 10px;">
                        <tbody>
                            <tr><td><?php esc_html_e('Plugin Version', 'ultra-optimized-featured-image'); ?></td><td><?php echo '2.0.2'; ?></td></tr>
                            <tr><td><?php esc_html_e('WordPress Version', 'ultra-optimized-featured-image'); ?></td><td><?php echo esc_html(get_bloginfo('version')); ?></td></tr>
                            <tr><td><?php esc_html_e('PHP Version', 'ultra-optimized-featured-image'); ?></td><td><?php echo esc_html(PHP_VERSION); ?></td></tr>
                            <tr><td><?php esc_html_e('WP Memory Limit', 'ultra-optimized-featured-image'); ?></td><td><?php echo esc_html(WP_MEMORY_LIMIT); ?></td></tr>
                             <tr><td><?php esc_html_e('PHP Memory Limit', 'ultra-optimized-featured-image'); ?></td><td><?php echo esc_html(ini_get('memory_limit')); ?></td></tr>
                            <tr><td><?php esc_html_e('Max Execution Time', 'ultra-optimized-featured-image'); ?></td><td><?php echo esc_html(ini_get('max_execution_time')); ?> seconds</td></tr>
                             <tr><td><?php esc_html_e('Background Processing', 'ultra-optimized-featured-image'); ?></td><td><?php echo $this->options['background_processing'] ? __('Enabled', 'ultra-optimized-featured-image') : __('Disabled', 'ultra-optimized-featured-image'); ?></td></tr>
                             <tr><td><?php esc_html_e('AI Matching', 'ultra-optimized-featured-image'); ?></td><td><?php echo ($this->options['use_ai'] && !empty($this->options['api_key'])) ? __('Enabled', 'ultra-optimized-featured-image') : __('Disabled', 'ultra-optimized-featured-image'); ?></td></tr>
                             <tr><td><?php esc_html_e('Logging', 'ultra-optimized-featured-image'); ?></td><td><?php echo $this->options['enable_logging'] ? __('Enabled', 'ultra-optimized-featured-image') : __('Disabled', 'ultra-optimized-featured-image'); ?></td></tr>
                        </tbody>
                    </table>
                </div>

                 <!-- Export Logs (Placeholder) -->
                 <div class="uofi-tool-card">
                    <h2><span class="dashicons dashicons-download"></span> <?php esc_html_e('Export Logs', 'ultra-optimized-featured-image'); ?></h2>
                    <p><?php esc_html_e('Download the assignment log stored in the database as a CSV file.', 'ultra-optimized-featured-image'); ?></p>
                    <p class="description"><?php esc_html_e('Useful for analysis or troubleshooting.', 'ultra-optimized-featured-image'); ?></p>
                    <?php if (empty($this->options['enable_logging'])) : ?>
                         <p style="color:orange;"><?php esc_html_e('Logging is currently disabled in settings. The log might be empty or incomplete.', 'ultra-optimized-featured-image'); ?></p>
                     <?php endif; ?>
                    <button id="uofi-export-logs" class="button button-secondary" disabled><?php esc_html_e('Export Logs (Coming Soon)', 'ultra-optimized-featured-image'); ?></button>
                </div>

            </div><!-- /.uofi-tools-grid -->
        </div><!-- /.wrap -->

        <?php // Styles needed for tools page ?>
        <style>
        .uofi-tools-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(400px, 1fr)); gap: 20px; margin-top: 20px; }
        .uofi-tool-card { background: #fff; border: 1px solid #ccd0d4; border-radius: 4px; padding: 20px; display: flex; flex-direction: column;}
        .uofi-tool-card h2 { margin-top: 0; padding-bottom: 10px; border-bottom: 1px solid #eee; font-size: 1.1em; display: flex; align-items: center; gap: 8px; }
        .uofi-tool-card h2 .dashicons { margin: 0; }
        .uofi-tool-card p { flex-grow: 1; } /* Push buttons to bottom */
        .uofi-tool-card .button-danger { background: #d63638; border-color: #b62f30 #a12a2b #a12a2b; box-shadow: 0 1px 0 #a12a2b; color: #fff; text-decoration: none; text-shadow: 0 -1px 1px #a12a2b,1px 0 1px #a12a2b,0 1px 1px #a12a2b,-1px 0 1px #a12a2b; }
        .uofi-tool-card .button-danger:hover { background: #c13032; border-color: #a12a2b; }
        .uofi-progress-container { margin-top: 15px; }
        .uofi-progress-bar { height: 10px; background-color: #f0f0f0; border-radius: 5px; overflow: hidden; margin-bottom: 5px;}
        .uofi-progress-fill { height: 100%; background-color: #2271b1; width: 0%; transition: width 0.3s ease; border-radius: 5px; }
        .uofi-progress-text { text-align: center; font-weight: bold; font-size:0.9em; }
        .uofi-progress-log { font-size: 0.85em; max-height: 150px; overflow-y: auto; background: #f0f0f0; border: 1px solid #e0e0e0; padding: 8px; margin-top: 8px; line-height: 1.4; font-family: monospace; }
        @media (max-width: 782px) { .uofi-tools-grid { grid-template-columns: 1fr; } }
        </style>

        <?php // JS for Tools Page Actions ?>
        <script>
        jQuery(document).ready(function($) {
            var stopAction = {}; // Object to hold stop flags for different actions
            var xhr = {}; // Object to hold XHR requests for different actions

             function setupAjaxAction(actionId, startButtonId, stopButtonId, progressContainerId, getItemsAction, processItemAction, confirmMsg) {
                var $startButton = $('#' + startButtonId);
                var $stopButton = $('#' + stopButtonId);
                var $progressContainer = $('#' + progressContainerId);
                var $fill = $progressContainer.find('.uofi-progress-fill');
                var $text = $progressContainer.find('.uofi-progress-text');
                var $log = $progressContainer.find('.uofi-progress-log');
                var allItems = [];
                var totalItems = 0;
                var currentIndex = 0;
                var processedCount = 0;
                var failedCount = 0;
                var skippedCount = 0;

                stopAction[actionId] = false; // Initialize stop flag

                function updateProgress() {
                     var processedTotal = currentIndex;
                     var percent = totalItems > 0 ? Math.min(100, Math.round((processedTotal / totalItems) * 100)) : 100;
                     $fill.css('width', percent + '%');
                     $text.text(percent + '% (' + processedTotal + '/' + totalItems + ')');
                 }

                 function addLog(message, isError = false) {
                     var entry = $('<div>').text(message);
                     if(isError) entry.css('color', 'red');
                     $log.append(entry);
                     $log.scrollTop($log[0].scrollHeight);
                 }

                function processNextItem() {
                     if (stopAction[actionId] || currentIndex >= totalItems) {
                         addLog(stopAction[actionId] ? 'Processing stopped by user.' : 'Finished processing all items.');
                         finalizeProcessing();
                         return;
                     }

                     var itemId = allItems[currentIndex];
                     addLog('Processing item: ' + itemId + ' (' + (currentIndex + 1) + '/' + totalItems + ')');

                     xhr[actionId] = $.ajax({
                         url: uofiData.ajaxUrl,
                         type: 'POST',
                         data: {
                             action: processItemAction,
                             nonce: uofiData.nonce,
                             item_id: itemId, // Use generic 'item_id' or specific like 'post_id', 'attachment_id'
                             post_id: itemId // Assuming item_id is post_id for duplicates
                         },
                         success: function(response) {
                             if (response.success) {
                                 addLog('- Item ' + itemId + ': Success - ' + (response.data.message || 'Processed.'));
                                 processedCount++;
                             } else {
                                 addLog('- Item ' + itemId + ': Failed/Skipped - ' + (response.data.message || uofiData.generic_error), true);
                                 failedCount++;
                             }
                         },
                         error: function(jqXHR, textStatus, errorThrown) {
                              if (textStatus === 'abort') {
                                 addLog('- Item ' + itemId + ': Aborted.');
                             } else {
                                 addLog('- Item ' + itemId + ': AJAX Error - ' + textStatus, true);
                                 console.error("AJAX Error:", textStatus, errorThrown, jqXHR.responseText);
                                 failedCount++;
                             }
                         },
                         complete: function() {
                             currentIndex++;
                             updateProgress();
                             setTimeout(processNextItem, 100); // Delay between items
                         }
                     });
                 }

                function finalizeProcessing() {
                    $startButton.prop('disabled', false).show();
                    $stopButton.hide();
                    stopAction[actionId] = true; // Ensure flag is set
                    addLog('Summary: Processed ' + processedCount + ', Failed/Skipped ' + failedCount);
                    xhr[actionId] = null; // Clear XHR object
                }

                // Attach start button click handler
                $startButton.on('click', function() {
                    if (confirmMsg && !confirm(confirmMsg)) {
                        return;
                    }

                    $startButton.prop('disabled', true).hide();
                    $stopButton.prop('disabled', false).show();
                    $progressContainer.show();
                    $log.html(''); // Clear log
                    stopAction[actionId] = false;
                    allItems = [];
                    totalItems = 0;
                    currentIndex = 0;
                    processedCount = 0;
                    failedCount = 0;
                    skippedCount = 0;

                    addLog('Fetching items to process...');
                    $.ajax({
                        url: uofiData.ajaxUrl,
                        type: 'POST',
                        data: {
                            action: getItemsAction,
                            nonce: uofiData.nonce
                        },
                        success: function(response) {
                            if (response.success) {
                                // Assuming response.data.posts or response.data.items holds the IDs
                                allItems = response.data.posts || response.data.items || [];
                                totalItems = allItems.length;
                                addLog('Found ' + totalItems + ' items. Starting processing...');
                                if (totalItems > 0) {
                                    updateProgress();
                                    processNextItem(); // Start the loop
                                } else {
                                    addLog('No items found to process.');
                                    finalizeProcessing();
                                }
                            } else {
                                addLog(uofiData.error + ' ' + (response.data.message || 'Could not fetch item list.'), true);
                                finalizeProcessing();
                            }
                        },
                        error: function() {
							addLog(uofiData.error + ' Failed to fetch item list (server error).', true);
							finalizeProcessing();
						}
                    });
                });

                // Attach stop button click handler
                $stopButton.on('click', function() {
                     $(this).prop('disabled', true);
                     stopAction[actionId] = true;
                     if (xhr[actionId]) {
                         xhr[actionId].abort();
                     }
                     addLog('Stopping processing...');
                });
            }

            // --- Setup Actions ---

            // 1. Rebuild Index
            setupAjaxAction(
                'rebuild',                                // Action ID for internal tracking
                'uofi-rebuild-index',                     // Start button ID
                'uofi-stop-rebuild',                      // Stop button ID
                'uofi-rebuild-progress',                  // Progress container ID
                'uofi_get_all_attachments',               // AJAX action to get all attachment IDs
                'uofi_rebuild_index',                     // AJAX action to process one attachment ID
                uofiData.confirm_rebuild_index            // Confirmation message from localized data
            );

            // 2. Clear Cache
            $('#uofi-clear-cache').on('click', function() {
                if (!confirm(uofiData.confirm_clear_cache)) {
                    return;
                }
                var $button = $(this);
                var $resultDiv = $('#uofi-cache-result');
                $button.prop('disabled', true);
                $resultDiv.html('<span class="spinner is-active" style="vertical-align: middle;"></span> Clearing...');

                $.ajax({
                    url: uofiData.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'uofi_clear_cache',
                        nonce: uofiData.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            $resultDiv.html('<div class="notice notice-success inline"><p>' + (response.data.message || 'Cache cleared successfully!') + '</p></div>');
                        } else {
                             $resultDiv.html('<div class="notice notice-error inline"><p>' + (response.data.message || uofiData.generic_error) + '</p></div>');
                        }
                    },
                    error: function() {
                        $resultDiv.html('<div class="notice notice-error inline"><p>' + uofiData.error + ' Server error occurred.</p></div>');
                    },
                    complete: function() {
                        $button.prop('disabled', false);
                    }
                });
            });

            // 3. Remove Duplicate Images (All Posts)
            setupAjaxAction(
                'duplicates',                                 // Action ID
                'uofi-remove-duplicates',                     // Start button ID
                'uofi-stop-duplicates',                       // Stop button ID
                'uofi-duplicates-progress',                   // Progress container ID
                'uofi_get_all_posts',                         // AJAX action to get item IDs (post IDs)
                'uofi_remove_duplicate_images',               // AJAX action to process one item (post ID)
                uofiData.confirm_remove_duplicates            // Confirmation message
            );


            // 4. Export Logs (Placeholder)
            $('#uofi-export-logs').on('click', function() {
                alert('Export logs functionality needs server-side implementation to generate and download a CSV file.');
                // Example of triggering download if implemented:
                // window.location.href = uofiData.ajaxUrl + '?action=uofi_export_logs&nonce=' + uofiData.nonce;
            });

        }); // End of jQuery(document).ready()
        </script>
        <?php
    }


    /**
     * AJAX handler for clearing plugin caches.
     */
    public function ajax_clear_cache() {
        check_ajax_referer('uofi_admin_nonce', 'nonce');
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('Permission denied', 'ultra-optimized-featured-image')]);
            return;
        }

        try {
            $success = $this->clear_all_caches();
            if ($success) {
                 wp_send_json_success(['message' => __('Plugin caches cleared successfully!', 'ultra-optimized-featured-image')]);
            } else {
                 wp_send_json_error(['message' => __('Failed to clear caches completely.', 'ultra-optimized-featured-image')]);
            }
        } catch (Exception $e) {
             $this->log("Error during AJAX clear cache: " . $e->getMessage());
             wp_send_json_error(['message' => __('An error occurred while clearing caches.', 'ultra-optimized-featured-image')]);
        }
    }

     /**
     * AJAX handler for getting all attachment IDs (for index rebuild).
     */
    public function ajax_get_all_attachments() {
        // Log that the function was called
        error_log('[UOFI Debug] ajax_get_all_attachments called. Nonce received: ' . (isset($_POST['nonce']) ? $_POST['nonce'] : 'MISSING'));

        // Security checks
        $nonce_check_result = check_ajax_referer('uofi_admin_nonce', 'nonce', false); // false = don't die
        if (false === $nonce_check_result) {
             error_log('[UOFI Debug] Nonce check FAILED for action uofi_get_all_attachments.');
             wp_send_json_error(['message' => 'Nonce verification failed. Please refresh the page and try again.']);
             return; // Exit after sending error
        } else {
             error_log('[UOFI Debug] Nonce check PASSED.');
        }

        // REMOVED the stray */ and the SKIPPING log line

        if (!current_user_can('manage_options')) {
            error_log('[UOFI Debug] Permission check FAILED.');
            wp_send_json_error(['message' => __('Permission denied.')]);
            return; // Exit after sending error
        } else {
             error_log('[UOFI Debug] Permission check PASSED.');
        }

        // Original database query
        global $wpdb;
        error_log('[UOFI Debug] Running DB query for attachments...');
        // NOTE: Removed the LIMIT 100 from the query to get all IDs again.
        // Add it back if you suspect memory issues during the query itself.
        $query = "SELECT ID FROM {$wpdb->posts} WHERE post_type = 'attachment' AND post_mime_type LIKE 'image/%' ORDER BY ID ASC";
        $ids = $wpdb->get_col($query);

        // Check for DB errors
        if ($wpdb->last_error) {
             error_log('[UOFI Debug] DB Error fetching attachments: ' . $wpdb->last_error);
             wp_send_json_error(['message' => 'Database error fetching attachments: ' . $wpdb->last_error]);
             return;
        }

        $count = count($ids);
        error_log('[UOFI Debug] Found ' . $count . ' attachments. Sending success response.');

        // Send the result
        wp_send_json_success([
            'items'   => $ids, // Use 'items' for consistency with JS function
            'count'   => $count,
            'message' => sprintf(__('Found %d images to index.', 'ultra-optimized-featured-image'), $count)
        ]);
        // wp_send_json_success includes die()
    }

     /**
      * AJAX handler for rebuilding index for a single image.
      * Needs implementation server-side.
      */
     public function ajax_rebuild_index() {
          check_ajax_referer('uofi_admin_nonce', 'nonce');
          if (!current_user_can('manage_options')) {
             wp_send_json_error(['message' => __('Permission denied.')]);
             return;
          }

          $attachment_id = isset($_POST['item_id']) ? absint($_POST['item_id']) : 0; // Use item_id from JS

          if (!$attachment_id || !wp_attachment_is_image($attachment_id)) {
               wp_send_json_error(['message' => sprintf(__('Invalid attachment ID: %d', 'ultra-optimized-featured-image'), $attachment_id)]);
               return;
          }

          // Force re-index by clearing cache first (optional, depends on how caching is used in index_image)
          // delete_transient('uofi_index_img_' . $attachment_id);

          $result = $this->index_image($attachment_id);

          if ($result) {
               wp_send_json_success(['message' => sprintf(__('Image %d indexed/updated.', 'ultra-optimized-featured-image'), $attachment_id)]);
          } else {
                // Error/skip logged internally by index_image
               wp_send_json_error(['message' => sprintf(__('Failed to index image %d (see logs).', 'ultra-optimized-featured-image'), $attachment_id)]);
          }
     }


} // End Class UltraOptimizedFeaturedImage

/**
 * Initialize the plugin instance.
 */
function uofi_get_instance() {
    return UltraOptimizedFeaturedImage::get_instance();
}
// Initialize on plugins_loaded hook for better compatibility
add_action('plugins_loaded', 'uofi_get_instance');