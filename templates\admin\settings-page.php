<?php
/**
 * Settings page template
 * 
 * @package UltraFeaturedImageOptimizer
 */

if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="ufio-admin-wrap">
    <div class="ufio-header">
        <h1><?php _e('Ultra Featured Image Optimizer Settings', 'ultra-featured-image-optimizer'); ?></h1>
        <p class="description">
            <?php _e('Configure the plugin settings to optimize your image processing workflow.', 'ultra-featured-image-optimizer'); ?>
        </p>
    </div>

    <?php settings_errors('ufio_messages'); ?>

    <form method="post" action="options.php" id="ufio-settings-form">
        <?php
        settings_fields('ufio_settings');
        do_settings_sections('ufio_settings');
        ?>
        
        <div class="ufio-card">
            <table class="form-table" role="presentation">
                <?php do_settings_fields('ufio_settings', 'ufio_general'); ?>
            </table>
        </div>

        <div class="ufio-card">
            <table class="form-table" role="presentation">
                <?php do_settings_fields('ufio_settings', 'ufio_ai'); ?>
            </table>
        </div>

        <div class="ufio-card">
            <table class="form-table" role="presentation">
                <?php do_settings_fields('ufio_settings', 'ufio_seo'); ?>
            </table>
        </div>

        <div class="ufio-card">
            <table class="form-table" role="presentation">
                <?php do_settings_fields('ufio_settings', 'ufio_performance'); ?>
            </table>
        </div>

        <?php submit_button(__('Save Settings', 'ultra-featured-image-optimizer'), 'primary', 'submit', true, ['class' => 'ufio-button primary']); ?>
    </form>
</div>
