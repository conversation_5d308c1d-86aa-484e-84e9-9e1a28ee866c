<?php
/**
 * Admin Manager Class
 * 
 * Manages all admin-related functionality
 * 
 * @package AiSeoOptimizerUltra
 * @since 9.0.0
 */

namespace AiSeoOptimizerUltra\Admin;

use AiSeoOptimizerUltra\Core\Logger;
use AiSeoOptimizerUltra\Core\Security;
use AiSeoOptimizerUltra\Core\Options;
use Exception;

/**
 * Admin Manager Class
 * 
 * Handles admin interface and functionality
 */
final class AdminManager {
    private static $instance = null;
    private $admin_pages = [];
    private $hooks_registered = false;
    
    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Private constructor
     */
    private function __construct() {
        $this->init_admin();
    }
    
    /**
     * Initialize admin functionality
     */
    private function init_admin() {
        if (!is_admin()) {
            return;
        }
        
        $this->register_admin_hooks();
        $this->init_admin_components();
    }
    
    /**
     * Register admin hooks
     */
    private function register_admin_hooks() {
        if ($this->hooks_registered) {
            return;
        }
        
        // Menu and pages
        add_action('admin_menu', [$this, 'add_admin_menu']);
        add_action('admin_init', [$this, 'admin_init']);
        
        // Scripts and styles
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_assets']);
        
        // AJAX handlers
        add_action('wp_ajax_ai_seo_ultra_save_settings', [$this, 'ajax_save_settings']);
        add_action('wp_ajax_ai_seo_ultra_test_api', [$this, 'ajax_test_api']);
        add_action('wp_ajax_ai_seo_ultra_get_dashboard_data', [$this, 'ajax_get_dashboard_data']);
        
        // Admin notices
        add_action('admin_notices', [$this, 'admin_notices']);
        
        // Plugin action links
        add_filter('plugin_action_links_' . AI_SEO_ULTRA_PLUGIN_BASENAME, [$this, 'plugin_action_links']);
        
        $this->hooks_registered = true;
        Logger::debug('Admin hooks registered');
    }
    
    /**
     * Initialize admin components
     */
    private function init_admin_components() {
        try {
            // Initialize admin page classes
            $this->admin_pages = [
                'dashboard' => new DashboardPage(),
                'settings' => new SettingsPage(),
                'bulk_optimizer' => new BulkOptimizerPage(),
                'analytics' => new AnalyticsPage(),
                'tools' => new ToolsPage()
            ];
            
            Logger::debug('Admin components initialized');
            
        } catch (Exception $e) {
            Logger::error('Failed to initialize admin components: ' . $e->getMessage());
        }
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        try {
            // Main menu page
            add_menu_page(
                __('AI SEO Optimizer Ultra', 'ai-seo-optimizer-ultra'),
                __('AI SEO Ultra', 'ai-seo-optimizer-ultra'),
                'manage_options',
                'ai-seo-ultra',
                [$this, 'render_dashboard_page'],
                'dashicons-search',
                30
            );
            
            // Dashboard submenu
            add_submenu_page(
                'ai-seo-ultra',
                __('Dashboard', 'ai-seo-optimizer-ultra'),
                __('Dashboard', 'ai-seo-optimizer-ultra'),
                'manage_options',
                'ai-seo-ultra',
                [$this, 'render_dashboard_page']
            );
            
            // Settings submenu
            add_submenu_page(
                'ai-seo-ultra',
                __('Settings', 'ai-seo-optimizer-ultra'),
                __('Settings', 'ai-seo-optimizer-ultra'),
                'manage_options',
                'ai-seo-ultra-settings',
                [$this, 'render_settings_page']
            );
            
            // Bulk Optimizer submenu
            add_submenu_page(
                'ai-seo-ultra',
                __('Bulk Optimizer', 'ai-seo-optimizer-ultra'),
                __('Bulk Optimizer', 'ai-seo-optimizer-ultra'),
                'edit_posts',
                'ai-seo-ultra-bulk',
                [$this, 'render_bulk_optimizer_page']
            );
            
            // Analytics submenu
            add_submenu_page(
                'ai-seo-ultra',
                __('Analytics', 'ai-seo-optimizer-ultra'),
                __('Analytics', 'ai-seo-optimizer-ultra'),
                'edit_posts',
                'ai-seo-ultra-analytics',
                [$this, 'render_analytics_page']
            );
            
            // Tools submenu
            add_submenu_page(
                'ai-seo-ultra',
                __('Tools', 'ai-seo-optimizer-ultra'),
                __('Tools', 'ai-seo-optimizer-ultra'),
                'manage_options',
                'ai-seo-ultra-tools',
                [$this, 'render_tools_page']
            );
            
            Logger::debug('Admin menu added');
            
        } catch (Exception $e) {
            Logger::error('Error adding admin menu: ' . $e->getMessage());
        }
    }
    
    /**
     * Admin init
     */
    public function admin_init() {
        try {
            // Register settings
            $this->register_settings();
            
            // Check for admin notices
            $this->check_admin_notices();
            
        } catch (Exception $e) {
            Logger::error('Error in admin_init: ' . $e->getMessage());
        }
    }
    
    /**
     * Register plugin settings
     */
    private function register_settings() {
        $option_definitions = Options::get_all_definitions();
        
        foreach ($option_definitions as $option_name => $definition) {
            register_setting(
                'ai_seo_ultra_settings',
                $option_name,
                [
                    'type' => $definition['type'],
                    'sanitize_callback' => function($value) use ($option_name) {
                        return Security::sanitize_input($value, Options::get_definition($option_name)['sanitize'] ?? 'text');
                    }
                ]
            );
        }
    }
    
    /**
     * Check for admin notices
     */
    private function check_admin_notices() {
        // Check API key
        $api_key = Options::get('ai_seo_ultra_api_key');
        if (empty($api_key)) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-warning is-dismissible">';
                echo '<p><strong>' . __('AI SEO Optimizer Ultra:', 'ai-seo-optimizer-ultra') . '</strong> ';
                echo sprintf(
                    __('Please configure your API key in the <a href="%s">settings</a> to start optimizing your content.', 'ai-seo-optimizer-ultra'),
                    admin_url('admin.php?page=ai-seo-ultra-settings')
                );
                echo '</p></div>';
            });
        }
    }
    
    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook) {
        // Only load on our admin pages
        if (strpos($hook, 'ai-seo-ultra') === false) {
            return;
        }
        
        try {
            // Enqueue styles
            wp_enqueue_style(
                'ai-seo-ultra-admin',
                AI_SEO_ULTRA_PLUGIN_URL . 'assets/css/admin.css',
                [],
                AI_SEO_ULTRA_VERSION
            );
            
            // Enqueue scripts
            wp_enqueue_script(
                'ai-seo-ultra-admin',
                AI_SEO_ULTRA_PLUGIN_URL . 'assets/js/admin.js',
                ['jquery', 'wp-util'],
                AI_SEO_ULTRA_VERSION,
                true
            );
            
            // Localize script
            wp_localize_script('ai-seo-ultra-admin', 'aiSeoUltra', [
                'ajaxUrl' => admin_url('admin-ajax.php'),
                'nonce' => Security::create_nonce('ai_seo_ultra_ajax'),
                'strings' => [
                    'processing' => __('Processing...', 'ai-seo-optimizer-ultra'),
                    'error' => __('An error occurred. Please try again.', 'ai-seo-optimizer-ultra'),
                    'success' => __('Operation completed successfully.', 'ai-seo-optimizer-ultra'),
                    'confirm' => __('Are you sure?', 'ai-seo-optimizer-ultra')
                ]
            ]);
            
            Logger::debug('Admin assets enqueued for hook: ' . $hook);
            
        } catch (Exception $e) {
            Logger::error('Error enqueuing admin assets: ' . $e->getMessage());
        }
    }
    
    /**
     * Render dashboard page
     */
    public function render_dashboard_page() {
        if (isset($this->admin_pages['dashboard'])) {
            $this->admin_pages['dashboard']->render();
        }
    }
    
    /**
     * Render settings page
     */
    public function render_settings_page() {
        if (isset($this->admin_pages['settings'])) {
            $this->admin_pages['settings']->render();
        }
    }
    
    /**
     * Render bulk optimizer page
     */
    public function render_bulk_optimizer_page() {
        if (isset($this->admin_pages['bulk_optimizer'])) {
            $this->admin_pages['bulk_optimizer']->render();
        }
    }
    
    /**
     * Render analytics page
     */
    public function render_analytics_page() {
        if (isset($this->admin_pages['analytics'])) {
            $this->admin_pages['analytics']->render();
        }
    }
    
    /**
     * Render tools page
     */
    public function render_tools_page() {
        if (isset($this->admin_pages['tools'])) {
            $this->admin_pages['tools']->render();
        }
    }
    
    /**
     * AJAX: Save settings
     */
    public function ajax_save_settings() {
        try {
            Security::verify_ajax_nonce('ai_seo_ultra_ajax');
            
            if (!Security::can_manage_options()) {
                wp_send_json_error('Insufficient permissions');
            }
            
            $settings = $_POST['settings'] ?? [];
            $updated = 0;
            
            foreach ($settings as $option_name => $value) {
                if (Options::exists($option_name)) {
                    if (Options::set($option_name, $value)) {
                        $updated++;
                    }
                }
            }
            
            wp_send_json_success([
                'message' => sprintf(__('%d settings updated successfully.', 'ai-seo-optimizer-ultra'), $updated),
                'updated' => $updated
            ]);
            
        } catch (Exception $e) {
            Logger::error('AJAX save settings error: ' . $e->getMessage());
            wp_send_json_error('Error saving settings: ' . $e->getMessage());
        }
    }
    
    /**
     * AJAX: Test API connection
     */
    public function ajax_test_api() {
        try {
            Security::verify_ajax_nonce('ai_seo_ultra_ajax');
            
            if (!Security::can_manage_options()) {
                wp_send_json_error('Insufficient permissions');
            }
            
            // Test API connection logic will be implemented in ApiClient
            $api_client = \AiSeoOptimizerUltra\Core\ApiClient::get_instance();
            $result = $api_client->test_connection();
            
            if ($result) {
                wp_send_json_success(['message' => __('API connection successful!', 'ai-seo-optimizer-ultra')]);
            } else {
                wp_send_json_error('API connection failed');
            }
            
        } catch (Exception $e) {
            Logger::error('AJAX test API error: ' . $e->getMessage());
            wp_send_json_error('Error testing API: ' . $e->getMessage());
        }
    }
    
    /**
     * AJAX: Get dashboard data
     */
    public function ajax_get_dashboard_data() {
        try {
            Security::verify_ajax_nonce('ai_seo_ultra_ajax');
            
            if (!Security::can_edit_posts()) {
                wp_send_json_error('Insufficient permissions');
            }
            
            // Get dashboard data
            $data = [
                'stats' => $this->get_dashboard_stats(),
                'recent_activity' => $this->get_recent_activity(),
                'top_posts' => $this->get_top_posts()
            ];
            
            wp_send_json_success($data);
            
        } catch (Exception $e) {
            Logger::error('AJAX get dashboard data error: ' . $e->getMessage());
            wp_send_json_error('Error loading dashboard data: ' . $e->getMessage());
        }
    }
    
    /**
     * Get dashboard statistics
     */
    private function get_dashboard_stats() {
        // Implementation will be added
        return [
            'total_posts' => 0,
            'optimized_posts' => 0,
            'average_score' => 0,
            'pending_optimization' => 0
        ];
    }
    
    /**
     * Get recent activity
     */
    private function get_recent_activity() {
        // Implementation will be added
        return [];
    }
    
    /**
     * Get top performing posts
     */
    private function get_top_posts() {
        // Implementation will be added
        return [];
    }
    
    /**
     * Admin notices
     */
    public function admin_notices() {
        // Check for transient notices
        $notices = get_transient('ai_seo_ultra_admin_notices');
        if ($notices) {
            foreach ($notices as $notice) {
                echo '<div class="notice notice-' . esc_attr($notice['type']) . ' is-dismissible">';
                echo '<p>' . esc_html($notice['message']) . '</p>';
                echo '</div>';
            }
            delete_transient('ai_seo_ultra_admin_notices');
        }
    }
    
    /**
     * Plugin action links
     */
    public function plugin_action_links($links) {
        $settings_link = '<a href="' . admin_url('admin.php?page=ai-seo-ultra-settings') . '">' . __('Settings', 'ai-seo-optimizer-ultra') . '</a>';
        array_unshift($links, $settings_link);
        return $links;
    }
    
    /**
     * Add admin notice
     */
    public static function add_notice($message, $type = 'info') {
        $notices = get_transient('ai_seo_ultra_admin_notices') ?: [];
        $notices[] = ['message' => $message, 'type' => $type];
        set_transient('ai_seo_ultra_admin_notices', $notices, 300); // 5 minutes
    }
    
    /**
     * Prevent cloning and unserialization
     */
    private function __clone() {}
    public function __wakeup() {
        throw new Exception('Cannot unserialize singleton');
    }
}

// Placeholder classes for admin pages (will be implemented separately)
class DashboardPage {
    public function render() {
        echo '<div class="wrap"><h1>AI SEO Ultra Dashboard</h1><p>Dashboard implementation coming soon...</p></div>';
    }
}

class SettingsPage {
    public function render() {
        echo '<div class="wrap"><h1>AI SEO Ultra Settings</h1><p>Settings implementation coming soon...</p></div>';
    }
}

class BulkOptimizerPage {
    public function render() {
        echo '<div class="wrap"><h1>Bulk Optimizer</h1><p>Bulk optimizer implementation coming soon...</p></div>';
    }
}

class AnalyticsPage {
    public function render() {
        echo '<div class="wrap"><h1>Analytics</h1><p>Analytics implementation coming soon...</p></div>';
    }
}

class ToolsPage {
    public function render() {
        echo '<div class="wrap"><h1>Tools</h1><p>Tools implementation coming soon...</p></div>';
    }
}
