<?php
/**
 * Meta Box Template
 * 
 * Professional meta box interface for post editing
 * 
 * @package AiSeoOptimizerUltra
 * @since 9.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

use AiSeoOptimizerUltra\Core\Security;
use AiSeoOptimizerUltra\Core\Database;

// Get current SEO data
$seo_data = Database::get_seo_data($post->ID) ?: [];
$current_title = $post->post_title;
$current_description = get_post_meta($post->ID, '_yoast_wpseo_metadesc', true) ?: 
                      get_post_meta($post->ID, 'rank_math_description', true) ?: 
                      $post->post_excerpt;

// Calculate current scores
$title_score = $seo_data['title_score'] ?? 0;
$description_score = $seo_data['description_score'] ?? 0;
$overall_score = $seo_data['overall_score'] ?? 0;
?>

<div class="ai-seo-ultra-meta-box">
    <!-- Notices Container -->
    <div class="ai-seo-notices"></div>
    
    <!-- SEO Scores Section -->
    <div class="ai-seo-scores">
        <div class="ai-seo-score-item score-<?php echo $title_score >= 80 ? 'excellent' : ($title_score >= 60 ? 'good' : ($title_score >= 40 ? 'fair' : 'poor')); ?>" data-type="title">
            <div class="ai-seo-score-label"><?php _e('Title Score', 'ai-seo-optimizer-ultra'); ?></div>
            <div class="ai-seo-score-value"><?php echo esc_html($title_score); ?></div>
            <div class="ai-seo-score-bar">
                <div class="ai-seo-score-fill" style="width: <?php echo esc_attr($title_score); ?>%"></div>
            </div>
        </div>
        
        <div class="ai-seo-score-item score-<?php echo $description_score >= 80 ? 'excellent' : ($description_score >= 60 ? 'good' : ($description_score >= 40 ? 'fair' : 'poor')); ?>" data-type="description">
            <div class="ai-seo-score-label"><?php _e('Description Score', 'ai-seo-optimizer-ultra'); ?></div>
            <div class="ai-seo-score-value"><?php echo esc_html($description_score); ?></div>
            <div class="ai-seo-score-bar">
                <div class="ai-seo-score-fill" style="width: <?php echo esc_attr($description_score); ?>%"></div>
            </div>
        </div>
        
        <div class="ai-seo-score-item score-<?php echo $overall_score >= 80 ? 'excellent' : ($overall_score >= 60 ? 'good' : ($overall_score >= 40 ? 'fair' : 'poor')); ?>" data-type="overall">
            <div class="ai-seo-score-label"><?php _e('Overall Score', 'ai-seo-optimizer-ultra'); ?></div>
            <div class="ai-seo-score-value"><?php echo esc_html($overall_score); ?></div>
            <div class="ai-seo-score-bar">
                <div class="ai-seo-score-fill" style="width: <?php echo esc_attr($overall_score); ?>%"></div>
            </div>
        </div>
    </div>
    
    <!-- SEO Fields Section -->
    <div class="ai-seo-fields">
        <!-- Primary Keyword -->
        <div class="ai-seo-field">
            <label for="ai_seo_primary_keyword">
                <?php _e('Primary Keyword', 'ai-seo-optimizer-ultra'); ?>
            </label>
            <input 
                type="text" 
                id="ai_seo_primary_keyword" 
                name="ai_seo_primary_keyword" 
                value="<?php echo esc_attr(get_post_meta($post->ID, '_ai_seo_ultra_primary_keyword', true)); ?>"
                placeholder="<?php esc_attr_e('Enter your primary keyword', 'ai-seo-optimizer-ultra'); ?>"
            />
            <div class="ai-seo-field-description">
                <?php _e('The main keyword you want this content to rank for.', 'ai-seo-optimizer-ultra'); ?>
            </div>
        </div>
        
        <!-- Secondary Keywords -->
        <div class="ai-seo-field">
            <label for="ai_seo_secondary_keywords">
                <?php _e('Secondary Keywords', 'ai-seo-optimizer-ultra'); ?>
            </label>
            <input 
                type="text" 
                id="ai_seo_secondary_keywords" 
                name="ai_seo_secondary_keywords" 
                value="<?php echo esc_attr(get_post_meta($post->ID, '_ai_seo_ultra_secondary_keywords', true)); ?>"
                placeholder="<?php esc_attr_e('keyword1, keyword2, keyword3', 'ai-seo-optimizer-ultra'); ?>"
            />
            <div class="ai-seo-field-description">
                <?php _e('Additional keywords separated by commas.', 'ai-seo-optimizer-ultra'); ?>
            </div>
        </div>
        
        <!-- Meta Description -->
        <div class="ai-seo-field">
            <label for="ai_seo_meta_description">
                <?php _e('Meta Description', 'ai-seo-optimizer-ultra'); ?>
            </label>
            <textarea 
                id="ai_seo_meta_description" 
                name="ai_seo_meta_description" 
                rows="3"
                maxlength="160"
                placeholder="<?php esc_attr_e('Write a compelling meta description...', 'ai-seo-optimizer-ultra'); ?>"
            ><?php echo esc_textarea($current_description); ?></textarea>
            <div class="ai-seo-field-description">
                <?php _e('Optimal length: 150-160 characters. This appears in search results.', 'ai-seo-optimizer-ultra'); ?>
            </div>
        </div>
        
        <!-- Canonical URL -->
        <div class="ai-seo-field">
            <label for="ai_seo_canonical_url">
                <?php _e('Canonical URL', 'ai-seo-optimizer-ultra'); ?>
            </label>
            <input 
                type="url" 
                id="ai_seo_canonical_url" 
                name="ai_seo_canonical_url" 
                value="<?php echo esc_attr(get_post_meta($post->ID, '_ai_seo_ultra_canonical_url', true)); ?>"
                placeholder="<?php echo esc_attr(get_permalink($post->ID)); ?>"
            />
            <div class="ai-seo-field-description">
                <?php _e('Leave blank to use the default permalink.', 'ai-seo-optimizer-ultra'); ?>
            </div>
        </div>
    </div>
    
    <!-- AI Suggestions Section -->
    <?php if (!empty($seo_data['suggested_title']) || !empty($seo_data['suggested_description'])): ?>
    <div class="ai-seo-suggestions">
        <h3><?php _e('AI Suggestions', 'ai-seo-optimizer-ultra'); ?></h3>
        
        <?php if (!empty($seo_data['suggested_title'])): ?>
        <div class="ai-seo-suggestion-item">
            <span class="ai-seo-suggestion-label"><?php _e('Suggested Title:', 'ai-seo-optimizer-ultra'); ?></span>
            <div class="ai-seo-suggestion-content">
                <?php echo esc_html($seo_data['suggested_title']); ?>
            </div>
            <div class="ai-seo-suggestion-actions">
                <button 
                    type="button" 
                    class="ai-seo-button small ai-seo-apply-suggestion"
                    data-field="post_title"
                    data-suggestion="<?php echo esc_attr($seo_data['suggested_title']); ?>"
                >
                    <?php _e('Apply', 'ai-seo-optimizer-ultra'); ?>
                </button>
            </div>
        </div>
        <?php endif; ?>
        
        <?php if (!empty($seo_data['suggested_description'])): ?>
        <div class="ai-seo-suggestion-item">
            <span class="ai-seo-suggestion-label"><?php _e('Suggested Description:', 'ai-seo-optimizer-ultra'); ?></span>
            <div class="ai-seo-suggestion-content">
                <?php echo esc_html($seo_data['suggested_description']); ?>
            </div>
            <div class="ai-seo-suggestion-actions">
                <button 
                    type="button" 
                    class="ai-seo-button small ai-seo-apply-suggestion"
                    data-field="ai_seo_meta_description"
                    data-suggestion="<?php echo esc_attr($seo_data['suggested_description']); ?>"
                >
                    <?php _e('Apply', 'ai-seo-optimizer-ultra'); ?>
                </button>
            </div>
        </div>
        <?php endif; ?>
        
        <?php if (!empty($seo_data['suggested_h1'])): ?>
        <div class="ai-seo-suggestion-item">
            <span class="ai-seo-suggestion-label"><?php _e('Suggested H1:', 'ai-seo-optimizer-ultra'); ?></span>
            <div class="ai-seo-suggestion-content">
                <?php echo esc_html($seo_data['suggested_h1']); ?>
            </div>
            <div class="ai-seo-field-description">
                <?php _e('Consider using this as your main heading in the content.', 'ai-seo-optimizer-ultra'); ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>
    
    <!-- Actions Section -->
    <div class="ai-seo-actions">
        <button 
            type="button" 
            class="ai-seo-button ai-seo-process-post"
            data-post-id="<?php echo esc_attr($post->ID); ?>"
        >
            <span class="dashicons dashicons-admin-generic"></span>
            <?php _e('Optimize with AI', 'ai-seo-optimizer-ultra'); ?>
        </button>
        
        <button 
            type="button" 
            class="ai-seo-button secondary"
            onclick="location.reload()"
        >
            <span class="dashicons dashicons-update"></span>
            <?php _e('Refresh Scores', 'ai-seo-optimizer-ultra'); ?>
        </button>
    </div>
    
    <!-- Last Processed Info -->
    <?php if (!empty($seo_data['last_processed'])): ?>
    <div class="ai-seo-status info ai-seo-mt-15">
        <?php 
        printf(
            __('Last optimized: %s', 'ai-seo-optimizer-ultra'),
            '<strong>' . esc_html(mysql2date(get_option('date_format') . ' ' . get_option('time_format'), $seo_data['last_processed'])) . '</strong>'
        ); 
        ?>
    </div>
    <?php endif; ?>
    
    <!-- Schema Settings -->
    <div class="ai-seo-field ai-seo-mt-15">
        <label>
            <input 
                type="checkbox" 
                name="ai_seo_schema_enabled" 
                value="1"
                <?php checked(get_post_meta($post->ID, '_ai_seo_ultra_schema_enabled', true), '1'); ?>
            />
            <?php _e('Enable Schema Markup for this post', 'ai-seo-optimizer-ultra'); ?>
        </label>
        <div class="ai-seo-field-description">
            <?php _e('Automatically generate structured data to help search engines understand your content.', 'ai-seo-optimizer-ultra'); ?>
        </div>
    </div>
    
    <!-- Advanced Options (Collapsible) -->
    <details class="ai-seo-advanced-options ai-seo-mt-15">
        <summary><?php _e('Advanced Options', 'ai-seo-optimizer-ultra'); ?></summary>
        
        <div class="ai-seo-field">
            <label for="ai_seo_meta_robots">
                <?php _e('Meta Robots', 'ai-seo-optimizer-ultra'); ?>
            </label>
            <select id="ai_seo_meta_robots" name="ai_seo_meta_robots">
                <option value=""><?php _e('Default', 'ai-seo-optimizer-ultra'); ?></option>
                <option value="index, follow" <?php selected(get_post_meta($post->ID, '_ai_seo_ultra_meta_robots', true), 'index, follow'); ?>>
                    <?php _e('Index, Follow', 'ai-seo-optimizer-ultra'); ?>
                </option>
                <option value="noindex, follow" <?php selected(get_post_meta($post->ID, '_ai_seo_ultra_meta_robots', true), 'noindex, follow'); ?>>
                    <?php _e('No Index, Follow', 'ai-seo-optimizer-ultra'); ?>
                </option>
                <option value="index, nofollow" <?php selected(get_post_meta($post->ID, '_ai_seo_ultra_meta_robots', true), 'index, nofollow'); ?>>
                    <?php _e('Index, No Follow', 'ai-seo-optimizer-ultra'); ?>
                </option>
                <option value="noindex, nofollow" <?php selected(get_post_meta($post->ID, '_ai_seo_ultra_meta_robots', true), 'noindex, nofollow'); ?>>
                    <?php _e('No Index, No Follow', 'ai-seo-optimizer-ultra'); ?>
                </option>
            </select>
        </div>
        
        <div class="ai-seo-field">
            <label for="ai_seo_schema_type">
                <?php _e('Schema Type', 'ai-seo-optimizer-ultra'); ?>
            </label>
            <select id="ai_seo_schema_type" name="ai_seo_schema_type">
                <option value=""><?php _e('Auto-detect', 'ai-seo-optimizer-ultra'); ?></option>
                <option value="Article" <?php selected(get_post_meta($post->ID, '_ai_seo_ultra_schema_type', true), 'Article'); ?>>
                    <?php _e('Article', 'ai-seo-optimizer-ultra'); ?>
                </option>
                <option value="WebPage" <?php selected(get_post_meta($post->ID, '_ai_seo_ultra_schema_type', true), 'WebPage'); ?>>
                    <?php _e('Web Page', 'ai-seo-optimizer-ultra'); ?>
                </option>
                <option value="Recipe" <?php selected(get_post_meta($post->ID, '_ai_seo_ultra_schema_type', true), 'Recipe'); ?>>
                    <?php _e('Recipe', 'ai-seo-optimizer-ultra'); ?>
                </option>
                <option value="HowTo" <?php selected(get_post_meta($post->ID, '_ai_seo_ultra_schema_type', true), 'HowTo'); ?>>
                    <?php _e('How-To', 'ai-seo-optimizer-ultra'); ?>
                </option>
                <option value="FAQPage" <?php selected(get_post_meta($post->ID, '_ai_seo_ultra_schema_type', true), 'FAQPage'); ?>>
                    <?php _e('FAQ Page', 'ai-seo-optimizer-ultra'); ?>
                </option>
                <option value="Review" <?php selected(get_post_meta($post->ID, '_ai_seo_ultra_schema_type', true), 'Review'); ?>>
                    <?php _e('Review', 'ai-seo-optimizer-ultra'); ?>
                </option>
            </select>
        </div>
    </details>
</div>

<script>
// Pass post ID to JavaScript
if (typeof aiSeoUltra === 'undefined') {
    window.aiSeoUltra = {};
}
aiSeoUltra.postId = <?php echo json_encode($post->ID); ?>;
</script>

<style>
/* Additional styles for the meta box */
.ai-seo-advanced-options {
    border: 1px solid var(--ai-seo-border);
    border-radius: var(--ai-seo-radius);
    padding: 15px;
    margin-top: 15px;
}

.ai-seo-advanced-options summary {
    font-weight: 600;
    cursor: pointer;
    margin-bottom: 15px;
    outline: none;
}

.ai-seo-advanced-options[open] summary {
    margin-bottom: 15px;
    border-bottom: 1px solid var(--ai-seo-border);
    padding-bottom: 10px;
}

.ai-seo-char-counter {
    font-size: 12px;
    color: var(--ai-seo-text-light);
    text-align: right;
    margin-top: 5px;
}

.ai-seo-char-counter.warning {
    color: var(--ai-seo-warning);
    font-weight: 600;
}
</style>
