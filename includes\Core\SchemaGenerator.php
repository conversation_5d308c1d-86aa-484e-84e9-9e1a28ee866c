<?php
/**
 * Schema Generator Class
 * 
 * Generates structured data markup for improved SEO
 * 
 * @package AiSeoOptimizerUltra
 * @since 9.0.0
 */

namespace AiSeoOptimizerUltra\Core;

use Exception;

/**
 * Schema Generator Class
 * 
 * Creates JSON-LD structured data for various content types
 */
final class SchemaGenerator {
    private static $instance = null;
    private $schema_types = [];
    
    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Private constructor
     */
    private function __construct() {
        $this->init_schema_types();
    }
    
    /**
     * Initialize supported schema types
     */
    private function init_schema_types() {
        $this->schema_types = [
            'post' => 'Article',
            'page' => 'WebPage',
            'product' => 'Product',
            'event' => 'Event',
            'recipe' => 'Recipe',
            'review' => 'Review',
            'faq' => 'FAQPage',
            'howto' => 'HowTo'
        ];
    }
    
    /**
     * Generate schema markup for a post
     */
    public function generate_schema($post_id) {
        try {
            $post_id = absint($post_id);
            if (!$post_id) {
                return null;
            }
            
            // Check if schema is enabled
            if (!Options::get('ai_seo_ultra_schema_enabled', true)) {
                return null;
            }
            
            // Check cache first
            $cache_key = "schema_{$post_id}";
            $cached_schema = Cache::get($cache_key, 'schema');
            
            if ($cached_schema !== false) {
                return $cached_schema;
            }
            
            $post = get_post($post_id);
            if (!$post) {
                return null;
            }
            
            // Determine schema type
            $schema_type = $this->determine_schema_type($post);
            
            // Generate schema based on type
            $schema = $this->generate_schema_by_type($post, $schema_type);
            
            if ($schema) {
                // Cache the schema
                Cache::set($cache_key, $schema, 'schema', WEEK_IN_SECONDS);
                
                // Save to database
                update_post_meta($post_id, '_ai_seo_ultra_schema_data', wp_json_encode($schema));
                update_post_meta($post_id, '_ai_seo_ultra_schema_type', $schema_type);
                update_post_meta($post_id, '_ai_seo_ultra_schema_generated', current_time('mysql'));
            }
            
            return $schema;
            
        } catch (Exception $e) {
            Logger::error("Schema generation failed for post {$post_id}: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Determine schema type for post
     */
    private function determine_schema_type($post) {
        // Check for custom schema type
        $custom_type = get_post_meta($post->ID, '_ai_seo_ultra_schema_type', true);
        if (!empty($custom_type)) {
            return $custom_type;
        }
        
        // Determine by post type
        $post_type = $post->post_type;
        
        // Check for specific content patterns
        $content = $post->post_content;
        $title = $post->post_title;
        
        // Recipe detection
        if ($this->contains_recipe_markers($content)) {
            return 'Recipe';
        }
        
        // FAQ detection
        if ($this->contains_faq_markers($content, $title)) {
            return 'FAQPage';
        }
        
        // How-to detection
        if ($this->contains_howto_markers($content, $title)) {
            return 'HowTo';
        }
        
        // Review detection
        if ($this->contains_review_markers($content, $title)) {
            return 'Review';
        }
        
        // Default based on post type
        return $this->schema_types[$post_type] ?? 'Article';
    }
    
    /**
     * Generate schema by type
     */
    private function generate_schema_by_type($post, $schema_type) {
        switch ($schema_type) {
            case 'Article':
                return $this->generate_article_schema($post);
            case 'WebPage':
                return $this->generate_webpage_schema($post);
            case 'Recipe':
                return $this->generate_recipe_schema($post);
            case 'FAQPage':
                return $this->generate_faq_schema($post);
            case 'HowTo':
                return $this->generate_howto_schema($post);
            case 'Review':
                return $this->generate_review_schema($post);
            default:
                return $this->generate_article_schema($post);
        }
    }
    
    /**
     * Generate Article schema
     */
    private function generate_article_schema($post) {
        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'Article',
            'headline' => $post->post_title,
            'description' => $this->get_post_description($post),
            'url' => get_permalink($post->ID),
            'datePublished' => get_the_date('c', $post->ID),
            'dateModified' => get_the_modified_date('c', $post->ID),
            'author' => $this->get_author_schema($post),
            'publisher' => $this->get_publisher_schema(),
            'mainEntityOfPage' => [
                '@type' => 'WebPage',
                '@id' => get_permalink($post->ID)
            ]
        ];
        
        // Add featured image
        $image = $this->get_featured_image_schema($post->ID);
        if ($image) {
            $schema['image'] = $image;
        }
        
        // Add word count
        $word_count = str_word_count(wp_strip_all_tags($post->post_content));
        if ($word_count > 0) {
            $schema['wordCount'] = $word_count;
        }
        
        // Add categories
        $categories = get_the_category($post->ID);
        if ($categories) {
            $schema['articleSection'] = array_map(function($cat) {
                return $cat->name;
            }, array_slice($categories, 0, 3));
        }
        
        // Add tags
        $tags = get_the_tags($post->ID);
        if ($tags) {
            $schema['keywords'] = array_map(function($tag) {
                return $tag->name;
            }, array_slice($tags, 0, 10));
        }
        
        return $schema;
    }
    
    /**
     * Generate WebPage schema
     */
    private function generate_webpage_schema($post) {
        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'WebPage',
            'name' => $post->post_title,
            'description' => $this->get_post_description($post),
            'url' => get_permalink($post->ID),
            'datePublished' => get_the_date('c', $post->ID),
            'dateModified' => get_the_modified_date('c', $post->ID),
            'author' => $this->get_author_schema($post),
            'publisher' => $this->get_publisher_schema()
        ];
        
        // Add breadcrumb if available
        $breadcrumb = $this->generate_breadcrumb_schema($post);
        if ($breadcrumb) {
            $schema['breadcrumb'] = $breadcrumb;
        }
        
        return $schema;
    }
    
    /**
     * Generate Recipe schema
     */
    private function generate_recipe_schema($post) {
        $content = $post->post_content;
        
        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'Recipe',
            'name' => $post->post_title,
            'description' => $this->get_post_description($post),
            'url' => get_permalink($post->ID),
            'datePublished' => get_the_date('c', $post->ID),
            'author' => $this->get_author_schema($post)
        ];
        
        // Extract recipe data from content
        $recipe_data = $this->extract_recipe_data($content);
        $schema = array_merge($schema, $recipe_data);
        
        // Add featured image
        $image = $this->get_featured_image_schema($post->ID);
        if ($image) {
            $schema['image'] = $image;
        }
        
        return $schema;
    }
    
    /**
     * Generate FAQ schema
     */
    private function generate_faq_schema($post) {
        $content = $post->post_content;
        
        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'FAQPage',
            'name' => $post->post_title,
            'url' => get_permalink($post->ID)
        ];
        
        // Extract FAQ items
        $faq_items = $this->extract_faq_items($content);
        if (!empty($faq_items)) {
            $schema['mainEntity'] = $faq_items;
        }
        
        return $schema;
    }
    
    /**
     * Generate HowTo schema
     */
    private function generate_howto_schema($post) {
        $content = $post->post_content;
        
        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'HowTo',
            'name' => $post->post_title,
            'description' => $this->get_post_description($post),
            'url' => get_permalink($post->ID),
            'datePublished' => get_the_date('c', $post->ID),
            'author' => $this->get_author_schema($post)
        ];
        
        // Extract steps
        $steps = $this->extract_howto_steps($content);
        if (!empty($steps)) {
            $schema['step'] = $steps;
        }
        
        // Add featured image
        $image = $this->get_featured_image_schema($post->ID);
        if ($image) {
            $schema['image'] = $image;
        }
        
        return $schema;
    }
    
    /**
     * Generate Review schema
     */
    private function generate_review_schema($post) {
        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'Review',
            'name' => $post->post_title,
            'reviewBody' => $this->get_post_description($post),
            'url' => get_permalink($post->ID),
            'datePublished' => get_the_date('c', $post->ID),
            'author' => $this->get_author_schema($post)
        ];
        
        // Extract review data
        $review_data = $this->extract_review_data($post->post_content);
        $schema = array_merge($schema, $review_data);
        
        return $schema;
    }
    
    /**
     * Get author schema
     */
    private function get_author_schema($post) {
        $author_id = $post->post_author;
        $author_name = get_the_author_meta('display_name', $author_id);
        $author_url = get_author_posts_url($author_id);
        
        return [
            '@type' => 'Person',
            'name' => $author_name,
            'url' => $author_url
        ];
    }
    
    /**
     * Get publisher schema
     */
    private function get_publisher_schema() {
        $site_name = get_bloginfo('name');
        $site_url = home_url();
        
        $publisher = [
            '@type' => 'Organization',
            'name' => $site_name,
            'url' => $site_url
        ];
        
        // Add logo if available
        $custom_logo_id = get_theme_mod('custom_logo');
        if ($custom_logo_id) {
            $logo_url = wp_get_attachment_image_url($custom_logo_id, 'full');
            if ($logo_url) {
                $publisher['logo'] = [
                    '@type' => 'ImageObject',
                    'url' => $logo_url
                ];
            }
        }
        
        return $publisher;
    }
    
    /**
     * Get featured image schema
     */
    private function get_featured_image_schema($post_id) {
        if (!has_post_thumbnail($post_id)) {
            return null;
        }
        
        $image_id = get_post_thumbnail_id($post_id);
        $image_url = wp_get_attachment_image_url($image_id, 'large');
        
        if (!$image_url) {
            return null;
        }
        
        $image_meta = wp_get_attachment_metadata($image_id);
        
        $schema = [
            '@type' => 'ImageObject',
            'url' => $image_url
        ];
        
        if ($image_meta) {
            $schema['width'] = $image_meta['width'] ?? null;
            $schema['height'] = $image_meta['height'] ?? null;
        }
        
        return $schema;
    }
    
    /**
     * Get post description
     */
    private function get_post_description($post) {
        // Try excerpt first
        if (!empty($post->post_excerpt)) {
            return $post->post_excerpt;
        }
        
        // Generate from content
        $content = wp_strip_all_tags($post->post_content);
        return wp_trim_words($content, 25);
    }
    
    /**
     * Content detection methods
     */
    private function contains_recipe_markers($content) {
        $markers = ['ingredients', 'instructions', 'recipe', 'cooking time', 'prep time'];
        $content_lower = strtolower($content);
        
        $found = 0;
        foreach ($markers as $marker) {
            if (strpos($content_lower, $marker) !== false) {
                $found++;
            }
        }
        
        return $found >= 2;
    }
    
    private function contains_faq_markers($content, $title) {
        $markers = ['faq', 'frequently asked', 'questions', 'q:', 'a:'];
        $text = strtolower($content . ' ' . $title);
        
        $found = 0;
        foreach ($markers as $marker) {
            if (strpos($text, $marker) !== false) {
                $found++;
            }
        }
        
        return $found >= 2;
    }
    
    private function contains_howto_markers($content, $title) {
        $markers = ['how to', 'step', 'tutorial', 'guide', 'instructions'];
        $text = strtolower($content . ' ' . $title);
        
        $found = 0;
        foreach ($markers as $marker) {
            if (strpos($text, $marker) !== false) {
                $found++;
            }
        }
        
        return $found >= 2;
    }
    
    private function contains_review_markers($content, $title) {
        $markers = ['review', 'rating', 'stars', 'pros', 'cons'];
        $text = strtolower($content . ' ' . $title);
        
        $found = 0;
        foreach ($markers as $marker) {
            if (strpos($text, $marker) !== false) {
                $found++;
            }
        }
        
        return $found >= 2;
    }
    
    /**
     * Data extraction methods (simplified)
     */
    private function extract_recipe_data($content) {
        // Simplified recipe data extraction
        return [
            'recipeCategory' => 'Main Course',
            'recipeCuisine' => 'International'
        ];
    }
    
    private function extract_faq_items($content) {
        // Simplified FAQ extraction
        return [];
    }
    
    private function extract_howto_steps($content) {
        // Simplified step extraction
        return [];
    }
    
    private function extract_review_data($content) {
        // Simplified review data extraction
        return [
            'reviewRating' => [
                '@type' => 'Rating',
                'ratingValue' => 4,
                'bestRating' => 5
            ]
        ];
    }
    
    private function generate_breadcrumb_schema($post) {
        // Simplified breadcrumb generation
        return null;
    }
    
    /**
     * Prevent cloning and unserialization
     */
    private function __clone() {}
    public function __wakeup() {
        throw new Exception('Cannot unserialize singleton');
    }
}
