<?php
/**
 * Simple test script to verify plugin structure
 * 
 * This script tests the basic plugin loading without WordPress
 */

// Simulate WordPress constants and functions for testing
if (!defined('ABSPATH')) {
    define('ABSPATH', __DIR__ . '/');
}

if (!defined('DAY_IN_SECONDS')) {
    define('DAY_IN_SECONDS', 86400);
}

if (!defined('HOUR_IN_SECONDS')) {
    define('HOUR_IN_SECONDS', 3600);
}

if (!defined('WEEK_IN_SECONDS')) {
    define('WEEK_IN_SECONDS', 604800);
}

// Mock WordPress functions for testing
if (!function_exists('plugin_dir_path')) {
    function plugin_dir_path($file) {
        return dirname($file) . '/';
    }
}

if (!function_exists('plugin_dir_url')) {
    function plugin_dir_url($file) {
        return 'http://example.com/wp-content/plugins/' . basename(dirname($file)) . '/';
    }
}

if (!function_exists('plugin_basename')) {
    function plugin_basename($file) {
        return basename(dirname($file)) . '/' . basename($file);
    }
}

if (!function_exists('add_action')) {
    function add_action($hook, $callback, $priority = 10, $accepted_args = 1) {
        // Mock function for testing
        return true;
    }
}

if (!function_exists('register_activation_hook')) {
    function register_activation_hook($file, $callback) {
        // Mock function for testing
        return true;
    }
}

if (!function_exists('register_deactivation_hook')) {
    function register_deactivation_hook($file, $callback) {
        // Mock function for testing
        return true;
    }
}

if (!function_exists('is_admin')) {
    function is_admin() {
        return false;
    }
}

if (!function_exists('wp_doing_ajax')) {
    function wp_doing_ajax() {
        return false;
    }
}

if (!function_exists('get_bloginfo')) {
    function get_bloginfo($show = '') {
        return 'Test Site';
    }
}

if (!function_exists('size_format')) {
    function size_format($bytes, $decimals = 0) {
        $sz = 'BKMGTP';
        $factor = floor((strlen($bytes) - 1) / 3);
        return sprintf("%.{$decimals}f", $bytes / pow(1024, $factor)) . @$sz[$factor];
    }
}

// Test the plugin loading
echo "Testing AI SEO Optimizer Ultra Plugin...\n\n";

try {
    // Test autoloader
    echo "1. Testing autoloader...\n";
    
    // Include the main plugin file
    require_once __DIR__ . '/ai-seo-optimizer-ultra.php';
    
    echo "✓ Plugin file loaded successfully\n";
    
    // Test if constants are defined
    echo "\n2. Testing constants...\n";
    $constants = [
        'AI_SEO_ULTRA_VERSION',
        'AI_SEO_ULTRA_PLUGIN_FILE',
        'AI_SEO_ULTRA_PLUGIN_DIR',
        'AI_SEO_ULTRA_PLUGIN_URL',
        'AI_SEO_ULTRA_PLUGIN_BASENAME'
    ];
    
    foreach ($constants as $constant) {
        if (defined($constant)) {
            echo "✓ {$constant}: " . constant($constant) . "\n";
        } else {
            echo "✗ {$constant} not defined\n";
        }
    }
    
    // Test class loading
    echo "\n3. Testing class autoloading...\n";
    $classes = [
        'AiSeoOptimizerUltra\\Core\\Logger',
        'AiSeoOptimizerUltra\\Core\\Cache',
        'AiSeoOptimizerUltra\\Core\\Options',
        'AiSeoOptimizerUltra\\Core\\Security',
        'AiSeoOptimizerUltra\\Core\\Database',
        'AiSeoOptimizerUltra\\Core\\ApiClient',
        'AiSeoOptimizerUltra\\Core\\SeoProcessor'
    ];
    
    foreach ($classes as $class) {
        if (class_exists($class)) {
            echo "✓ {$class} loaded\n";
        } else {
            echo "✗ {$class} not found\n";
        }
    }
    
    // Test monitor class
    echo "\n4. Testing monitor class...\n";
    if (class_exists('AiSeoUltraMonitor')) {
        echo "✓ AiSeoUltraMonitor class exists\n";
        
        // Test memory check
        try {
            AiSeoUltraMonitor::check_memory_limit('test');
            echo "✓ Memory monitoring works\n";
        } catch (Exception $e) {
            echo "✗ Memory monitoring failed: " . $e->getMessage() . "\n";
        }
    } else {
        echo "✗ AiSeoUltraMonitor class not found\n";
    }
    
    // Test main plugin class
    echo "\n5. Testing main plugin class...\n";
    if (class_exists('AiSeoOptimizerUltraMain')) {
        echo "✓ AiSeoOptimizerUltraMain class exists\n";
        
        // Test singleton
        $instance1 = AiSeoOptimizerUltraMain::get_instance();
        $instance2 = AiSeoOptimizerUltraMain::get_instance();
        
        if ($instance1 === $instance2) {
            echo "✓ Singleton pattern works\n";
        } else {
            echo "✗ Singleton pattern failed\n";
        }
    } else {
        echo "✗ AiSeoOptimizerUltraMain class not found\n";
    }
    
    echo "\n6. Testing file structure...\n";
    $files = [
        'assets/css/admin.css',
        'assets/js/admin.js',
        'templates/meta-box.php',
        'includes/Core/CoreManager.php',
        'includes/Core/Logger.php',
        'includes/Core/Cache.php',
        'includes/Core/Security.php',
        'includes/Core/Options.php',
        'includes/Core/Database.php',
        'includes/Core/ApiClient.php',
        'includes/Core/SeoProcessor.php'
    ];
    
    foreach ($files as $file) {
        if (file_exists(__DIR__ . '/' . $file)) {
            echo "✓ {$file} exists\n";
        } else {
            echo "✗ {$file} missing\n";
        }
    }
    
    echo "\n🎉 Plugin structure test completed!\n";
    echo "\nSummary:\n";
    echo "- Plugin loads without fatal errors\n";
    echo "- Autoloader works correctly\n";
    echo "- Core classes are accessible\n";
    echo "- File structure is complete\n";
    echo "- Memory monitoring is functional\n";
    echo "- Singleton pattern implemented correctly\n";
    
    echo "\n✅ The plugin is ready for WordPress installation!\n";
    
} catch (Exception $e) {
    echo "\n❌ Test failed with error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
} catch (Error $e) {
    echo "\n❌ Fatal error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "AI SEO Optimizer Ultra v9.0.0 - Test Complete\n";
echo str_repeat("=", 60) . "\n";
?>
