<?php
/**
 * Security Class
 * 
 * Comprehensive security layer with input validation, sanitization, and access control
 * 
 * @package AiSeoOptimizerUltra
 * @since 9.0.0
 */

namespace AiSeoOptimizerUltra\Core;

use Exception;

/**
 * Security Class
 * 
 * Handles all security-related functionality
 */
final class Security {
    private static $instance = null;
    private $nonce_actions = [
        'ai_seo_ultra_ajax' => 'ai_seo_ultra_ajax_nonce',
        'ai_seo_ultra_meta_box' => 'ai_seo_ultra_meta_box_nonce',
        'ai_seo_ultra_settings' => 'ai_seo_ultra_settings_nonce',
        'ai_seo_ultra_bulk_process' => 'ai_seo_ultra_bulk_process_nonce'
    ];
    
    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Private constructor
     */
    private function __construct() {
        $this->init_security_hooks();
    }
    
    /**
     * Initialize security hooks
     */
    private function init_security_hooks() {
        // Rate limiting for AJAX requests
        add_action('wp_ajax_ai_seo_process_post', [$this, 'check_rate_limit'], 1);
        add_action('wp_ajax_ai_seo_bulk_process', [$this, 'check_rate_limit'], 1);
        
        // Security headers
        add_action('send_headers', [$this, 'add_security_headers']);
        
        // Input sanitization filters
        add_filter('ai_seo_ultra_sanitize_input', [$this, 'sanitize_input'], 10, 2);
    }
    
    /**
     * Verify nonce for AJAX requests
     */
    public static function verify_ajax_nonce($action = 'ai_seo_ultra_ajax') {
        $instance = self::get_instance();
        return $instance->verify_nonce($action, 'ajax');
    }
    
    /**
     * Verify nonce for form submissions
     */
    public static function verify_form_nonce($action) {
        $instance = self::get_instance();
        return $instance->verify_nonce($action, 'form');
    }
    
    /**
     * Verify nonce for post save operations
     */
    public static function verify_post_save_nonce() {
        return self::verify_form_nonce('ai_seo_ultra_meta_box');
    }
    
    /**
     * Create nonce for action
     */
    public static function create_nonce($action) {
        return wp_create_nonce($action);
    }
    
    /**
     * Get nonce field HTML
     */
    public static function get_nonce_field($action, $name = null) {
        $name = $name ?: ($instance = self::get_instance())->nonce_actions[$action] ?? $action . '_nonce';
        return wp_nonce_field($action, $name, true, false);
    }
    
    /**
     * Verify nonce
     */
    private function verify_nonce($action, $context = 'ajax') {
        try {
            $nonce_field = $this->nonce_actions[$action] ?? $action . '_nonce';
            
            if ($context === 'ajax') {
                $nonce = $_POST[$nonce_field] ?? $_GET[$nonce_field] ?? '';
            } else {
                $nonce = $_POST[$nonce_field] ?? '';
            }
            
            if (empty($nonce)) {
                Logger::warning("Missing nonce for action: {$action}");
                return false;
            }
            
            $verified = wp_verify_nonce($nonce, $action);
            
            if (!$verified) {
                Logger::warning("Invalid nonce for action: {$action}", [
                    'user_id' => get_current_user_id(),
                    'ip' => $this->get_client_ip(),
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
                ]);
                return false;
            }
            
            return true;
            
        } catch (Exception $e) {
            Logger::error("Nonce verification error for action {$action}: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Check user capabilities
     */
    public static function check_capability($capability, $post_id = null) {
        if ($post_id) {
            return current_user_can($capability, $post_id);
        }
        return current_user_can($capability);
    }
    
    /**
     * Check if user can edit posts
     */
    public static function can_edit_posts() {
        return current_user_can('edit_posts');
    }
    
    /**
     * Check if user can manage options
     */
    public static function can_manage_options() {
        return current_user_can('manage_options');
    }
    
    /**
     * Check if user can edit specific post
     */
    public static function can_edit_post($post_id) {
        return current_user_can('edit_post', $post_id);
    }
    
    /**
     * Sanitize input data
     */
    public static function sanitize_input($value, $type = 'text') {
        $instance = self::get_instance();
        return $instance->sanitize_value($value, $type);
    }
    
    /**
     * Sanitize value based on type
     */
    private function sanitize_value($value, $type) {
        switch ($type) {
            case 'text':
                return sanitize_text_field($value);
                
            case 'textarea':
                return sanitize_textarea_field($value);
                
            case 'email':
                return sanitize_email($value);
                
            case 'url':
                return esc_url_raw($value);
                
            case 'html':
                return wp_kses_post($value);
                
            case 'int':
                return intval($value);
                
            case 'float':
                return floatval($value);
                
            case 'bool':
                return (bool) $value;
                
            case 'array':
                if (!is_array($value)) {
                    return [];
                }
                return array_map([$this, 'sanitize_array_value'], $value);
                
            case 'json':
                if (is_string($value)) {
                    $decoded = json_decode($value, true);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        return $this->sanitize_value($decoded, 'array');
                    }
                }
                return [];
                
            case 'slug':
                return sanitize_title($value);
                
            case 'key':
                return sanitize_key($value);
                
            default:
                return sanitize_text_field($value);
        }
    }
    
    /**
     * Sanitize array values recursively
     */
    private function sanitize_array_value($value) {
        if (is_array($value)) {
            return array_map([$this, 'sanitize_array_value'], $value);
        }
        return sanitize_text_field($value);
    }
    
    /**
     * Validate API key format
     */
    public static function validate_api_key($api_key) {
        if (empty($api_key)) {
            return false;
        }
        
        // Basic validation - adjust based on your API provider requirements
        if (strlen($api_key) < 20 || strlen($api_key) > 200) {
            return false;
        }
        
        // Check for valid characters (alphanumeric, hyphens, underscores)
        if (!preg_match('/^[a-zA-Z0-9_-]+$/', $api_key)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Rate limiting for AJAX requests
     */
    public function check_rate_limit() {
        $user_id = get_current_user_id();
        $ip = $this->get_client_ip();
        $action = $_POST['action'] ?? '';
        
        // Create rate limit key
        $rate_limit_key = "ai_seo_rate_limit_{$user_id}_{$ip}_{$action}";
        
        // Get current count
        $current_count = get_transient($rate_limit_key);
        
        // Set limits based on action
        $limits = [
            'ai_seo_process_post' => ['count' => 10, 'period' => MINUTE_IN_SECONDS],
            'ai_seo_bulk_process' => ['count' => 3, 'period' => MINUTE_IN_SECONDS],
            'default' => ['count' => 20, 'period' => MINUTE_IN_SECONDS]
        ];
        
        $limit = $limits[$action] ?? $limits['default'];
        
        if ($current_count === false) {
            // First request
            set_transient($rate_limit_key, 1, $limit['period']);
        } else {
            if ($current_count >= $limit['count']) {
                Logger::warning("Rate limit exceeded for action: {$action}", [
                    'user_id' => $user_id,
                    'ip' => $ip,
                    'count' => $current_count
                ]);
                
                wp_send_json_error([
                    'message' => 'Rate limit exceeded. Please wait before trying again.',
                    'retry_after' => $limit['period']
                ], 429);
            }
            
            // Increment count
            set_transient($rate_limit_key, $current_count + 1, $limit['period']);
        }
    }
    
    /**
     * Get client IP address
     */
    private function get_client_ip() {
        $ip_headers = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',            // Proxy
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxy
            'HTTP_FORWARDED',            // Proxy
            'REMOTE_ADDR'                // Standard
        ];
        
        foreach ($ip_headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ip = $_SERVER[$header];
                
                // Handle comma-separated IPs (X-Forwarded-For)
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                // Validate IP
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * Add security headers
     */
    public function add_security_headers() {
        // Only add headers for our plugin pages
        if (!$this->is_plugin_page()) {
            return;
        }
        
        // Content Security Policy for admin pages
        if (is_admin()) {
            header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:;");
        }
        
        // Prevent clickjacking
        header('X-Frame-Options: SAMEORIGIN');
        
        // Prevent MIME type sniffing
        header('X-Content-Type-Options: nosniff');
        
        // XSS Protection
        header('X-XSS-Protection: 1; mode=block');
        
        // Referrer Policy
        header('Referrer-Policy: strict-origin-when-cross-origin');
    }
    
    /**
     * Check if current page is a plugin page
     */
    private function is_plugin_page() {
        if (!is_admin()) {
            return false;
        }
        
        $screen = get_current_screen();
        if (!$screen) {
            return false;
        }
        
        return strpos($screen->id, 'ai-seo-ultra') !== false;
    }
    
    /**
     * Escape output for HTML context
     */
    public static function escape_html($value) {
        return esc_html($value);
    }
    
    /**
     * Escape output for attribute context
     */
    public static function escape_attr($value) {
        return esc_attr($value);
    }
    
    /**
     * Escape output for URL context
     */
    public static function escape_url($value) {
        return esc_url($value);
    }
    
    /**
     * Escape output for JavaScript context
     */
    public static function escape_js($value) {
        return esc_js($value);
    }
    
    /**
     * Log security event
     */
    private function log_security_event($event, $details = []) {
        Logger::warning("Security event: {$event}", array_merge($details, [
            'user_id' => get_current_user_id(),
            'ip' => $this->get_client_ip(),
            'timestamp' => current_time('mysql'),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]));
    }
    
    /**
     * Prevent cloning and unserialization
     */
    private function __clone() {}
    public function __wakeup() {
        throw new Exception('Cannot unserialize singleton');
    }
}
