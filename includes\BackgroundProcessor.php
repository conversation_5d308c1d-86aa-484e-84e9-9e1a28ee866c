<?php
/**
 * Background processing for large operations
 * 
 * @package UltraFeaturedImageOptimizer
 * @subpackage BackgroundProcessor
 */

namespace UltraFeaturedImageOptimizer;

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Efficient background processing with queue management
 */
class BackgroundProcessor {
    
    /**
     * Processing batch size
     */
    const BATCH_SIZE = 5;
    const MAX_EXECUTION_TIME = 25; // seconds
    const MEMORY_LIMIT_THRESHOLD = 0.8; // 80% of memory limit
    
    /**
     * Queue priorities
     */
    const PRIORITY_LOW = 1;
    const PRIORITY_NORMAL = 5;
    const PRIORITY_HIGH = 8;
    const PRIORITY_CRITICAL = 10;
    
    /**
     * Dependencies
     */
    private $database;
    private $cache;
    private $logger;
    private $image_processor;
    
    /**
     * Processing state
     * @var array
     */
    private $processing_state = [
        'start_time' => 0,
        'processed_count' => 0,
        'error_count' => 0,
        'memory_start' => 0,
    ];
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->database = new Database();
        $this->cache = new Cache();
        $this->logger = new Logger();
        $this->image_processor = new ImageProcessor();
    }
    
    /**
     * Initialize background processor
     */
    public function init() {
        // Register AJAX handlers for manual processing
        add_action('wp_ajax_ufio_process_batch', [$this, 'ajax_process_batch']);
        add_action('wp_ajax_ufio_process_single', [$this, 'ajax_process_single']);
        add_action('wp_ajax_ufio_get_queue_status', [$this, 'ajax_get_queue_status']);
        
        // Register cron handlers
        add_action('ufio_background_process', [$this, 'process_queue']);
        add_action('ufio_process_single_item', [$this, 'process_single_item'], 10, 2);
        
        // Register shutdown handler for cleanup
        register_shutdown_function([$this, 'shutdown_handler']);
    }
    
    /**
     * Add item to processing queue
     * 
     * @param string $action Action to perform
     * @param int $item_id Item ID
     * @param string $item_type Item type
     * @param int $priority Priority level
     * @param array $metadata Additional metadata
     * @return int|false Queue item ID or false on failure
     */
    public function add_to_queue($action, $item_id, $item_type = 'post', $priority = self::PRIORITY_NORMAL, $metadata = []) {
        $queue_data = [
            'item_type' => $item_type,
            'item_id' => $item_id,
            'action' => $action,
            'priority' => $priority,
            'status' => 'pending',
            'scheduled_at' => current_time('mysql'),
        ];
        
        if (!empty($metadata)) {
            $queue_data['metadata'] = json_encode($metadata);
        }
        
        $queue_id = $this->database->add_to_queue($queue_data);
        
        if ($queue_id) {
            $this->logger->debug('Added item to queue', [
                'queue_id' => $queue_id,
                'action' => $action,
                'item_id' => $item_id,
                'item_type' => $item_type,
                'priority' => $priority,
            ]);
            
            // Schedule immediate processing for high priority items
            if ($priority >= self::PRIORITY_HIGH) {
                wp_schedule_single_event(time() + 10, 'ufio_process_single_item', [$queue_id, $action]);
            }
        }
        
        return $queue_id;
    }
    
    /**
     * Process queue items
     */
    public function process_queue() {
        if (!$this->can_process()) {
            $this->logger->debug('Background processing skipped - conditions not met');
            return;
        }
        
        $tracking_id = $this->logger->start_performance_tracking('background_process_queue');
        $this->init_processing_state();
        
        try {
            $items = $this->database->get_queue_items(self::BATCH_SIZE);
            
            if (empty($items)) {
                $this->logger->debug('No queue items to process');
                return;
            }
            
            $this->logger->info('Starting background processing', [
                'items_count' => count($items),
                'batch_size' => self::BATCH_SIZE,
            ]);
            
            foreach ($items as $item) {
                if (!$this->should_continue_processing()) {
                    $this->logger->info('Stopping processing - resource limits reached');
                    break;
                }
                
                $this->process_queue_item($item);
                $this->processing_state['processed_count']++;
                
                // Small delay to prevent overwhelming the system
                usleep(100000); // 0.1 second
            }
            
            $this->logger->info('Background processing completed', [
                'processed' => $this->processing_state['processed_count'],
                'errors' => $this->processing_state['error_count'],
                'execution_time' => time() - $this->processing_state['start_time'],
            ]);
            
        } catch (\Exception $e) {
            $this->logger->error('Background processing failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
        } finally {
            $this->logger->end_performance_tracking($tracking_id, [
                'processed_count' => $this->processing_state['processed_count'],
                'error_count' => $this->processing_state['error_count'],
            ]);
        }
    }
    
    /**
     * Process single queue item
     * 
     * @param object $item Queue item
     */
    private function process_queue_item($item) {
        $this->database->update_queue_item($item->id, 'processing');
        
        try {
            $result = $this->execute_action($item);
            
            if ($result['success']) {
                $this->database->update_queue_item($item->id, 'completed');
                $this->logger->debug('Queue item processed successfully', [
                    'queue_id' => $item->id,
                    'action' => $item->action,
                    'item_id' => $item->item_id,
                ]);
            } else {
                $this->handle_processing_failure($item, $result['error'] ?? 'Unknown error');
            }
            
        } catch (\Exception $e) {
            $this->handle_processing_failure($item, $e->getMessage());
        }
    }
    
    /**
     * Execute queue item action
     * 
     * @param object $item Queue item
     * @return array Result with success status and data
     */
    private function execute_action($item) {
        switch ($item->action) {
            case 'process_featured_image':
                return $this->process_featured_image_action($item);
                
            case 'process_content_images':
                return $this->process_content_images_action($item);
                
            case 'generate_alt_text':
                return $this->generate_alt_text_action($item);
                
            case 'optimize_image':
                return $this->optimize_image_action($item);
                
            case 'bulk_process_posts':
                return $this->bulk_process_posts_action($item);
                
            default:
                return [
                    'success' => false,
                    'error' => 'Unknown action: ' . $item->action,
                ];
        }
    }
    
    /**
     * Process featured image action
     * 
     * @param object $item Queue item
     * @return array Result
     */
    private function process_featured_image_action($item) {
        try {
            $post = get_post($item->item_id);
            if (!$post) {
                return ['success' => false, 'error' => 'Post not found'];
            }
            
            $result = $this->image_processor->process_featured_image($post);
            
            return [
                'success' => true,
                'data' => $result,
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }
    
    /**
     * Process content images action
     * 
     * @param object $item Queue item
     * @return array Result
     */
    private function process_content_images_action($item) {
        try {
            $post = get_post($item->item_id);
            if (!$post) {
                return ['success' => false, 'error' => 'Post not found'];
            }
            
            $result = $this->image_processor->process_content_images($post);
            
            return [
                'success' => true,
                'data' => $result,
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }
    
    /**
     * Generate alt text action
     * 
     * @param object $item Queue item
     * @return array Result
     */
    private function generate_alt_text_action($item) {
        try {
            $ai_handler = new AIHandler();
            $metadata = json_decode($item->metadata ?? '{}', true);
            $context = $metadata['context'] ?? '';
            
            $alt_text = $ai_handler->generate_alt_text($item->item_id, $context);
            
            if ($alt_text) {
                update_post_meta($item->item_id, '_wp_attachment_image_alt', $alt_text);
                
                return [
                    'success' => true,
                    'data' => ['alt_text' => $alt_text],
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'Failed to generate alt text',
                ];
            }
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }
    
    /**
     * Optimize image action
     * 
     * @param object $item Queue item
     * @return array Result
     */
    private function optimize_image_action($item) {
        try {
            // Placeholder for image optimization logic
            // This would include compression, WebP conversion, etc.
            
            return [
                'success' => true,
                'data' => ['optimized' => true],
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }
    
    /**
     * Bulk process posts action
     * 
     * @param object $item Queue item
     * @return array Result
     */
    private function bulk_process_posts_action($item) {
        try {
            $metadata = json_decode($item->metadata ?? '{}', true);
            $post_ids = $metadata['post_ids'] ?? [];
            
            if (empty($post_ids)) {
                return ['success' => false, 'error' => 'No post IDs provided'];
            }
            
            $processed = 0;
            $errors = 0;
            
            foreach ($post_ids as $post_id) {
                try {
                    $post = get_post($post_id);
                    if ($post) {
                        $this->image_processor->process_post_images($post_id);
                        $processed++;
                    }
                } catch (\Exception $e) {
                    $errors++;
                    $this->logger->error('Failed to process post in bulk', [
                        'post_id' => $post_id,
                        'error' => $e->getMessage(),
                    ]);
                }
                
                // Check if we should continue
                if (!$this->should_continue_processing()) {
                    break;
                }
            }
            
            return [
                'success' => true,
                'data' => [
                    'processed' => $processed,
                    'errors' => $errors,
                    'total' => count($post_ids),
                ],
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }
    
    /**
     * Handle processing failure
     * 
     * @param object $item Queue item
     * @param string $error Error message
     */
    private function handle_processing_failure($item, $error) {
        $this->processing_state['error_count']++;
        
        if ($item->attempts >= $item->max_attempts) {
            $this->database->update_queue_item($item->id, 'failed', $error);
            $this->logger->error('Queue item failed permanently', [
                'queue_id' => $item->id,
                'action' => $item->action,
                'item_id' => $item->item_id,
                'attempts' => $item->attempts,
                'error' => $error,
            ]);
        } else {
            // Reschedule with exponential backoff
            $delay = min(300, pow(2, $item->attempts) * 60); // Max 5 minutes
            $scheduled_at = date('Y-m-d H:i:s', time() + $delay);
            
            global $wpdb;
            $wpdb->update(
                $wpdb->prefix . 'ufio_processing_queue',
                [
                    'status' => 'pending',
                    'scheduled_at' => $scheduled_at,
                    'error_message' => $error,
                ],
                ['id' => $item->id],
                ['%s', '%s', '%s'],
                ['%d']
            );
            
            $this->logger->warning('Queue item rescheduled after failure', [
                'queue_id' => $item->id,
                'attempts' => $item->attempts,
                'delay' => $delay,
                'error' => $error,
            ]);
        }
    }
    
    /**
     * Check if background processing can run
     * 
     * @return bool
     */
    private function can_process() {
        // Check if background processing is enabled
        if (!ufio()->get_option('background_processing', true)) {
            return false;
        }
        
        // Check if another process is already running
        $lock_key = 'ufio_background_processing_lock';
        $lock_value = get_transient($lock_key);
        
        if ($lock_value && $lock_value > time() - 300) { // 5 minute lock
            return false;
        }
        
        // Set lock
        set_transient($lock_key, time(), 300);
        
        return true;
    }
    
    /**
     * Initialize processing state
     */
    private function init_processing_state() {
        $this->processing_state = [
            'start_time' => time(),
            'processed_count' => 0,
            'error_count' => 0,
            'memory_start' => memory_get_usage(true),
        ];
    }
    
    /**
     * Check if processing should continue
     * 
     * @return bool
     */
    private function should_continue_processing() {
        // Check execution time
        if (time() - $this->processing_state['start_time'] > self::MAX_EXECUTION_TIME) {
            return false;
        }
        
        // Check memory usage
        $memory_limit = $this->get_memory_limit();
        $current_memory = memory_get_usage(true);
        
        if ($memory_limit > 0 && $current_memory > ($memory_limit * self::MEMORY_LIMIT_THRESHOLD)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Get PHP memory limit in bytes
     * 
     * @return int Memory limit in bytes
     */
    private function get_memory_limit() {
        $memory_limit = ini_get('memory_limit');
        
        if ($memory_limit == -1) {
            return 0; // Unlimited
        }
        
        $unit = strtolower(substr($memory_limit, -1));
        $value = (int) $memory_limit;
        
        switch ($unit) {
            case 'g':
                $value *= 1024 * 1024 * 1024;
                break;
            case 'm':
                $value *= 1024 * 1024;
                break;
            case 'k':
                $value *= 1024;
                break;
        }
        
        return $value;
    }
    
    /**
     * Get queue status
     * 
     * @return array Queue status
     */
    public function get_queue_status() {
        global $wpdb;
        
        $stats = $wpdb->get_results(
            "SELECT status, COUNT(*) as count 
             FROM {$wpdb->prefix}ufio_processing_queue 
             GROUP BY status",
            ARRAY_A
        );
        
        $status = [
            'pending' => 0,
            'processing' => 0,
            'completed' => 0,
            'failed' => 0,
        ];
        
        foreach ($stats as $stat) {
            $status[$stat['status']] = (int) $stat['count'];
        }
        
        return $status;
    }
    
    /**
     * Clear completed queue items
     * 
     * @param int $days Days to keep completed items
     */
    public function cleanup_queue($days = 7) {
        global $wpdb;
        
        $cutoff_date = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        $deleted = $wpdb->query($wpdb->prepare(
            "DELETE FROM {$wpdb->prefix}ufio_processing_queue 
             WHERE status IN ('completed', 'failed') AND completed_at < %s",
            $cutoff_date
        ));
        
        $this->logger->info('Queue cleanup completed', [
            'deleted_items' => $deleted,
            'cutoff_date' => $cutoff_date,
        ]);
        
        return $deleted;
    }
    
    /**
     * Shutdown handler for cleanup
     */
    public function shutdown_handler() {
        // Release processing lock
        delete_transient('ufio_background_processing_lock');
    }
    
    // AJAX handlers
    public function ajax_process_batch() {
        check_ajax_referer('ufio_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
        }
        
        $this->process_queue();
        
        wp_send_json_success([
            'message' => 'Batch processing completed',
            'status' => $this->get_queue_status(),
        ]);
    }
    
    public function ajax_process_single() {
        check_ajax_referer('ufio_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
        }
        
        $item_id = isset($_POST['item_id']) ? absint($_POST['item_id']) : 0;
        $action = isset($_POST['action_type']) ? sanitize_text_field($_POST['action_type']) : 'process_featured_image';
        
        if (!$item_id) {
            wp_send_json_error(['message' => 'Invalid item ID']);
        }
        
        $queue_id = $this->add_to_queue($action, $item_id, 'post', self::PRIORITY_HIGH);
        
        if ($queue_id) {
            wp_send_json_success([
                'message' => 'Item added to queue',
                'queue_id' => $queue_id,
            ]);
        } else {
            wp_send_json_error(['message' => 'Failed to add item to queue']);
        }
    }
    
    public function ajax_get_queue_status() {
        check_ajax_referer('ufio_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
        }
        
        wp_send_json_success([
            'status' => $this->get_queue_status(),
        ]);
    }
}
