<?php
/**
 * Options Class
 * 
 * Manages plugin options with caching and validation
 * 
 * @package AiSeoOptimizerUltra
 * @since 9.0.0
 */

namespace AiSeoOptimizerUltra\Core;

use Exception;

/**
 * Options Class
 * 
 * Centralized options management with caching
 */
final class Options {
    private static $instance = null;
    private $options_cache = [];
    private $option_definitions = [];
    
    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Private constructor
     */
    private function __construct() {
        $this->define_options();
    }
    
    /**
     * Define all plugin options with defaults and validation
     */
    private function define_options() {
        $this->option_definitions = [
            'ai_seo_ultra_api_key' => [
                'default' => '',
                'type' => 'string',
                'sanitize' => 'text',
                'validate' => 'api_key'
            ],
            'ai_seo_ultra_api_provider' => [
                'default' => 'openai',
                'type' => 'string',
                'sanitize' => 'key',
                'validate' => 'api_provider'
            ],
            'ai_seo_ultra_api_endpoint' => [
                'default' => '',
                'type' => 'string',
                'sanitize' => 'url',
                'validate' => 'url'
            ],
            'ai_seo_ultra_model' => [
                'default' => 'gpt-3.5-turbo',
                'type' => 'string',
                'sanitize' => 'text',
                'validate' => 'model'
            ],
            'ai_seo_ultra_target_audience' => [
                'default' => '',
                'type' => 'string',
                'sanitize' => 'textarea',
                'validate' => 'text'
            ],
            'ai_seo_ultra_desired_tone' => [
                'default' => 'professional',
                'type' => 'string',
                'sanitize' => 'key',
                'validate' => 'tone'
            ],
            'ai_seo_ultra_elements_to_optimize' => [
                'default' => ['title', 'description', 'h1'],
                'type' => 'array',
                'sanitize' => 'array',
                'validate' => 'elements'
            ],
            'ai_seo_ultra_include_cta' => [
                'default' => false,
                'type' => 'boolean',
                'sanitize' => 'bool',
                'validate' => 'bool'
            ],
            'ai_seo_ultra_custom_instructions' => [
                'default' => '',
                'type' => 'string',
                'sanitize' => 'textarea',
                'validate' => 'text'
            ],
            'ai_seo_ultra_debug_mode' => [
                'default' => false,
                'type' => 'boolean',
                'sanitize' => 'bool',
                'validate' => 'bool'
            ],
            'ai_seo_ultra_schema_enabled' => [
                'default' => true,
                'type' => 'boolean',
                'sanitize' => 'bool',
                'validate' => 'bool'
            ],
            'ai_seo_ultra_auto_process' => [
                'default' => false,
                'type' => 'boolean',
                'sanitize' => 'bool',
                'validate' => 'bool'
            ],
            'ai_seo_ultra_max_requests_per_hour' => [
                'default' => 100,
                'type' => 'integer',
                'sanitize' => 'int',
                'validate' => 'positive_int'
            ],
            'ai_seo_ultra_cache_duration' => [
                'default' => DAY_IN_SECONDS,
                'type' => 'integer',
                'sanitize' => 'int',
                'validate' => 'positive_int'
            ]
        ];
    }
    
    /**
     * Get option value
     */
    public static function get($option_name, $default = null) {
        $instance = self::get_instance();
        return $instance->get_option($option_name, $default);
    }
    
    /**
     * Set option value
     */
    public static function set($option_name, $value) {
        $instance = self::get_instance();
        return $instance->set_option($option_name, $value);
    }
    
    /**
     * Delete option
     */
    public static function delete($option_name) {
        $instance = self::get_instance();
        return $instance->delete_option($option_name);
    }
    
    /**
     * Get all plugin options
     */
    public static function get_all() {
        $instance = self::get_instance();
        return $instance->get_all_options();
    }
    
    /**
     * Set default options
     */
    public static function set_defaults() {
        $instance = self::get_instance();
        return $instance->set_default_options();
    }
    
    /**
     * Internal get option method
     */
    private function get_option($option_name, $default) {
        try {
            // Check cache first
            if (isset($this->options_cache[$option_name])) {
                return $this->options_cache[$option_name];
            }
            
            // Check if option is defined
            if (!isset($this->option_definitions[$option_name])) {
                Logger::warning("Undefined option requested: {$option_name}");
                return $default;
            }
            
            $definition = $this->option_definitions[$option_name];
            $default_value = $default !== null ? $default : $definition['default'];
            
            // Get from cache layer
            $value = Cache::get($option_name, 'options', $default_value);
            
            if ($value === $default_value) {
                // Not in cache, get from database
                $value = get_option($option_name, $default_value);
                
                // Validate and sanitize
                $value = $this->validate_option_value($option_name, $value);
                
                // Cache the value
                Cache::set($option_name, $value, 'options');
            }
            
            // Store in memory cache
            $this->options_cache[$option_name] = $value;
            
            return $value;
            
        } catch (Exception $e) {
            Logger::error("Error getting option {$option_name}: " . $e->getMessage());
            return $default !== null ? $default : ($this->option_definitions[$option_name]['default'] ?? false);
        }
    }
    
    /**
     * Internal set option method
     */
    private function set_option($option_name, $value) {
        try {
            // Check if option is defined
            if (!isset($this->option_definitions[$option_name])) {
                Logger::warning("Attempting to set undefined option: {$option_name}");
                return false;
            }
            
            // Validate and sanitize
            $sanitized_value = $this->validate_option_value($option_name, $value);
            
            // Update database
            $result = update_option($option_name, $sanitized_value);
            
            if ($result) {
                // Update caches
                $this->options_cache[$option_name] = $sanitized_value;
                Cache::set($option_name, $sanitized_value, 'options');
                
                Logger::debug("Option updated: {$option_name}");
                
                // Trigger action for option change
                do_action('ai_seo_ultra_option_updated', $option_name, $sanitized_value);
            }
            
            return $result;
            
        } catch (Exception $e) {
            Logger::error("Error setting option {$option_name}: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Internal delete option method
     */
    private function delete_option($option_name) {
        try {
            $result = delete_option($option_name);
            
            if ($result) {
                // Clear caches
                unset($this->options_cache[$option_name]);
                Cache::delete($option_name, 'options');
                
                Logger::debug("Option deleted: {$option_name}");
            }
            
            return $result;
            
        } catch (Exception $e) {
            Logger::error("Error deleting option {$option_name}: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all plugin options
     */
    private function get_all_options() {
        $options = [];
        
        foreach (array_keys($this->option_definitions) as $option_name) {
            $options[$option_name] = $this->get_option($option_name, null);
        }
        
        return $options;
    }
    
    /**
     * Set default options
     */
    private function set_default_options() {
        try {
            foreach ($this->option_definitions as $option_name => $definition) {
                // Only set if option doesn't exist
                if (get_option($option_name) === false) {
                    $this->set_option($option_name, $definition['default']);
                }
            }
            
            Logger::info("Default options set");
            return true;
            
        } catch (Exception $e) {
            Logger::error("Error setting default options: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Validate and sanitize option value
     */
    private function validate_option_value($option_name, $value) {
        if (!isset($this->option_definitions[$option_name])) {
            return $value;
        }
        
        $definition = $this->option_definitions[$option_name];
        
        // Sanitize first
        $sanitized_value = Security::sanitize_input($value, $definition['sanitize']);
        
        // Then validate
        $validated_value = $this->validate_value($sanitized_value, $definition['validate'], $option_name);
        
        return $validated_value;
    }
    
    /**
     * Validate value based on validation type
     */
    private function validate_value($value, $validation_type, $option_name) {
        switch ($validation_type) {
            case 'api_key':
                if (!empty($value) && !Security::validate_api_key($value)) {
                    Logger::warning("Invalid API key format for option: {$option_name}");
                    return '';
                }
                return $value;
                
            case 'api_provider':
                $valid_providers = ['openai', 'anthropic', 'google', 'openrouter'];
                if (!in_array($value, $valid_providers)) {
                    Logger::warning("Invalid API provider for option: {$option_name}");
                    return 'openai';
                }
                return $value;
                
            case 'url':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_URL)) {
                    Logger::warning("Invalid URL for option: {$option_name}");
                    return '';
                }
                return $value;
                
            case 'model':
                $valid_models = [
                    'gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo',
                    'claude-3-sonnet', 'claude-3-opus',
                    'gemini-pro', 'gemini-pro-vision'
                ];
                if (!in_array($value, $valid_models)) {
                    Logger::warning("Invalid model for option: {$option_name}");
                    return 'gpt-3.5-turbo';
                }
                return $value;
                
            case 'tone':
                $valid_tones = ['professional', 'casual', 'friendly', 'authoritative', 'conversational'];
                if (!in_array($value, $valid_tones)) {
                    Logger::warning("Invalid tone for option: {$option_name}");
                    return 'professional';
                }
                return $value;
                
            case 'elements':
                if (!is_array($value)) {
                    return ['title', 'description', 'h1'];
                }
                $valid_elements = ['title', 'description', 'h1', 'h2', 'meta_keywords'];
                $filtered = array_intersect($value, $valid_elements);
                return !empty($filtered) ? $filtered : ['title', 'description', 'h1'];
                
            case 'positive_int':
                $int_value = intval($value);
                if ($int_value <= 0) {
                    Logger::warning("Invalid positive integer for option: {$option_name}");
                    return 1;
                }
                return $int_value;
                
            case 'bool':
                return (bool) $value;
                
            case 'text':
                if (strlen($value) > 5000) {
                    Logger::warning("Text too long for option: {$option_name}");
                    return substr($value, 0, 5000);
                }
                return $value;
                
            default:
                return $value;
        }
    }
    
    /**
     * Clear options cache
     */
    public static function clear_cache() {
        $instance = self::get_instance();
        $instance->options_cache = [];
        Cache::clear_group('options');
        Logger::debug("Options cache cleared");
    }
    
    /**
     * Get option definition
     */
    public static function get_definition($option_name) {
        $instance = self::get_instance();
        return $instance->option_definitions[$option_name] ?? null;
    }
    
    /**
     * Get all option definitions
     */
    public static function get_all_definitions() {
        $instance = self::get_instance();
        return $instance->option_definitions;
    }
    
    /**
     * Check if option exists
     */
    public static function exists($option_name) {
        $instance = self::get_instance();
        return isset($instance->option_definitions[$option_name]);
    }
    
    /**
     * Bulk update options
     */
    public static function bulk_update($options) {
        $instance = self::get_instance();
        $results = [];
        
        foreach ($options as $option_name => $value) {
            $results[$option_name] = $instance->set_option($option_name, $value);
        }
        
        return $results;
    }
    
    /**
     * Export options
     */
    public static function export() {
        $instance = self::get_instance();
        $options = $instance->get_all_options();
        
        // Remove sensitive data
        unset($options['ai_seo_ultra_api_key']);
        
        return [
            'version' => AI_SEO_ULTRA_VERSION,
            'exported_at' => current_time('mysql'),
            'options' => $options
        ];
    }
    
    /**
     * Import options
     */
    public static function import($data) {
        if (!is_array($data) || !isset($data['options'])) {
            return false;
        }
        
        $instance = self::get_instance();
        $imported = 0;
        
        foreach ($data['options'] as $option_name => $value) {
            // Skip sensitive options
            if ($option_name === 'ai_seo_ultra_api_key') {
                continue;
            }
            
            if ($instance->set_option($option_name, $value)) {
                $imported++;
            }
        }
        
        Logger::info("Imported {$imported} options");
        return $imported;
    }
    
    /**
     * Prevent cloning and unserialization
     */
    private function __clone() {}
    public function __wakeup() {
        throw new Exception('Cannot unserialize singleton');
    }
}
