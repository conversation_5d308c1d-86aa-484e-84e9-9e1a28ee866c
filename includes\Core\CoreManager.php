<?php
/**
 * Core Manager Class
 * 
 * Manages core functionality and coordinates between different components
 * 
 * @package AiSeoOptimizerUltra
 * @since 9.0.0
 */

namespace AiSeoOptimizerUltra\Core;

use Exception;
use RuntimeException;

/**
 * Core Manager Class
 * 
 * Central coordinator for all plugin functionality
 */
final class CoreManager {
    private static $instance = null;
    private $components = [];
    private $hooks_registered = false;
    
    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Private constructor
     */
    private function __construct() {
        $this->init_components();
        $this->register_hooks();
    }
    
    /**
     * Initialize core components
     */
    private function init_components() {
        try {
            // Initialize components in order of dependency
            $this->components['logger'] = Logger::get_instance();
            $this->components['cache'] = Cache::get_instance();
            $this->components['options'] = Options::get_instance();
            $this->components['security'] = Security::get_instance();
            $this->components['api_client'] = ApiClient::get_instance();
            $this->components['seo_processor'] = SeoProcessor::get_instance();
            $this->components['schema_generator'] = SchemaGenerator::get_instance();
            
            Logger::info('Core components initialized successfully');
            
        } catch (Exception $e) {
            Logger::error('Failed to initialize core components: ' . $e->getMessage());
            throw new RuntimeException('Core initialization failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Register WordPress hooks
     */
    private function register_hooks() {
        if ($this->hooks_registered) {
            return;
        }
        
        // Post save hooks for SEO processing
        add_action('save_post', [$this, 'handle_post_save'], 20, 2);
        add_action('wp_after_insert_post', [$this, 'handle_post_insert'], 10, 4);
        
        // Meta box hooks
        add_action('add_meta_boxes', [$this, 'add_meta_boxes']);
        add_action('save_post', [$this, 'save_meta_box_data'], 10, 2);
        
        // AJAX hooks
        add_action('wp_ajax_ai_seo_process_post', [$this, 'ajax_process_post']);
        add_action('wp_ajax_ai_seo_get_scores', [$this, 'ajax_get_scores']);
        add_action('wp_ajax_ai_seo_bulk_process', [$this, 'ajax_bulk_process']);
        
        // Cron hooks
        add_action('ai_seo_ultra_daily_cleanup', [$this, 'daily_cleanup']);
        add_action('ai_seo_ultra_process_queue', [$this, 'process_queue']);
        
        // Frontend hooks
        add_action('wp_head', [$this, 'output_meta_tags'], 1);
        add_action('wp_footer', [$this, 'output_schema_markup'], 99);
        
        // Admin hooks
        if (is_admin()) {
            add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_scripts']);
            add_action('admin_notices', [$this, 'admin_notices']);
        }
        
        $this->hooks_registered = true;
        Logger::debug('WordPress hooks registered');
    }
    
    /**
     * Handle post save
     */
    public function handle_post_save($post_id, $post) {
        try {
            // Skip for autosaves, revisions, and non-public post types
            if (wp_is_post_autosave($post_id) || wp_is_post_revision($post_id)) {
                return;
            }
            
            $allowed_post_types = apply_filters('ai_seo_ultra_allowed_post_types', ['post', 'page']);
            if (!in_array($post->post_type, $allowed_post_types)) {
                return;
            }
            
            // Security check
            if (!Security::verify_post_save_nonce()) {
                return;
            }
            
            // Clear caches for this post
            Cache::clear_post_cache($post_id);
            
            // Schedule background processing if Action Scheduler is available
            if (function_exists('as_schedule_single_action')) {
                as_schedule_single_action(
                    time() + 30, // Process after 30 seconds
                    'ai_seo_ultra_process_post',
                    [$post_id],
                    'ai-seo-ultra-processing'
                );
            } else {
                // Process immediately if no background processing available
                $this->process_post_seo($post_id);
            }
            
        } catch (Exception $e) {
            Logger::error('Error handling post save for post ID ' . $post_id . ': ' . $e->getMessage());
        }
    }
    
    /**
     * Handle post insert
     */
    public function handle_post_insert($post_id, $post, $update, $post_before) {
        if (!$update) {
            // New post - set default SEO data
            $this->set_default_seo_data($post_id);
        }
    }
    
    /**
     * Process post SEO optimization
     */
    public function process_post_seo($post_id) {
        try {
            \AiSeoUltraMonitor::check_memory_limit('seo_processing');
            
            $processor = $this->components['seo_processor'];
            $result = $processor->process_post($post_id);
            
            if (is_wp_error($result)) {
                Logger::error('SEO processing failed for post ' . $post_id . ': ' . $result->get_error_message());
                return false;
            }
            
            Logger::info('SEO processing completed for post ' . $post_id);
            return true;
            
        } catch (Exception $e) {
            Logger::error('Exception during SEO processing for post ' . $post_id . ': ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Add meta boxes
     */
    public function add_meta_boxes() {
        $post_types = apply_filters('ai_seo_ultra_meta_box_post_types', ['post', 'page']);
        
        foreach ($post_types as $post_type) {
            add_meta_box(
                'ai-seo-ultra-meta-box',
                __('AI SEO Optimizer Ultra', 'ai-seo-optimizer-ultra'),
                [$this, 'render_meta_box'],
                $post_type,
                'normal',
                'high'
            );
        }
    }
    
    /**
     * Render meta box
     */
    public function render_meta_box($post) {
        try {
            // Add nonce for security
            wp_nonce_field('ai_seo_ultra_meta_box', 'ai_seo_ultra_meta_box_nonce');
            
            // Get current SEO data
            $seo_data = $this->get_post_seo_data($post->ID);
            
            // Include meta box template
            include AI_SEO_ULTRA_PLUGIN_DIR . 'templates/meta-box.php';
            
        } catch (Exception $e) {
            echo '<div class="notice notice-error"><p>Error loading SEO meta box: ' . esc_html($e->getMessage()) . '</p></div>';
        }
    }
    
    /**
     * Save meta box data
     */
    public function save_meta_box_data($post_id, $post) {
        try {
            // Security checks
            if (!isset($_POST['ai_seo_ultra_meta_box_nonce']) || 
                !wp_verify_nonce($_POST['ai_seo_ultra_meta_box_nonce'], 'ai_seo_ultra_meta_box')) {
                return;
            }
            
            if (!current_user_can('edit_post', $post_id)) {
                return;
            }
            
            // Save SEO data
            $this->save_post_seo_data($post_id, $_POST);
            
        } catch (Exception $e) {
            Logger::error('Error saving meta box data for post ' . $post_id . ': ' . $e->getMessage());
        }
    }
    
    /**
     * AJAX: Process single post
     */
    public function ajax_process_post() {
        try {
            Security::verify_ajax_nonce('ai_seo_ultra_ajax');
            
            $post_id = intval($_POST['post_id'] ?? 0);
            if (!$post_id || !current_user_can('edit_post', $post_id)) {
                wp_send_json_error('Invalid post ID or insufficient permissions');
            }
            
            $result = $this->process_post_seo($post_id);
            
            if ($result) {
                wp_send_json_success([
                    'message' => 'Post processed successfully',
                    'data' => $this->get_post_seo_data($post_id)
                ]);
            } else {
                wp_send_json_error('Processing failed');
            }
            
        } catch (Exception $e) {
            Logger::error('AJAX process post error: ' . $e->getMessage());
            wp_send_json_error('Processing error: ' . $e->getMessage());
        }
    }
    
    /**
     * AJAX: Get SEO scores
     */
    public function ajax_get_scores() {
        try {
            Security::verify_ajax_nonce('ai_seo_ultra_ajax');
            
            $post_ids = array_map('intval', $_POST['post_ids'] ?? []);
            if (empty($post_ids)) {
                wp_send_json_error('No post IDs provided');
            }
            
            $scores = [];
            foreach ($post_ids as $post_id) {
                if (current_user_can('edit_post', $post_id)) {
                    $scores[$post_id] = $this->get_post_seo_scores($post_id);
                }
            }
            
            wp_send_json_success($scores);
            
        } catch (Exception $e) {
            Logger::error('AJAX get scores error: ' . $e->getMessage());
            wp_send_json_error('Error retrieving scores: ' . $e->getMessage());
        }
    }
    
    /**
     * Daily cleanup task
     */
    public function daily_cleanup() {
        try {
            Logger::info('Starting daily cleanup');
            
            // Clear expired caches
            Cache::cleanup_expired();
            
            // Clean up old log entries
            Logger::cleanup_old_logs();
            
            // Optimize database tables
            Database::optimize_tables();
            
            Logger::info('Daily cleanup completed');
            
        } catch (Exception $e) {
            Logger::error('Daily cleanup failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Output meta tags in head
     */
    public function output_meta_tags() {
        if (!is_singular()) {
            return;
        }
        
        try {
            $post_id = get_the_ID();
            $meta_tags = $this->generate_meta_tags($post_id);
            
            foreach ($meta_tags as $tag) {
                echo $tag . "\n";
            }
            
        } catch (Exception $e) {
            Logger::error('Error outputting meta tags: ' . $e->getMessage());
        }
    }
    
    /**
     * Output schema markup in footer
     */
    public function output_schema_markup() {
        if (!is_singular()) {
            return;
        }
        
        try {
            $post_id = get_the_ID();
            $schema = $this->components['schema_generator']->generate_schema($post_id);
            
            if (!empty($schema)) {
                echo '<script type="application/ld+json">' . wp_json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . '</script>' . "\n";
            }
            
        } catch (Exception $e) {
            Logger::error('Error outputting schema markup: ' . $e->getMessage());
        }
    }
    
    /**
     * Get component by name
     */
    public function get_component($name) {
        return $this->components[$name] ?? null;
    }
    
    /**
     * Helper methods (to be implemented)
     */
    private function set_default_seo_data($post_id) {
        // Implementation will be added
    }
    
    private function get_post_seo_data($post_id) {
        // Implementation will be added
        return [];
    }
    
    private function save_post_seo_data($post_id, $data) {
        // Implementation will be added
    }
    
    private function get_post_seo_scores($post_id) {
        // Implementation will be added
        return [];
    }
    
    private function generate_meta_tags($post_id) {
        // Implementation will be added
        return [];
    }
    
    /**
     * Prevent cloning and unserialization
     */
    private function __clone() {}
    public function __wakeup() {
        throw new Exception('Cannot unserialize singleton');
    }
}
