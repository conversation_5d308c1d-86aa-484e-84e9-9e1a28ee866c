<?php
/**
 * Logger Class
 * 
 * High-performance logging system with multiple levels and efficient storage
 * 
 * @package AiSeoOptimizerUltra
 * @since 9.0.0
 */

namespace AiSeoOptimizerUltra\Core;

use Exception;
use DateTime;
use DateTimeZone;

/**
 * Logger Class
 * 
 * Provides comprehensive logging with performance optimization
 */
final class Logger {
    private static $instance = null;
    private $log_levels = [
        'DEBUG' => 0,
        'INFO' => 1,
        'WARNING' => 2,
        'ERROR' => 3,
        'CRITICAL' => 4
    ];
    
    private $current_log_level = 1; // INFO by default
    private $log_buffer = [];
    private $buffer_size = 100;
    private $log_file = null;
    
    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Private constructor
     */
    private function __construct() {
        $this->init_logger();
        
        // Register shutdown function to flush buffer
        register_shutdown_function([$this, 'flush_buffer']);
    }
    
    /**
     * Initialize logger
     */
    private function init_logger() {
        // Set log level from options
        $debug_mode = get_option('ai_seo_ultra_debug_mode', false);
        $this->current_log_level = $debug_mode ? 0 : 1;
        
        // Set log file path
        $upload_dir = wp_upload_dir();
        $log_dir = $upload_dir['basedir'] . '/ai-seo-ultra-logs';
        
        if (!file_exists($log_dir)) {
            wp_mkdir_p($log_dir);
            
            // Add .htaccess to protect log files
            $htaccess_content = "Order deny,allow\nDeny from all\n";
            file_put_contents($log_dir . '/.htaccess', $htaccess_content);
        }
        
        $this->log_file = $log_dir . '/ai-seo-ultra-' . date('Y-m-d') . '.log';
    }
    
    /**
     * Log a message
     */
    public static function log($message, $level = 'INFO', $context = []) {
        $instance = self::get_instance();
        $instance->write_log($message, $level, $context);
    }
    
    /**
     * Debug level logging
     */
    public static function debug($message, $context = []) {
        self::log($message, 'DEBUG', $context);
    }
    
    /**
     * Info level logging
     */
    public static function info($message, $context = []) {
        self::log($message, 'INFO', $context);
    }
    
    /**
     * Warning level logging
     */
    public static function warning($message, $context = []) {
        self::log($message, 'WARNING', $context);
    }
    
    /**
     * Error level logging
     */
    public static function error($message, $context = []) {
        self::log($message, 'ERROR', $context);
    }
    
    /**
     * Critical level logging
     */
    public static function critical($message, $context = []) {
        self::log($message, 'CRITICAL', $context);
    }
    
    /**
     * Write log entry
     */
    private function write_log($message, $level, $context) {
        try {
            // Check if we should log this level
            if (!isset($this->log_levels[$level]) || 
                $this->log_levels[$level] < $this->current_log_level) {
                return;
            }
            
            // Prepare log entry
            $log_entry = $this->format_log_entry($message, $level, $context);
            
            // Add to buffer
            $this->log_buffer[] = $log_entry;
            
            // Flush buffer if it's full or if it's a critical error
            if (count($this->log_buffer) >= $this->buffer_size || $level === 'CRITICAL') {
                $this->flush_buffer();
            }
            
            // Also log to WordPress debug.log for critical errors
            if ($level === 'CRITICAL' || $level === 'ERROR') {
                if (defined('WP_DEBUG_LOG') && WP_DEBUG_LOG) {
                    error_log("AI SEO Ultra [{$level}]: {$message}");
                }
            }
            
        } catch (Exception $e) {
            // Fallback to error_log if our logging fails
            error_log("AI SEO Ultra Logger Error: " . $e->getMessage());
        }
    }
    
    /**
     * Format log entry
     */
    private function format_log_entry($message, $level, $context) {
        $timestamp = (new DateTime('now', new DateTimeZone('UTC')))->format('Y-m-d H:i:s T');
        
        // Sanitize and format context
        $context_string = '';
        if (!empty($context)) {
            $safe_context = $this->sanitize_context($context);
            $context_string = ' | Context: ' . wp_json_encode($safe_context, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
        }
        
        // Add memory usage for debug level
        $memory_info = '';
        if ($level === 'DEBUG') {
            $memory_usage = size_format(memory_get_usage(true));
            $memory_info = " | Memory: {$memory_usage}";
        }
        
        return "[{$timestamp}] [{$level}] {$message}{$context_string}{$memory_info}";
    }
    
    /**
     * Sanitize context data for safe logging
     */
    private function sanitize_context($context) {
        $safe_context = [];
        
        foreach ($context as $key => $value) {
            try {
                if (is_wp_error($value)) {
                    $safe_context[$key] = [
                        'type' => 'WP_Error',
                        'code' => $value->get_error_code(),
                        'message' => $value->get_error_message(),
                        'data' => $value->get_error_data()
                    ];
                } elseif (is_object($value)) {
                    if (method_exists($value, '__toString')) {
                        $safe_context[$key] = (string) $value;
                    } else {
                        $safe_context[$key] = [
                            'type' => 'object',
                            'class' => get_class($value)
                        ];
                    }
                } elseif (is_array($value)) {
                    // Recursively sanitize arrays (with depth limit)
                    $safe_context[$key] = $this->sanitize_array($value, 3);
                } elseif (is_resource($value)) {
                    $safe_context[$key] = [
                        'type' => 'resource',
                        'resource_type' => get_resource_type($value)
                    ];
                } elseif (is_string($value) && strlen($value) > 1000) {
                    // Truncate very long strings
                    $safe_context[$key] = substr($value, 0, 1000) . '... (truncated)';
                } else {
                    $safe_context[$key] = $value;
                }
            } catch (Exception $e) {
                $safe_context[$key] = '(Error serializing: ' . $e->getMessage() . ')';
            }
        }
        
        return $safe_context;
    }
    
    /**
     * Sanitize array with depth limit
     */
    private function sanitize_array($array, $max_depth) {
        if ($max_depth <= 0) {
            return '(Max depth reached)';
        }
        
        $safe_array = [];
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $safe_array[$key] = $this->sanitize_array($value, $max_depth - 1);
            } elseif (is_object($value)) {
                $safe_array[$key] = '(Object: ' . get_class($value) . ')';
            } else {
                $safe_array[$key] = $value;
            }
        }
        
        return $safe_array;
    }
    
    /**
     * Flush log buffer to file
     */
    public function flush_buffer() {
        if (empty($this->log_buffer) || !$this->log_file) {
            return;
        }
        
        try {
            $log_content = implode("\n", $this->log_buffer) . "\n";
            
            // Use file locking to prevent corruption
            $result = file_put_contents($this->log_file, $log_content, FILE_APPEND | LOCK_EX);
            
            if ($result === false) {
                // Fallback to error_log
                error_log("AI SEO Ultra: Failed to write to log file");
            }
            
            // Clear buffer
            $this->log_buffer = [];
            
        } catch (Exception $e) {
            error_log("AI SEO Ultra: Log flush error - " . $e->getMessage());
        }
    }
    
    /**
     * Get recent log entries
     */
    public static function get_recent_logs($lines = 100, $level_filter = null) {
        $instance = self::get_instance();
        return $instance->read_recent_logs($lines, $level_filter);
    }
    
    /**
     * Read recent log entries from file
     */
    private function read_recent_logs($lines, $level_filter) {
        if (!file_exists($this->log_file)) {
            return [];
        }
        
        try {
            // Read file in reverse to get recent entries efficiently
            $file_lines = file($this->log_file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            if (!$file_lines) {
                return [];
            }
            
            $recent_lines = array_slice(array_reverse($file_lines), 0, $lines);
            $recent_lines = array_reverse($recent_lines); // Restore chronological order
            
            // Filter by level if specified
            if ($level_filter) {
                $recent_lines = array_filter($recent_lines, function($line) use ($level_filter) {
                    return strpos($line, "[{$level_filter}]") !== false;
                });
            }
            
            return $recent_lines;
            
        } catch (Exception $e) {
            error_log("AI SEO Ultra: Error reading logs - " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Clean up old log files
     */
    public static function cleanup_old_logs($days_to_keep = 30) {
        try {
            $upload_dir = wp_upload_dir();
            $log_dir = $upload_dir['basedir'] . '/ai-seo-ultra-logs';
            
            if (!is_dir($log_dir)) {
                return;
            }
            
            $cutoff_time = time() - ($days_to_keep * DAY_IN_SECONDS);
            $files = glob($log_dir . '/ai-seo-ultra-*.log');
            
            foreach ($files as $file) {
                if (filemtime($file) < $cutoff_time) {
                    unlink($file);
                }
            }
            
            self::info("Cleaned up old log files older than {$days_to_keep} days");
            
        } catch (Exception $e) {
            error_log("AI SEO Ultra: Log cleanup error - " . $e->getMessage());
        }
    }
    
    /**
     * Get log file size
     */
    public static function get_log_file_size() {
        $instance = self::get_instance();
        if (file_exists($instance->log_file)) {
            return filesize($instance->log_file);
        }
        return 0;
    }
    
    /**
     * Clear current log file
     */
    public static function clear_current_log() {
        $instance = self::get_instance();
        if (file_exists($instance->log_file)) {
            file_put_contents($instance->log_file, '');
            self::info('Log file cleared');
        }
    }
    
    /**
     * Prevent cloning and unserialization
     */
    private function __clone() {}
    public function __wakeup() {
        throw new Exception('Cannot unserialize singleton');
    }
}
