<?php
/**
 * Meta box template for post editing
 * 
 * @package UltraFeaturedImageOptimizer
 */

if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="ufio-meta-box">
    <div class="ufio-meta-section">
        <h4><?php _e('Processing Status', 'ultra-featured-image-optimizer'); ?></h4>
        
        <?php if ($processing_status): ?>
            <p class="ufio-status <?php echo esc_attr($processing_status); ?>">
                <span class="status-indicator"></span>
                <?php echo esc_html(ucfirst(str_replace('_', ' ', $processing_status))); ?>
            </p>
        <?php else: ?>
            <p class="ufio-status not-processed">
                <span class="status-indicator"></span>
                <?php _e('Not processed', 'ultra-featured-image-optimizer'); ?>
            </p>
        <?php endif; ?>
        
        <?php if ($last_processed): ?>
            <p class="description">
                <?php printf(__('Last processed: %s', 'ultra-featured-image-optimizer'), human_time_diff(strtotime($last_processed), current_time('timestamp')) . ' ago'); ?>
            </p>
        <?php endif; ?>
    </div>

    <div class="ufio-meta-section">
        <h4><?php _e('Quick Actions', 'ultra-featured-image-optimizer'); ?></h4>
        
        <div class="ufio-button-group">
            <button type="button" class="ufio-button primary ufio-process-single" data-post-id="<?php echo esc_attr($post->ID); ?>" data-action-type="process_featured_image">
                <?php _e('Process Featured Image', 'ultra-featured-image-optimizer'); ?>
            </button>
            
            <button type="button" class="ufio-button secondary ufio-process-single" data-post-id="<?php echo esc_attr($post->ID); ?>" data-action-type="process_content_images">
                <?php _e('Process Content Images', 'ultra-featured-image-optimizer'); ?>
            </button>
        </div>
        
        <label>
            <input type="checkbox" name="ufio_process_images" value="1" />
            <?php _e('Process images when saving this post', 'ultra-featured-image-optimizer'); ?>
        </label>
    </div>

    <?php
    // Show current featured image info if available
    $featured_image_id = get_post_thumbnail_id($post->ID);
    if ($featured_image_id):
        $image_data = wp_get_attachment_metadata($featured_image_id);
        $seo_optimizer = new \UltraFeaturedImageOptimizer\SEOOptimizer();
        $seo_score = $seo_optimizer->calculate_seo_score($featured_image_id);
    ?>
    <div class="ufio-meta-section">
        <h4><?php _e('Featured Image Info', 'ultra-featured-image-optimizer'); ?></h4>
        
        <div class="ufio-image-info">
            <div class="ufio-image-thumbnail">
                <?php echo get_the_post_thumbnail($post->ID, 'thumbnail'); ?>
            </div>
            
            <div class="ufio-image-details">
                <p><strong><?php _e('Dimensions:', 'ultra-featured-image-optimizer'); ?></strong> 
                   <?php echo esc_html($image_data['width'] ?? 'Unknown'); ?> × <?php echo esc_html($image_data['height'] ?? 'Unknown'); ?>px</p>
                
                <p><strong><?php _e('File Size:', 'ultra-featured-image-optimizer'); ?></strong> 
                   <?php echo esc_html(size_format($image_data['filesize'] ?? 0)); ?></p>
                
                <p><strong><?php _e('SEO Score:', 'ultra-featured-image-optimizer'); ?></strong> 
                   <span class="ufio-seo-score-badge <?php echo $seo_score >= 0.8 ? 'good' : ($seo_score >= 0.5 ? 'fair' : 'poor'); ?>">
                       <?php echo esc_html(number_format($seo_score, 2)); ?>/1.0
                   </span>
                </p>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<style>
.ufio-meta-box {
    padding: 0;
}

.ufio-meta-section {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ddd;
}

.ufio-meta-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.ufio-meta-section h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    font-weight: 600;
}

.ufio-status {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 0 5px 0;
    font-weight: 500;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

.ufio-status.completed .status-indicator {
    background: #00a32a;
}

.ufio-status.processing .status-indicator {
    background: #dba617;
}

.ufio-status.queued .status-indicator {
    background: #72aee6;
}

.ufio-status.failed .status-indicator {
    background: #d63638;
}

.ufio-status.not-processed .status-indicator {
    background: #c3c4c7;
}

.ufio-button-group {
    display: flex;
    gap: 8px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.ufio-button {
    padding: 6px 12px;
    font-size: 12px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.ufio-button.primary {
    background: #2271b1;
    color: #fff;
}

.ufio-button.secondary {
    background: #f6f7f7;
    color: #50575e;
    border: 1px solid #c3c4c7;
}

.ufio-button:hover {
    opacity: 0.9;
}

.ufio-image-info {
    display: flex;
    gap: 15px;
    align-items: flex-start;
}

.ufio-image-thumbnail {
    flex-shrink: 0;
}

.ufio-image-thumbnail img {
    max-width: 80px;
    height: auto;
    border-radius: 3px;
}

.ufio-image-details p {
    margin: 0 0 8px 0;
    font-size: 12px;
}

.ufio-seo-score-badge {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
}

.ufio-seo-score-badge.good {
    background: #d1e7dd;
    color: #0f5132;
}

.ufio-seo-score-badge.fair {
    background: #fff3cd;
    color: #664d03;
}

.ufio-seo-score-badge.poor {
    background: #f8d7da;
    color: #721c24;
}
</style>
