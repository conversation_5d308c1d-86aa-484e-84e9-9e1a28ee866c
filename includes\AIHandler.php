<?php
/**
 * <PERSON> Handler for Gemini integration
 * 
 * @package UltraFeaturedImageOptimizer
 * @subpackage AIHandler
 */

namespace UltraFeaturedImageOptimizer;

if (!defined('ABSPATH')) {
    exit;
}

/**
 * AI-powered image analysis and optimization using Google Gemini
 */
class AIHandler {
    
    /**
     * Gemini API endpoints
     */
    const API_BASE_URL = 'https://generativelanguage.googleapis.com/v1beta/models/';
    const GENERATION_MODEL = 'gemini-2.0-flash-exp';
    const VISION_MODEL = 'gemini-2.0-flash-exp';
    const EMBEDDING_MODEL = 'text-embedding-004';
    
    /**
     * Rate limiting
     */
    const REQUESTS_PER_MINUTE = 60;
    const REQUESTS_PER_DAY = 1500;
    
    /**
     * Dependencies
     */
    private $cache;
    private $logger;
    
    /**
     * API configuration
     * @var array
     */
    private $config;
    
    /**
     * Rate limiting data
     * @var array
     */
    private $rate_limits = [];
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->cache = new Cache();
        $this->logger = new Logger();
        
        $this->config = [
            'api_key' => ufio()->get_option('gemini_api_key', ''),
            'timeout' => 30,
            'max_retries' => 3,
            'retry_delay' => 1,
        ];
    }
    
    /**
     * Score image relevance for post
     * 
     * @param array $candidates Image candidates
     * @param \WP_Post $post Post object
     * @return array Scored candidates
     */
    public function score_image_relevance($candidates, $post) {
        if (empty($this->config['api_key'])) {
            $this->logger->warning('Gemini API key not configured');
            return $candidates;
        }
        
        $tracking_id = $this->logger->start_performance_tracking('ai_score_relevance');
        
        try {
            $post_context = $this->prepare_post_context($post);
            $scored_candidates = [];
            
            foreach ($candidates as $candidate) {
                $cache_key = "relevance_score_{$candidate['attachment_id']}_{$post->ID}";
                $cached_score = $this->cache->get($cache_key, Cache::GROUP_AI);
                
                if ($cached_score !== false) {
                    $candidate['ai_confidence'] = $cached_score;
                    $scored_candidates[] = $candidate;
                    continue;
                }
                
                // Check rate limits
                if (!$this->check_rate_limits()) {
                    $this->logger->warning('Rate limit exceeded, skipping AI scoring');
                    $candidate['ai_confidence'] = 0.5; // Default score
                    $scored_candidates[] = $candidate;
                    continue;
                }
                
                $score = $this->analyze_image_relevance($candidate, $post_context);
                $candidate['ai_confidence'] = $score;
                
                // Cache the score
                $this->cache->set($cache_key, $score, Cache::GROUP_AI, DAY_IN_SECONDS);
                
                $scored_candidates[] = $candidate;
                
                // Small delay to respect rate limits
                usleep(100000); // 0.1 second
            }
            
            return $scored_candidates;
            
        } catch (\Exception $e) {
            $this->logger->error('Failed to score image relevance', [
                'error' => $e->getMessage(),
                'post_id' => $post->ID,
                'candidates_count' => count($candidates),
            ]);
            
            return $candidates;
            
        } finally {
            $this->logger->end_performance_tracking($tracking_id, [
                'candidates_processed' => count($candidates),
                'api_calls' => count($candidates),
            ]);
        }
    }
    
    /**
     * Generate alt text for image using AI
     * 
     * @param int $attachment_id Attachment ID
     * @param string $context Additional context
     * @return string|null Generated alt text
     */
    public function generate_alt_text($attachment_id, $context = '') {
        if (empty($this->config['api_key'])) {
            return null;
        }
        
        $cache_key = "alt_text_{$attachment_id}_" . md5($context);
        $cached_alt = $this->cache->get($cache_key, Cache::GROUP_AI);
        
        if ($cached_alt !== false) {
            return $cached_alt;
        }
        
        if (!$this->check_rate_limits()) {
            $this->logger->warning('Rate limit exceeded, skipping alt text generation');
            return null;
        }
        
        try {
            $image_url = wp_get_attachment_url($attachment_id);
            if (!$image_url) {
                return null;
            }
            
            $prompt = $this->build_alt_text_prompt($context);
            $alt_text = $this->analyze_image_with_vision($image_url, $prompt);
            
            if ($alt_text) {
                // Cache the result
                $this->cache->set($cache_key, $alt_text, Cache::GROUP_AI, WEEK_IN_SECONDS);
                
                $this->logger->info('Generated alt text', [
                    'attachment_id' => $attachment_id,
                    'alt_text' => $alt_text,
                ]);
            }
            
            return $alt_text;
            
        } catch (\Exception $e) {
            $this->logger->error('Failed to generate alt text', [
                'attachment_id' => $attachment_id,
                'error' => $e->getMessage(),
            ]);
            
            return null;
        }
    }
    
    /**
     * Generate image tags and description
     * 
     * @param int $attachment_id Attachment ID
     * @return array|null Generated data
     */
    public function generate_image_metadata($attachment_id) {
        if (empty($this->config['api_key'])) {
            return null;
        }
        
        $cache_key = "image_metadata_{$attachment_id}";
        $cached_metadata = $this->cache->get($cache_key, Cache::GROUP_AI);
        
        if ($cached_metadata !== false) {
            return $cached_metadata;
        }
        
        if (!$this->check_rate_limits()) {
            $this->logger->warning('Rate limit exceeded, skipping metadata generation');
            return null;
        }
        
        try {
            $image_url = wp_get_attachment_url($attachment_id);
            if (!$image_url) {
                return null;
            }
            
            $prompt = $this->build_metadata_prompt();
            $response = $this->analyze_image_with_vision($image_url, $prompt);
            
            if ($response) {
                $metadata = $this->parse_metadata_response($response);
                
                // Cache the result
                $this->cache->set($cache_key, $metadata, Cache::GROUP_AI, WEEK_IN_SECONDS);
                
                $this->logger->info('Generated image metadata', [
                    'attachment_id' => $attachment_id,
                    'metadata' => $metadata,
                ]);
                
                return $metadata;
            }
            
            return null;
            
        } catch (\Exception $e) {
            $this->logger->error('Failed to generate image metadata', [
                'attachment_id' => $attachment_id,
                'error' => $e->getMessage(),
            ]);
            
            return null;
        }
    }
    
    /**
     * Analyze image relevance to post
     * 
     * @param array $candidate Image candidate
     * @param array $post_context Post context
     * @return float Relevance score (0-1)
     */
    private function analyze_image_relevance($candidate, $post_context) {
        try {
            $prompt = $this->build_relevance_prompt($post_context);
            $response = $this->analyze_image_with_vision($candidate['url'], $prompt);
            
            if ($response) {
                return $this->parse_relevance_score($response);
            }
            
            return 0.5; // Default score
            
        } catch (\Exception $e) {
            $this->logger->error('Failed to analyze image relevance', [
                'candidate' => $candidate['attachment_id'],
                'error' => $e->getMessage(),
            ]);
            
            return 0.5;
        }
    }
    
    /**
     * Analyze image with Gemini Vision
     * 
     * @param string $image_url Image URL
     * @param string $prompt Analysis prompt
     * @return string|null Analysis result
     */
    private function analyze_image_with_vision($image_url, $prompt) {
        $image_data = $this->fetch_image_data($image_url);
        if (!$image_data) {
            throw new \Exception('Failed to fetch image data');
        }
        
        $request_data = [
            'contents' => [
                [
                    'parts' => [
                        [
                            'text' => $prompt
                        ],
                        [
                            'inline_data' => [
                                'mime_type' => $image_data['mime_type'],
                                'data' => $image_data['base64']
                            ]
                        ]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => 0.1,
                'topK' => 32,
                'topP' => 1,
                'maxOutputTokens' => 1024,
            ],
            'safetySettings' => [
                [
                    'category' => 'HARM_CATEGORY_HARASSMENT',
                    'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                ],
                [
                    'category' => 'HARM_CATEGORY_HATE_SPEECH',
                    'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                ],
                [
                    'category' => 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
                    'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                ],
                [
                    'category' => 'HARM_CATEGORY_DANGEROUS_CONTENT',
                    'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                ]
            ]
        ];
        
        $response = $this->make_api_request(
            self::VISION_MODEL . ':generateContent',
            $request_data
        );
        
        if ($response && isset($response['candidates'][0]['content']['parts'][0]['text'])) {
            return trim($response['candidates'][0]['content']['parts'][0]['text']);
        }
        
        return null;
    }
    
    /**
     * Make API request to Gemini
     * 
     * @param string $endpoint API endpoint
     * @param array $data Request data
     * @return array|null Response data
     */
    private function make_api_request($endpoint, $data) {
        $url = self::API_BASE_URL . $endpoint . '?key=' . $this->config['api_key'];
        
        $args = [
            'method' => 'POST',
            'headers' => [
                'Content-Type' => 'application/json',
            ],
            'body' => json_encode($data),
            'timeout' => $this->config['timeout'],
        ];
        
        $retries = 0;
        
        while ($retries < $this->config['max_retries']) {
            $response = wp_remote_request($url, $args);
            
            if (is_wp_error($response)) {
                $this->logger->warning('API request failed', [
                    'error' => $response->get_error_message(),
                    'retry' => $retries + 1,
                ]);
                
                $retries++;
                if ($retries < $this->config['max_retries']) {
                    sleep($this->config['retry_delay'] * $retries);
                }
                continue;
            }
            
            $status_code = wp_remote_retrieve_response_code($response);
            $body = wp_remote_retrieve_body($response);
            
            if ($status_code === 200) {
                $decoded = json_decode($body, true);
                if ($decoded) {
                    $this->update_rate_limits();
                    return $decoded;
                }
            }
            
            // Handle rate limiting
            if ($status_code === 429) {
                $this->logger->warning('Rate limit hit', ['status_code' => $status_code]);
                $this->handle_rate_limit_response($response);
                return null;
            }
            
            // Handle other errors
            if ($status_code >= 400) {
                $this->logger->error('API request error', [
                    'status_code' => $status_code,
                    'response' => $body,
                ]);
                
                $retries++;
                if ($retries < $this->config['max_retries']) {
                    sleep($this->config['retry_delay'] * $retries);
                }
                continue;
            }
            
            break;
        }
        
        return null;
    }
    
    /**
     * Fetch and encode image data
     * 
     * @param string $image_url Image URL
     * @return array|null Image data
     */
    private function fetch_image_data($image_url) {
        $response = wp_remote_get($image_url, [
            'timeout' => 30,
        ]);
        
        if (is_wp_error($response)) {
            return null;
        }
        
        $image_content = wp_remote_retrieve_body($response);
        $content_type = wp_remote_retrieve_header($response, 'content-type');
        
        if (empty($image_content) || empty($content_type)) {
            return null;
        }
        
        return [
            'base64' => base64_encode($image_content),
            'mime_type' => $content_type,
        ];
    }
    
    /**
     * Prepare post context for AI analysis
     * 
     * @param \WP_Post $post Post object
     * @return array Post context
     */
    private function prepare_post_context($post) {
        $categories = get_the_category($post->ID);
        $tags = get_the_tags($post->ID);
        
        return [
            'title' => $post->post_title,
            'content' => wp_strip_all_tags($post->post_content),
            'excerpt' => $post->post_excerpt,
            'categories' => $categories ? wp_list_pluck($categories, 'name') : [],
            'tags' => $tags ? wp_list_pluck($tags, 'name') : [],
            'post_type' => $post->post_type,
        ];
    }
    
    /**
     * Build relevance analysis prompt
     * 
     * @param array $post_context Post context
     * @return string Prompt
     */
    private function build_relevance_prompt($post_context) {
        $prompt = "Analyze this image and determine how relevant it is to the following content:\n\n";
        $prompt .= "Title: " . $post_context['title'] . "\n";
        
        if (!empty($post_context['categories'])) {
            $prompt .= "Categories: " . implode(', ', $post_context['categories']) . "\n";
        }
        
        if (!empty($post_context['tags'])) {
            $prompt .= "Tags: " . implode(', ', $post_context['tags']) . "\n";
        }
        
        $prompt .= "Content excerpt: " . substr($post_context['content'], 0, 500) . "...\n\n";
        $prompt .= "Rate the relevance of this image to the content on a scale of 0.0 to 1.0, where:\n";
        $prompt .= "- 0.0 = Completely irrelevant\n";
        $prompt .= "- 0.5 = Somewhat relevant\n";
        $prompt .= "- 1.0 = Highly relevant and perfect match\n\n";
        $prompt .= "Respond with only the numerical score (e.g., 0.8).";
        
        return $prompt;
    }
    
    /**
     * Build alt text generation prompt
     * 
     * @param string $context Additional context
     * @return string Prompt
     */
    private function build_alt_text_prompt($context = '') {
        $prompt = "Generate a concise, descriptive alt text for this image that:\n";
        $prompt .= "- Describes the main subject and important visual elements\n";
        $prompt .= "- Is 125 characters or less\n";
        $prompt .= "- Is accessible and helpful for screen readers\n";
        $prompt .= "- Avoids phrases like 'image of' or 'picture of'\n";
        
        if ($context) {
            $prompt .= "- Considers this context: " . $context . "\n";
        }
        
        $prompt .= "\nRespond with only the alt text, no additional formatting or explanation.";
        
        return $prompt;
    }
    
    /**
     * Build metadata generation prompt
     * 
     * @return string Prompt
     */
    private function build_metadata_prompt() {
        $prompt = "Analyze this image and provide the following information in JSON format:\n";
        $prompt .= "{\n";
        $prompt .= '  "description": "Detailed description of the image",\n';
        $prompt .= '  "tags": ["tag1", "tag2", "tag3"],\n';
        $prompt .= '  "keywords": ["keyword1", "keyword2", "keyword3"],\n';
        $prompt .= '  "subject": "Main subject of the image",\n';
        $prompt .= '  "style": "Photography style or type",\n';
        $prompt .= '  "colors": ["dominant", "color", "palette"]\n';
        $prompt .= "}\n\n";
        $prompt .= "Provide accurate, relevant metadata that would be useful for SEO and accessibility.";
        
        return $prompt;
    }
    
    /**
     * Parse relevance score from response
     * 
     * @param string $response AI response
     * @return float Relevance score
     */
    private function parse_relevance_score($response) {
        // Extract numerical score from response
        if (preg_match('/(\d+\.?\d*)/', $response, $matches)) {
            $score = (float) $matches[1];
            return max(0.0, min(1.0, $score)); // Clamp between 0 and 1
        }
        
        return 0.5; // Default score
    }
    
    /**
     * Parse metadata response
     * 
     * @param string $response AI response
     * @return array|null Parsed metadata
     */
    private function parse_metadata_response($response) {
        // Try to extract JSON from response
        if (preg_match('/\{.*\}/s', $response, $matches)) {
            $json = json_decode($matches[0], true);
            if ($json) {
                return $json;
            }
        }
        
        return null;
    }
    
    /**
     * Check rate limits
     * 
     * @return bool Whether request is allowed
     */
    private function check_rate_limits() {
        $now = time();
        $minute_key = floor($now / 60);
        $day_key = floor($now / 86400);
        
        // Initialize counters if not set
        if (!isset($this->rate_limits[$minute_key])) {
            $this->rate_limits[$minute_key] = 0;
        }
        if (!isset($this->rate_limits[$day_key . '_day'])) {
            $this->rate_limits[$day_key . '_day'] = 0;
        }
        
        // Check limits
        if ($this->rate_limits[$minute_key] >= self::REQUESTS_PER_MINUTE) {
            return false;
        }
        if ($this->rate_limits[$day_key . '_day'] >= self::REQUESTS_PER_DAY) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Update rate limit counters
     */
    private function update_rate_limits() {
        $now = time();
        $minute_key = floor($now / 60);
        $day_key = floor($now / 86400);
        
        $this->rate_limits[$minute_key] = ($this->rate_limits[$minute_key] ?? 0) + 1;
        $this->rate_limits[$day_key . '_day'] = ($this->rate_limits[$day_key . '_day'] ?? 0) + 1;
        
        // Clean old entries
        foreach ($this->rate_limits as $key => $count) {
            if (strpos($key, '_day') !== false) {
                $key_day = (int) str_replace('_day', '', $key);
                if ($key_day < $day_key - 1) {
                    unset($this->rate_limits[$key]);
                }
            } else {
                if ($key < $minute_key - 5) { // Keep last 5 minutes
                    unset($this->rate_limits[$key]);
                }
            }
        }
    }
    
    /**
     * Handle rate limit response
     * 
     * @param array $response HTTP response
     */
    private function handle_rate_limit_response($response) {
        $retry_after = wp_remote_retrieve_header($response, 'retry-after');
        if ($retry_after) {
            $this->logger->info('Rate limited, retry after: ' . $retry_after . ' seconds');
        }
    }
    
    /**
     * Test API connection
     * 
     * @return array Test results
     */
    public function test_api_connection() {
        if (empty($this->config['api_key'])) {
            return [
                'success' => false,
                'message' => 'API key not configured',
            ];
        }
        
        try {
            $response = $this->make_api_request(
                self::GENERATION_MODEL . ':generateContent',
                [
                    'contents' => [
                        [
                            'parts' => [
                                ['text' => 'Hello, respond with "API connection successful"']
                            ]
                        ]
                    ]
                ]
            );
            
            if ($response) {
                return [
                    'success' => true,
                    'message' => 'API connection successful',
                    'model' => self::GENERATION_MODEL,
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to get valid response from API',
                ];
            }
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'API connection failed: ' . $e->getMessage(),
            ];
        }
    }
}
