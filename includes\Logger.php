<?php
/**
 * Logger class for debugging and monitoring
 * 
 * @package UltraFeaturedImageOptimizer
 * @subpackage Logger
 */

namespace UltraFeaturedImageOptimizer;

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Advanced logging system with multiple levels and outputs
 */
class Logger {
    
    /**
     * Log levels
     */
    const LEVEL_DEBUG = 'debug';
    const LEVEL_INFO = 'info';
    const LEVEL_WARNING = 'warning';
    const LEVEL_ERROR = 'error';
    const LEVEL_CRITICAL = 'critical';
    
    /**
     * Log level priorities
     */
    private $level_priorities = [
        self::LEVEL_DEBUG => 1,
        self::LEVEL_INFO => 2,
        self::LEVEL_WARNING => 3,
        self::LEVEL_ERROR => 4,
        self::LEVEL_CRITICAL => 5,
    ];
    
    /**
     * Current log level
     * @var string
     */
    private $log_level;
    
    /**
     * Log file path
     * @var string
     */
    private $log_file;
    
    /**
     * Maximum log file size (10MB)
     * @var int
     */
    private $max_file_size = 10485760;
    
    /**
     * Maximum number of log files to keep
     * @var int
     */
    private $max_files = 5;
    
    /**
     * Database instance
     * @var Database
     */
    private $database;
    
    /**
     * Performance tracking
     * @var array
     */
    private $performance_data = [];
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->log_level = ufio()->get_option('log_level', self::LEVEL_ERROR);
        $this->log_file = UFIO_LOG_FILE;
        $this->database = new Database();
        
        // Ensure log directory exists
        $this->ensure_log_directory();
    }
    
    /**
     * Log debug message
     * 
     * @param string $message Log message
     * @param array $context Additional context
     */
    public function debug($message, $context = []) {
        $this->log(self::LEVEL_DEBUG, $message, $context);
    }
    
    /**
     * Log info message
     * 
     * @param string $message Log message
     * @param array $context Additional context
     */
    public function info($message, $context = []) {
        $this->log(self::LEVEL_INFO, $message, $context);
    }
    
    /**
     * Log warning message
     * 
     * @param string $message Log message
     * @param array $context Additional context
     */
    public function warning($message, $context = []) {
        $this->log(self::LEVEL_WARNING, $message, $context);
    }
    
    /**
     * Log error message
     * 
     * @param string $message Log message
     * @param array $context Additional context
     */
    public function error($message, $context = []) {
        $this->log(self::LEVEL_ERROR, $message, $context);
    }
    
    /**
     * Log critical message
     * 
     * @param string $message Log message
     * @param array $context Additional context
     */
    public function critical($message, $context = []) {
        $this->log(self::LEVEL_CRITICAL, $message, $context);
    }
    
    /**
     * Main logging method
     * 
     * @param string $level Log level
     * @param string $message Log message
     * @param array $context Additional context
     */
    public function log($level, $message, $context = []) {
        // Check if we should log this level
        if (!$this->should_log($level)) {
            return;
        }
        
        // Prepare log entry
        $log_entry = $this->prepare_log_entry($level, $message, $context);
        
        // Write to file
        $this->write_to_file($log_entry);
        
        // Write to WordPress debug log if enabled
        if (WP_DEBUG_LOG) {
            error_log($log_entry['formatted']);
        }
        
        // Send critical errors to admin email
        if ($level === self::LEVEL_CRITICAL) {
            $this->send_critical_alert($message, $context);
        }
        
        // Store performance data if available
        if (isset($context['performance'])) {
            $this->store_performance_data($context['performance']);
        }
    }
    
    /**
     * Start performance tracking
     * 
     * @param string $action Action being tracked
     * @return string Tracking ID
     */
    public function start_performance_tracking($action) {
        $tracking_id = uniqid('perf_', true);
        
        $this->performance_data[$tracking_id] = [
            'action' => $action,
            'start_time' => microtime(true),
            'start_memory' => memory_get_usage(true),
            'start_queries' => get_num_queries(),
        ];
        
        return $tracking_id;
    }
    
    /**
     * End performance tracking
     * 
     * @param string $tracking_id Tracking ID
     * @param array $additional_data Additional performance data
     */
    public function end_performance_tracking($tracking_id, $additional_data = []) {
        if (!isset($this->performance_data[$tracking_id])) {
            return;
        }
        
        $start_data = $this->performance_data[$tracking_id];
        $end_time = microtime(true);
        $end_memory = memory_get_usage(true);
        $end_queries = get_num_queries();
        
        $performance_data = array_merge([
            'action' => $start_data['action'],
            'execution_time' => round($end_time - $start_data['start_time'], 4),
            'memory_usage' => $end_memory - $start_data['start_memory'],
            'query_count' => $end_queries - $start_data['start_queries'],
            'peak_memory' => memory_get_peak_usage(true),
        ], $additional_data);
        
        // Log performance data
        $this->info('Performance tracking completed', [
            'performance' => $performance_data
        ]);
        
        // Clean up
        unset($this->performance_data[$tracking_id]);
    }
    
    /**
     * Get recent log entries
     * 
     * @param int $limit Number of entries to retrieve
     * @param string $level Minimum log level
     * @return array Log entries
     */
    public function get_recent_logs($limit = 100, $level = self::LEVEL_INFO) {
        if (!file_exists($this->log_file)) {
            return [];
        }
        
        $lines = file($this->log_file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        if (!$lines) {
            return [];
        }
        
        $logs = [];
        $min_priority = $this->level_priorities[$level];
        
        // Process lines in reverse order (newest first)
        $lines = array_reverse($lines);
        
        foreach ($lines as $line) {
            if (count($logs) >= $limit) {
                break;
            }
            
            $log_data = $this->parse_log_line($line);
            if ($log_data && $this->level_priorities[$log_data['level']] >= $min_priority) {
                $logs[] = $log_data;
            }
        }
        
        return $logs;
    }
    
    /**
     * Get performance statistics
     * 
     * @param int $days Number of days to analyze
     * @return array Performance statistics
     */
    public function get_performance_stats($days = 7) {
        global $wpdb;
        
        $since = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        $stats = $wpdb->get_results($wpdb->prepare(
            "SELECT 
                action,
                COUNT(*) as count,
                AVG(execution_time) as avg_time,
                MAX(execution_time) as max_time,
                AVG(memory_usage) as avg_memory,
                MAX(memory_usage) as max_memory,
                SUM(query_count) as total_queries,
                SUM(error_count) as total_errors
             FROM {$wpdb->prefix}ufio_performance_logs 
             WHERE created_at >= %s 
             GROUP BY action 
             ORDER BY avg_time DESC",
            $since
        ), ARRAY_A);
        
        return $stats ?: [];
    }
    
    /**
     * Clear old log files
     * 
     * @param int $days Number of days to keep
     */
    public function cleanup_logs($days = 30) {
        $log_dir = dirname($this->log_file);
        $cutoff_time = time() - ($days * DAY_IN_SECONDS);
        
        if (!is_dir($log_dir)) {
            return;
        }
        
        $files = glob($log_dir . '/ufio*.log*');
        foreach ($files as $file) {
            if (filemtime($file) < $cutoff_time) {
                unlink($file);
            }
        }
    }
    
    /**
     * Export logs to CSV
     * 
     * @param int $days Number of days to export
     * @return string|false CSV content or false on failure
     */
    public function export_logs_csv($days = 7) {
        $logs = $this->get_recent_logs(1000, self::LEVEL_DEBUG);
        
        if (empty($logs)) {
            return false;
        }
        
        $csv_content = "Timestamp,Level,Message,Context\n";
        
        foreach ($logs as $log) {
            $csv_content .= sprintf(
                "%s,%s,\"%s\",\"%s\"\n",
                $log['timestamp'],
                $log['level'],
                str_replace('"', '""', $log['message']),
                str_replace('"', '""', json_encode($log['context']))
            );
        }
        
        return $csv_content;
    }
    
    /**
     * Check if we should log this level
     * 
     * @param string $level Log level
     * @return bool
     */
    private function should_log($level) {
        if (!isset($this->level_priorities[$level])) {
            return false;
        }
        
        return $this->level_priorities[$level] >= $this->level_priorities[$this->log_level];
    }
    
    /**
     * Prepare log entry
     * 
     * @param string $level Log level
     * @param string $message Log message
     * @param array $context Additional context
     * @return array Log entry data
     */
    private function prepare_log_entry($level, $message, $context) {
        $timestamp = current_time('Y-m-d H:i:s');
        $memory_usage = memory_get_usage(true);
        $peak_memory = memory_get_peak_usage(true);
        
        // Add system context
        $system_context = [
            'memory_usage' => $memory_usage,
            'peak_memory' => $peak_memory,
            'query_count' => get_num_queries(),
        ];
        
        if (function_exists('wp_get_current_user')) {
            $user = wp_get_current_user();
            if ($user->ID) {
                $system_context['user_id'] = $user->ID;
                $system_context['user_login'] = $user->user_login;
            }
        }
        
        $context = array_merge($context, $system_context);
        
        // Format message
        $formatted_message = sprintf(
            "[%s] [%s] %s %s",
            $timestamp,
            strtoupper($level),
            $message,
            !empty($context) ? json_encode($context) : ''
        );
        
        return [
            'timestamp' => $timestamp,
            'level' => $level,
            'message' => $message,
            'context' => $context,
            'formatted' => $formatted_message,
        ];
    }
    
    /**
     * Write log entry to file
     * 
     * @param array $log_entry Log entry data
     */
    private function write_to_file($log_entry) {
        // Check file size and rotate if necessary
        if (file_exists($this->log_file) && filesize($this->log_file) > $this->max_file_size) {
            $this->rotate_log_file();
        }
        
        // Write to file
        file_put_contents(
            $this->log_file,
            $log_entry['formatted'] . PHP_EOL,
            FILE_APPEND | LOCK_EX
        );
    }
    
    /**
     * Rotate log file
     */
    private function rotate_log_file() {
        $log_dir = dirname($this->log_file);
        $log_name = basename($this->log_file, '.log');
        
        // Move existing rotated files
        for ($i = $this->max_files - 1; $i >= 1; $i--) {
            $old_file = $log_dir . '/' . $log_name . '.' . $i . '.log';
            $new_file = $log_dir . '/' . $log_name . '.' . ($i + 1) . '.log';
            
            if (file_exists($old_file)) {
                if ($i + 1 > $this->max_files) {
                    unlink($old_file);
                } else {
                    rename($old_file, $new_file);
                }
            }
        }
        
        // Move current log file
        if (file_exists($this->log_file)) {
            rename($this->log_file, $log_dir . '/' . $log_name . '.1.log');
        }
    }
    
    /**
     * Parse log line
     * 
     * @param string $line Log line
     * @return array|false Parsed log data or false
     */
    private function parse_log_line($line) {
        $pattern = '/^\[([^\]]+)\] \[([^\]]+)\] (.+)$/';
        
        if (!preg_match($pattern, $line, $matches)) {
            return false;
        }
        
        $timestamp = $matches[1];
        $level = strtolower($matches[2]);
        $content = $matches[3];
        
        // Try to extract context JSON
        $context = [];
        if (preg_match('/^(.+?) (\{.+\})$/', $content, $content_matches)) {
            $message = $content_matches[1];
            $context_json = $content_matches[2];
            $context = json_decode($context_json, true) ?: [];
        } else {
            $message = $content;
        }
        
        return [
            'timestamp' => $timestamp,
            'level' => $level,
            'message' => $message,
            'context' => $context,
        ];
    }
    
    /**
     * Store performance data in database
     * 
     * @param array $performance_data Performance data
     */
    private function store_performance_data($performance_data) {
        global $wpdb;
        
        $wpdb->insert(
            $wpdb->prefix . 'ufio_performance_logs',
            [
                'action' => $performance_data['action'],
                'execution_time' => $performance_data['execution_time'],
                'memory_usage' => $performance_data['memory_usage'],
                'query_count' => $performance_data['query_count'],
                'cache_hits' => $performance_data['cache_hits'] ?? 0,
                'cache_misses' => $performance_data['cache_misses'] ?? 0,
                'error_count' => $performance_data['error_count'] ?? 0,
                'metadata' => json_encode($performance_data),
            ],
            ['%s', '%f', '%d', '%d', '%d', '%d', '%d', '%s']
        );
    }
    
    /**
     * Send critical alert email
     * 
     * @param string $message Error message
     * @param array $context Error context
     */
    private function send_critical_alert($message, $context) {
        $admin_email = get_option('admin_email');
        if (!$admin_email) {
            return;
        }
        
        $subject = sprintf(
            '[%s] Critical Error in Ultra Featured Image Optimizer',
            get_bloginfo('name')
        );
        
        $body = sprintf(
            "A critical error occurred in the Ultra Featured Image Optimizer plugin:\n\n" .
            "Error: %s\n\n" .
            "Context: %s\n\n" .
            "Time: %s\n" .
            "Site: %s\n\n" .
            "Please check the plugin logs for more details.",
            $message,
            json_encode($context, JSON_PRETTY_PRINT),
            current_time('Y-m-d H:i:s'),
            home_url()
        );
        
        wp_mail($admin_email, $subject, $body);
    }
    
    /**
     * Ensure log directory exists
     */
    private function ensure_log_directory() {
        $log_dir = dirname($this->log_file);
        
        if (!file_exists($log_dir)) {
            wp_mkdir_p($log_dir);
            
            // Create index.php for security
            $index_file = $log_dir . '/index.php';
            if (!file_exists($index_file)) {
                file_put_contents($index_file, '<?php // Silence is golden');
            }
            
            // Create .htaccess to prevent direct access
            $htaccess_file = $log_dir . '/.htaccess';
            if (!file_exists($htaccess_file)) {
                file_put_contents($htaccess_file, "deny from all\n");
            }
        }
    }
}
