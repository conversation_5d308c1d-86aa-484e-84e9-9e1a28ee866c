/**
 * AI SEO Optimizer Ultra - Admin Styles (v15.0.0 - HYPERION EDITION)
 *
 * Description: The definitive professional UI/UX experience reimagined for 2025+. Featuring a breathtaking,
 *              ultra-premium design with fluid micro-interactions, sophisticated typography, and a harmonious
 *              yet vibrant color palette. Meticulously crafted for maximum visual appeal, intuitive usability,
 *              perfect accessibility, and flawless harmony with modern design principles.
 *              This Hyperion Edition introduces revolutionary visual aesthetics, enhanced readability,
 *              perfect mobile responsiveness, stunning interactive elements, and optimized performance.
 * Version: 15.0.0
 * Author: AI Assistant (Ultra Premium Design Edition)
 * Original Base: v14.0.0
 */

/* ==========================================================================
   == Stellar Foundation: Variables & Core Styles ==
   ========================================================================== */

@import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Outfit:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@400;500;600&display=swap');

:root {
    /* --- Quantum Color System - Futuristic Tech Palette --- */
    /* Primary Brand Color - Electric Blue with enhanced vibrancy */
    --ai-seo-primary-hue: 210;
    --ai-seo-primary-saturation: 100%;
    --ai-seo-primary-lightness: 60%;
    --ai-seo-primary-color: hsl(var(--ai-seo-primary-hue), var(--ai-seo-primary-saturation), var(--ai-seo-primary-lightness));
    --ai-seo-primary-dark: hsl(var(--ai-seo-primary-hue), var(--ai-seo-primary-saturation), calc(var(--ai-seo-primary-lightness) - 10%));
    --ai-seo-primary-darker: hsl(var(--ai-seo-primary-hue), var(--ai-seo-primary-saturation), calc(var(--ai-seo-primary-lightness) - 20%));
    --ai-seo-primary-light: hsl(var(--ai-seo-primary-hue), 95%, 97%);
    --ai-seo-primary-lighter: hsl(var(--ai-seo-primary-hue), 95%, 94%);
    --ai-seo-primary-border: hsl(var(--ai-seo-primary-hue), 95%, 85%);
    --ai-seo-primary-focus-shadow: hsla(var(--ai-seo-primary-hue), var(--ai-seo-primary-saturation), var(--ai-seo-primary-lightness), 0.45);
    --ai-seo-primary-text: #ffffff;

    /* Secondary Brand Color - Neon Purple with enhanced vibrancy */
    --ai-seo-secondary-hue: 270;
    --ai-seo-secondary-saturation: 100%;
    --ai-seo-secondary-lightness: 65%;
    --ai-seo-secondary-color: hsl(var(--ai-seo-secondary-hue), var(--ai-seo-secondary-saturation), var(--ai-seo-secondary-lightness));
    --ai-seo-secondary-dark: hsl(var(--ai-seo-secondary-hue), var(--ai-seo-secondary-saturation), calc(var(--ai-seo-secondary-lightness) - 10%));
    --ai-seo-secondary-light: hsl(var(--ai-seo-secondary-hue), 95%, 92%);
    --ai-seo-secondary-lighter: hsl(var(--ai-seo-secondary-hue), 95%, 97%);
    --ai-seo-secondary-border: hsl(var(--ai-seo-secondary-hue), 95%, 85%);
    --ai-seo-secondary-focus-shadow: hsla(var(--ai-seo-secondary-hue), var(--ai-seo-secondary-saturation), var(--ai-seo-secondary-lightness), 0.45);

    /* Neutral Colors - Enhanced Grayscale with better contrast */
    --ai-seo-neutral-50: #f9fafb;  /* Lightest background */
    --ai-seo-neutral-100: #f3f4f6; /* Light background */
    --ai-seo-neutral-200: #e5e7eb; /* Border light */
    --ai-seo-neutral-300: #d1d5db; /* Border default */
    --ai-seo-neutral-400: #9ca3af; /* Muted text */
    --ai-seo-neutral-500: #6b7280; /* Secondary text */
    --ai-seo-neutral-600: #4b5563; /* Primary text */
    --ai-seo-neutral-700: #374151; /* Heading text */
    --ai-seo-neutral-800: #1f2937; /* Dark text */
    --ai-seo-neutral-900: #111827; /* Darkest text */

    /* Accent Colors - Cyberpunk Tech Palette */
    --ai-seo-accent-purple-hue: 285;
    --ai-seo-accent-purple: hsl(var(--ai-seo-accent-purple-hue), 100%, 65%);
    --ai-seo-accent-purple-dark: hsl(var(--ai-seo-accent-purple-hue), 100%, 55%);
    --ai-seo-accent-purple-light: hsl(var(--ai-seo-accent-purple-hue), 100%, 95%);
    --ai-seo-accent-purple-border: hsl(var(--ai-seo-accent-purple-hue), 100%, 85%);

    --ai-seo-accent-blue-hue: 195;
    --ai-seo-accent-blue: hsl(var(--ai-seo-accent-blue-hue), 100%, 60%);
    --ai-seo-accent-blue-dark: hsl(var(--ai-seo-accent-blue-hue), 100%, 50%);
    --ai-seo-accent-blue-light: hsl(var(--ai-seo-accent-blue-hue), 100%, 95%);
    --ai-seo-accent-blue-border: hsl(var(--ai-seo-accent-blue-hue), 100%, 85%);

    --ai-seo-accent-pink-hue: 320;
    --ai-seo-accent-pink: hsl(var(--ai-seo-accent-pink-hue), 100%, 70%);
    --ai-seo-accent-pink-dark: hsl(var(--ai-seo-accent-pink-hue), 100%, 60%);
    --ai-seo-accent-pink-light: hsl(var(--ai-seo-accent-pink-hue), 100%, 95%);
    --ai-seo-accent-pink-border: hsl(var(--ai-seo-accent-pink-hue), 100%, 85%);

    /* New Tech Accent Colors */
    --ai-seo-accent-cyan-hue: 175;
    --ai-seo-accent-cyan: hsl(var(--ai-seo-accent-cyan-hue), 100%, 60%);
    --ai-seo-accent-cyan-dark: hsl(var(--ai-seo-accent-cyan-hue), 100%, 50%);
    --ai-seo-accent-cyan-light: hsl(var(--ai-seo-accent-cyan-hue), 100%, 95%);
    --ai-seo-accent-cyan-border: hsl(var(--ai-seo-accent-cyan-hue), 100%, 85%);

    /* Semantic Colors - Tech-Focused Enhanced Palette */
    --ai-seo-success-hue: 140;
    --ai-seo-success-color: hsl(var(--ai-seo-success-hue), 90%, 45%);
    --ai-seo-success-dark: hsl(var(--ai-seo-success-hue), 90%, 35%);
    --ai-seo-success-light: hsl(var(--ai-seo-success-hue), 90%, 95%);
    --ai-seo-success-border: hsl(var(--ai-seo-success-hue), 90%, 85%);
    --ai-seo-success-bg: hsl(var(--ai-seo-success-hue), 90%, 97%);
    --ai-seo-success-glow: 0 0 15px hsla(var(--ai-seo-success-hue), 90%, 45%, 0.5);

    --ai-seo-warning-hue: 35;
    --ai-seo-warning-color: hsl(var(--ai-seo-warning-hue), 100%, 55%);
    --ai-seo-warning-dark: hsl(var(--ai-seo-warning-hue), 100%, 45%);
    --ai-seo-warning-darker: hsl(var(--ai-seo-warning-hue), 100%, 35%);
    --ai-seo-warning-light: hsl(var(--ai-seo-warning-hue), 100%, 97%);
    --ai-seo-warning-border: hsl(var(--ai-seo-warning-hue), 100%, 85%);
    --ai-seo-warning-bg: hsl(var(--ai-seo-warning-hue), 100%, 97%);
    --ai-seo-warning-glow: 0 0 15px hsla(var(--ai-seo-warning-hue), 100%, 55%, 0.5);

    --ai-seo-error-hue: 350;
    --ai-seo-error-color: hsl(var(--ai-seo-error-hue), 100%, 60%);
    --ai-seo-error-dark: hsl(var(--ai-seo-error-hue), 100%, 50%);
    --ai-seo-error-darker: hsl(var(--ai-seo-error-hue), 100%, 40%);
    --ai-seo-error-light: hsl(var(--ai-seo-error-hue), 100%, 97%);
    --ai-seo-error-border: hsl(var(--ai-seo-error-hue), 100%, 87%);
    --ai-seo-error-bg: hsl(var(--ai-seo-error-hue), 100%, 97%);
    --ai-seo-error-glow: 0 0 15px hsla(var(--ai-seo-error-hue), 100%, 60%, 0.5);

    --ai-seo-info-hue: 200;
    --ai-seo-info-color: hsl(var(--ai-seo-info-hue), 100%, 55%);
    --ai-seo-info-dark: hsl(var(--ai-seo-info-hue), 100%, 45%);
    --ai-seo-info-light: hsl(var(--ai-seo-info-hue), 100%, 97%);
    --ai-seo-info-border: hsl(var(--ai-seo-info-hue), 100%, 87%);
    --ai-seo-info-bg: hsl(var(--ai-seo-info-hue), 100%, 97%);
    --ai-seo-info-glow: 0 0 15px hsla(var(--ai-seo-info-hue), 100%, 55%, 0.5);

    /* Semantic Mapping */
    --ai-seo-text-color: var(--ai-seo-neutral-800);
    --ai-seo-text-light: var(--ai-seo-neutral-600);
    --ai-seo-text-lighter: var(--ai-seo-neutral-500);
    --ai-seo-text-lightest: var(--ai-seo-neutral-400);

    --ai-seo-link-color: var(--ai-seo-primary-color);
    --ai-seo-link-hover-color: var(--ai-seo-primary-dark);

    --ai-seo-bg-white: #ffffff;
    --ai-seo-bg-canvas: var(--ai-seo-neutral-50);
    --ai-seo-bg-subtle: var(--ai-seo-neutral-100);
    --ai-seo-bg-alt: var(--ai-seo-neutral-200);
    --ai-seo-bg-dark: var(--ai-seo-neutral-900);
    --ai-seo-bg-glass: hsla(0, 0%, 100%, 0.9);

    --ai-seo-border-color: var(--ai-seo-neutral-200);
    --ai-seo-border-light-color: var(--ai-seo-neutral-100);
    --ai-seo-border-dark-color: var(--ai-seo-neutral-300);
    --ai-seo-border-input-color: var(--ai-seo-neutral-300);
    --ai-seo-border-focus-color: var(--ai-seo-primary-color);

    /* --- Quantum UI Elements & Typography - Tech-Focused --- */
    --ai-seo-font-family: "Space Grotesk", "Outfit", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    --ai-seo-font-monospace: "Fira Code", "SF Mono", Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
    --ai-seo-base-font-size: 15px;
    --ai-seo-line-height-base: 1.65;
    --ai-seo-line-height-heading: 1.3;
    --ai-seo-letter-spacing-tight: -0.02em;
    --ai-seo-letter-spacing-normal: 0;
    --ai-seo-letter-spacing-wide: 0.02em;
    --ai-seo-letter-spacing-wider: 0.04em;

    /* Quantum Radii System - Tech-Focused Sharp Edges */
    --ai-seo-border-radius-xs: 4px;
    --ai-seo-border-radius-sm: 6px;
    --ai-seo-border-radius: 8px;
    --ai-seo-border-radius-lg: 12px;
    --ai-seo-border-radius-xl: 16px;
    --ai-seo-border-radius-xxl: 20px;
    --ai-seo-border-radius-pill: 9999px;

    /* Border Widths */
    --ai-seo-border-width: 1px;
    --ai-seo-border-width-thick: 2px;
    --ai-seo-border-width-accent: 3px;

    /* Quantum Shadow System - Tech-Focused with Colored Glows */
    --ai-seo-shadow-xs: 0 2px 5px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.04);
    --ai-seo-shadow-sm: 0 3px 8px rgba(0, 0, 0, 0.09), 0 1px 3px rgba(0, 0, 0, 0.06);
    --ai-seo-shadow-md: 0 8px 16px -2px rgba(0, 0, 0, 0.14), 0 3px 8px -2px rgba(0, 0, 0, 0.08);
    --ai-seo-shadow-lg: 0 16px 24px -4px rgba(0, 0, 0, 0.16), 0 6px 12px -4px rgba(0, 0, 0, 0.1);
    --ai-seo-shadow-xl: 0 28px 40px -8px rgba(0, 0, 0, 0.18), 0 12px 18px -8px rgba(0, 0, 0, 0.12);
    --ai-seo-shadow-2xl: 0 40px 60px -12px rgba(0, 0, 0, 0.22), 0 16px 24px -8px rgba(0, 0, 0, 0.14);
    --ai-seo-shadow-inner: inset 0 2px 8px 0 rgba(0, 0, 0, 0.1);
    --ai-seo-shadow-focus-ring: 0 0 0 3px var(--ai-seo-primary-focus-shadow), 0 0 10px 2px rgba(var(--ai-seo-primary-hue), 100%, 60%, 0.2);
    --ai-seo-shadow-focus-ring-secondary: 0 0 0 3px var(--ai-seo-secondary-focus-shadow), 0 0 10px 2px rgba(var(--ai-seo-secondary-hue), 100%, 65%, 0.2);

    /* Tech-Focused Colored Shadows & Glows */
    --ai-seo-shadow-primary: 0 8px 20px -2px hsla(var(--ai-seo-primary-hue), var(--ai-seo-primary-saturation), var(--ai-seo-primary-lightness), 0.25), 0 0 10px hsla(var(--ai-seo-primary-hue), var(--ai-seo-primary-saturation), var(--ai-seo-primary-lightness), 0.1);
    --ai-seo-shadow-secondary: 0 8px 20px -2px hsla(var(--ai-seo-secondary-hue), var(--ai-seo-secondary-saturation), var(--ai-seo-secondary-lightness), 0.25), 0 0 10px hsla(var(--ai-seo-secondary-hue), var(--ai-seo-secondary-saturation), var(--ai-seo-secondary-lightness), 0.1);
    --ai-seo-shadow-success: 0 8px 20px -2px hsla(var(--ai-seo-success-hue), 90%, 45%, 0.25), 0 0 10px hsla(var(--ai-seo-success-hue), 90%, 45%, 0.1);
    --ai-seo-shadow-warning: 0 8px 20px -2px hsla(var(--ai-seo-warning-hue), 100%, 55%, 0.25), 0 0 10px hsla(var(--ai-seo-warning-hue), 100%, 55%, 0.1);
    --ai-seo-shadow-error: 0 8px 20px -2px hsla(var(--ai-seo-error-hue), 100%, 60%, 0.25), 0 0 10px hsla(var(--ai-seo-error-hue), 100%, 60%, 0.1);

    /* Glow Effects for Tech UI */
    --ai-seo-glow-primary: 0 0 15px hsla(var(--ai-seo-primary-hue), var(--ai-seo-primary-saturation), var(--ai-seo-primary-lightness), 0.5);
    --ai-seo-glow-secondary: 0 0 15px hsla(var(--ai-seo-secondary-hue), var(--ai-seo-secondary-saturation), var(--ai-seo-secondary-lightness), 0.5);
    --ai-seo-glow-cyan: 0 0 15px hsla(var(--ai-seo-accent-cyan-hue), 100%, 60%, 0.5);

    /* Quantum Transitions & Animations - Enhanced Tech Responsiveness */
    --ai-seo-transition-fast: all 0.12s cubic-bezier(0.4, 0, 0.2, 1);
    --ai-seo-transition-base: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --ai-seo-transition-slow: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
    --ai-seo-transition-bounce: all 0.45s cubic-bezier(0.34, 1.56, 0.64, 1);
    --ai-seo-transition-spring: all 0.5s cubic-bezier(0.68, -0.6, 0.32, 1.6);

    /* Tech-Focused Easing Curves */
    --ai-seo-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --ai-seo-ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ai-seo-ease-in: cubic-bezier(0.4, 0, 1, 1);
    --ai-seo-ease-bounce: cubic-bezier(0.34, 1.56, 0.64, 1);
    --ai-seo-ease-spring: cubic-bezier(0.68, -0.6, 0.32, 1.6);

    /* Hyperion Spacing Scale - Enhanced Consistency */
    --ai-seo-spacing-2xs: 2px;
    --ai-seo-spacing-xs: 4px;
    --ai-seo-spacing-sm: 8px;
    --ai-seo-spacing-md: 16px;
    --ai-seo-spacing-lg: 24px;
    --ai-seo-spacing-xl: 32px;
    --ai-seo-spacing-2xl: 48px;
    --ai-seo-spacing-3xl: 64px;
    --ai-seo-spacing-4xl: 96px;

    /* Component-specific spacing */
    --ai-seo-card-padding: var(--ai-seo-spacing-xl);
    --ai-seo-card-padding-mobile: var(--ai-seo-spacing-md);
    --ai-seo-input-padding: var(--ai-seo-spacing-md) var(--ai-seo-spacing-lg);
    --ai-seo-button-padding: var(--ai-seo-spacing-md) var(--ai-seo-spacing-xl);
    --ai-seo-section-gap: var(--ai-seo-spacing-2xl);

    /* Quantum Gradients - Tech-Focused Visual Appeal */
    --ai-seo-gradient-primary: linear-gradient(135deg, var(--ai-seo-primary-color) 0%, hsl(var(--ai-seo-primary-hue), 100%, 45%) 100%);
    --ai-seo-gradient-secondary: linear-gradient(135deg, var(--ai-seo-secondary-color) 0%, hsl(var(--ai-seo-secondary-hue), 100%, 35%) 100%);
    --ai-seo-gradient-accent: linear-gradient(135deg, var(--ai-seo-accent-purple) 0%, var(--ai-seo-accent-cyan) 100%);
    --ai-seo-gradient-success: linear-gradient(135deg, var(--ai-seo-success-color) 0%, hsl(var(--ai-seo-success-hue), 90%, 35%) 100%);
    --ai-seo-gradient-warning: linear-gradient(135deg, var(--ai-seo-warning-color) 0%, hsl(var(--ai-seo-warning-hue), 100%, 40%) 100%);
    --ai-seo-gradient-error: linear-gradient(135deg, var(--ai-seo-error-color) 0%, hsl(var(--ai-seo-error-hue), 100%, 40%) 100%);
    --ai-seo-gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
    --ai-seo-gradient-subtle: linear-gradient(135deg, var(--ai-seo-bg-white) 0%, var(--ai-seo-bg-subtle) 100%);
    --ai-seo-gradient-header: linear-gradient(135deg, hsl(var(--ai-seo-primary-hue), 100%, 55%) 0%, hsl(var(--ai-seo-secondary-hue), 100%, 45%) 100%);

    /* Tech-Focused Additional Gradients */
    --ai-seo-gradient-primary-soft: linear-gradient(135deg, hsl(var(--ai-seo-primary-hue), 95%, 94%) 0%, hsl(var(--ai-seo-primary-hue), 95%, 88%) 100%);
    --ai-seo-gradient-secondary-soft: linear-gradient(135deg, hsl(var(--ai-seo-secondary-hue), 95%, 94%) 0%, hsl(var(--ai-seo-secondary-hue), 95%, 88%) 100%);
    --ai-seo-gradient-accent-soft: linear-gradient(135deg, var(--ai-seo-accent-purple-light) 0%, var(--ai-seo-accent-cyan-light) 100%);
    --ai-seo-gradient-card: linear-gradient(160deg, var(--ai-seo-bg-white) 0%, var(--ai-seo-bg-subtle) 100%);
    --ai-seo-gradient-card-hover: linear-gradient(160deg, var(--ai-seo-bg-white) 0%, var(--ai-seo-primary-lighter) 100%);

    /* Cyberpunk Tech Gradients */
    --ai-seo-gradient-tech: linear-gradient(135deg, var(--ai-seo-primary-color) 0%, var(--ai-seo-accent-cyan) 50%, var(--ai-seo-secondary-color) 100%);
    --ai-seo-gradient-tech-dark: linear-gradient(135deg, var(--ai-seo-primary-dark) 0%, var(--ai-seo-accent-cyan-dark) 50%, var(--ai-seo-secondary-dark) 100%);
    --ai-seo-gradient-tech-light: linear-gradient(135deg, var(--ai-seo-primary-light) 0%, var(--ai-seo-accent-cyan-light) 50%, var(--ai-seo-secondary-light) 100%);

    /* Quantum Effects - Tech-Focused Visual Depth */
    --ai-seo-backdrop-blur: blur(12px);
    --ai-seo-backdrop-saturate: saturate(1.7);
    --ai-seo-backdrop-filter: var(--ai-seo-backdrop-blur) var(--ai-seo-backdrop-saturate);
    --ai-seo-backdrop-filter-heavy: blur(20px) saturate(1.4);
    --ai-seo-backdrop-filter-light: blur(6px) saturate(1.5);

    /* Tech Glassmorphism Effects */
    --ai-seo-glass-white: rgba(255, 255, 255, 0.9);
    --ai-seo-glass-primary: hsla(var(--ai-seo-primary-hue), 95%, 97%, 0.9);
    --ai-seo-glass-secondary: hsla(var(--ai-seo-secondary-hue), 95%, 97%, 0.9);
    --ai-seo-glass-dark: rgba(30, 30, 40, 0.85);

    /* Tech UI Border Glow Effects */
    --ai-seo-border-glow-primary: 0 0 0 1px var(--ai-seo-primary-border), 0 0 8px 0 hsla(var(--ai-seo-primary-hue), 100%, 60%, 0.5);
    --ai-seo-border-glow-secondary: 0 0 0 1px var(--ai-seo-secondary-border), 0 0 8px 0 hsla(var(--ai-seo-secondary-hue), 100%, 65%, 0.5);
    --ai-seo-border-glow-cyan: 0 0 0 1px var(--ai-seo-accent-cyan-border), 0 0 8px 0 hsla(var(--ai-seo-accent-cyan-hue), 100%, 60%, 0.5);

    /* Score Colors - Tech-Focused Enhanced Vibrancy */
    --ai-seo-score-excellent: hsl(140, 90%, 45%);
    --ai-seo-score-good: hsl(95, 85%, 45%);
    --ai-seo-score-average: hsl(35, 100%, 55%);
    --ai-seo-score-poor: hsl(15, 100%, 60%);
    --ai-seo-score-bad: hsl(350, 100%, 60%);

    /* Score Gradients - Tech-Focused */
    --ai-seo-score-excellent-gradient: linear-gradient(135deg, var(--ai-seo-score-excellent) 0%, hsl(140, 90%, 35%) 100%);
    --ai-seo-score-good-gradient: linear-gradient(135deg, var(--ai-seo-score-good) 0%, hsl(95, 85%, 35%) 100%);
    --ai-seo-score-average-gradient: linear-gradient(135deg, var(--ai-seo-score-average) 0%, hsl(35, 100%, 45%) 100%);
    --ai-seo-score-poor-gradient: linear-gradient(135deg, var(--ai-seo-score-poor) 0%, hsl(15, 100%, 50%) 100%);
    --ai-seo-score-bad-gradient: linear-gradient(135deg, var(--ai-seo-score-bad) 0%, hsl(350, 100%, 50%) 100%);

    /* Score Glows - Tech-Focused */
    --ai-seo-score-excellent-glow: 0 0 15px hsla(140, 90%, 45%, 0.5);
    --ai-seo-score-good-glow: 0 0 15px hsla(95, 85%, 45%, 0.5);
    --ai-seo-score-average-glow: 0 0 15px hsla(35, 100%, 55%, 0.5);
    --ai-seo-score-poor-glow: 0 0 15px hsla(15, 100%, 60%, 0.5);
    --ai-seo-score-bad-glow: 0 0 15px hsla(350, 100%, 60%, 0.5);
}

/* ==========================================================================
   == Stellar Global Resets & Foundational Styles ==
   ========================================================================== */

.ai-seo-optimizer-wrap {
    /* Modern CSS Reset */
    *, *::before, *::after {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
        border: 0;
        font-size: 100%;
        font: inherit;
        vertical-align: baseline;
    }

    /* HTML5 display roles */
    article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
        display: block;
    }

    /* List reset */
    ol, ul {
        list-style: none;
    }

    /* Quote reset */
    blockquote, q {
        quotes: none;
    }

    blockquote:before, blockquote:after, q:before, q:after {
        content: '';
        content: none;
    }

    /* Table reset */
    table {
        border-collapse: collapse;
        border-spacing: 0;
    }

    /* Hyperion container styles - Enhanced Visual Appeal */
    font-family: var(--ai-seo-font-family);
    font-size: var(--ai-seo-base-font-size);
    font-weight: 400;
    line-height: var(--ai-seo-line-height-base);
    color: var(--ai-seo-text-color);
    background-color: var(--ai-seo-bg-white);
    margin: var(--ai-seo-spacing-lg) 20px var(--ai-seo-spacing-lg) 0;
    padding: var(--ai-seo-spacing-xl);
    border-radius: var(--ai-seo-border-radius-lg);
    box-shadow: var(--ai-seo-shadow-xl);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: relative;
    overflow: hidden;
    border: var(--ai-seo-border-width) solid var(--ai-seo-border-light-color);
    transition: var(--ai-seo-transition-base);

    /* Enhanced gradient background */
    background-image: linear-gradient(160deg,
        var(--ai-seo-bg-white) 0%,
        var(--ai-seo-bg-subtle) 100%);

    /* Modern accent border with gradient */
    border-top: var(--ai-seo-border-width-accent) solid;
    border-image: var(--ai-seo-gradient-primary) 1;
}

/* Hyperion Typography System - Enhanced Readability & Visual Appeal */
.ai-seo-optimizer-wrap h1,
.ai-seo-optimizer-wrap h2,
.ai-seo-optimizer-wrap h3,
.ai-seo-optimizer-wrap h4,
.ai-seo-optimizer-wrap h5,
.ai-seo-optimizer-wrap h6 {
    margin: 0 0 var(--ai-seo-spacing-md) 0;
    line-height: var(--ai-seo-line-height-heading);
    font-weight: 600;
    color: var(--ai-seo-neutral-800);
    letter-spacing: var(--ai-seo-letter-spacing-tight);
    transition: var(--ai-seo-transition-base);
}

/* Main title - Hyperion Design */
.ai-seo-optimizer-wrap h1 {
    font-size: 30px;
    font-weight: 700;
    letter-spacing: var(--ai-seo-letter-spacing-tight);
    color: var(--ai-seo-neutral-900);
    margin-bottom: var(--ai-seo-spacing-lg);
    position: relative;
    display: inline-block;
}

/* Enhanced gradient underline effect for main title */
.ai-seo-optimizer-wrap h1::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--ai-seo-gradient-primary);
    border-radius: var(--ai-seo-border-radius-pill);
    opacity: 0.9;
    transform: scaleX(0.3);
    transform-origin: left;
    transition: all 0.5s var(--ai-seo-ease-spring);
    box-shadow: var(--ai-seo-shadow-primary);
}

.ai-seo-optimizer-wrap h1:hover::after {
    transform: scaleX(0.7);
    opacity: 1;
}

/* Section title - Hyperion Design */
.ai-seo-optimizer-wrap h2 {
    font-size: 24px;
    font-weight: 600;
    color: var(--ai-seo-neutral-800);
    border-bottom: var(--ai-seo-border-width) solid var(--ai-seo-border-color);
    padding-bottom: var(--ai-seo-spacing-sm);
    margin-bottom: var(--ai-seo-spacing-lg);
    position: relative;
}

/* Enhanced accent line for section titles with animation */
.ai-seo-optimizer-wrap h2::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 80px;
    height: 3px;
    background: var(--ai-seo-gradient-primary);
    border-radius: var(--ai-seo-border-radius-pill);
    box-shadow: var(--ai-seo-shadow-xs);
    transition: all 0.4s var(--ai-seo-ease-out);
}

.ai-seo-optimizer-wrap h2:hover::after {
    width: 140px;
    box-shadow: var(--ai-seo-shadow-primary);
}

/* Subsection title / Card title - Hyperion Design */
.ai-seo-optimizer-wrap h3 {
    font-size: 19px;
    font-weight: 600;
    color: var(--ai-seo-primary-color);
    margin-top: var(--ai-seo-spacing-lg);
    margin-bottom: var(--ai-seo-spacing-md);
    position: relative;
    display: inline-block;
    padding-bottom: 6px;
}

/* Enhanced gradient underline for h3 */
.ai-seo-optimizer-wrap h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(to right, var(--ai-seo-primary-color), transparent);
    opacity: 0.6;
    transition: all 0.3s var(--ai-seo-ease-out);
}

.ai-seo-optimizer-wrap h3:hover::after {
    opacity: 1;
    background: var(--ai-seo-gradient-primary);
}

/* Minor heading / Label - Hyperion Design */
.ai-seo-optimizer-wrap h4 {
    font-size: 17px;
    font-weight: 600;
    color: var(--ai-seo-neutral-700);
    margin-bottom: var(--ai-seo-spacing-sm);
    display: flex;
    align-items: center;
    transition: all 0.3s var(--ai-seo-ease-out);
}

/* Enhanced icon for h4 with animation */
.ai-seo-optimizer-wrap h4::before {
    content: '•';
    color: var(--ai-seo-secondary-color);
    margin-right: 10px;
    font-size: 1.3em;
    line-height: 0;
    transition: all 0.3s var(--ai-seo-ease-bounce);
}

.ai-seo-optimizer-wrap h4:hover {
    color: var(--ai-seo-primary-color);
}

.ai-seo-optimizer-wrap h4:hover::before {
    color: var(--ai-seo-primary-color);
    transform: scale(1.2);
}

/* Paragraph styles - Hyperion Design */
.ai-seo-optimizer-wrap p {
    margin: 0 0 var(--ai-seo-spacing-md) 0;
    color: var(--ai-seo-text-light);
    font-size: inherit;
    line-height: 1.75;
    letter-spacing: var(--ai-seo-letter-spacing-normal);
}

/* Description text - Hyperion Design */
.ai-seo-optimizer-wrap p.description,
.ai-seo-optimizer-wrap .form-table td p.description,
.ai-seo-optimizer-wrap .ai-seo-info-box p {
    color: var(--ai-seo-text-lighter);
    font-size: 14px;
    margin-top: var(--ai-seo-spacing-xs);
    line-height: 1.65;
    letter-spacing: var(--ai-seo-letter-spacing-wide);
}

.ai-seo-optimizer-wrap p.description strong {
    color: var(--ai-seo-primary-color);
    font-weight: 600;
    letter-spacing: var(--ai-seo-letter-spacing-normal);
}

.ai-seo-optimizer-wrap p.description code {
    font-size: 0.95em;
    background-color: var(--ai-seo-primary-lighter);
    color: var(--ai-seo-primary-dark);
    padding: 3px 6px;
    border-radius: var(--ai-seo-border-radius-xs);
    font-weight: 500;
    letter-spacing: -0.01em;
    box-shadow: var(--ai-seo-shadow-xs);
}

/* Horizontal rule - Hyperion Design */
.ai-seo-optimizer-wrap hr {
    border: 0;
    height: 1px;
    background: linear-gradient(to right, var(--ai-seo-border-color), var(--ai-seo-border-light-color));
    margin: var(--ai-seo-spacing-xl) 0;
    opacity: 0.8;
    border-radius: var(--ai-seo-border-radius-pill);
}

/* Link styles - Hyperion Design */
.ai-seo-optimizer-wrap a {
    color: var(--ai-seo-link-color);
    text-decoration: none;
    transition: all var(--ai-seo-transition-fast);
    border-radius: var(--ai-seo-border-radius-xs);
    font-weight: 500;
    outline-offset: 3px;
    position: relative;
    padding: 0 1px;
}

.ai-seo-optimizer-wrap a:hover {
    color: var(--ai-seo-link-hover-color);
    text-decoration: none;
}

/* Enhanced underline effect on hover with animation */
.ai-seo-optimizer-wrap a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: currentColor;
    opacity: 0;
    transform: scaleX(0.7);
    transform-origin: center;
    transition: all 0.3s var(--ai-seo-ease-out);
}

.ai-seo-optimizer-wrap a:hover::after {
    opacity: 0.8;
    transform: scaleX(1);
    height: 2px;
}

.ai-seo-optimizer-wrap a:focus {
    outline: none;
}

.ai-seo-optimizer-wrap a:focus-visible {
    outline: 2px solid transparent;
    box-shadow: var(--ai-seo-shadow-focus-ring);
    text-decoration: none;
}

/* Code and pre styles */
.ai-seo-optimizer-wrap code,
.ai-seo-optimizer-wrap pre {
    font-family: var(--ai-seo-font-monospace);
    font-size: 0.9em;
    background-color: var(--ai-seo-neutral-50);
    padding: var(--ai-seo-spacing-xxs) var(--ai-seo-spacing-xs);
    border-radius: var(--ai-seo-border-radius-xs);
    border: var(--ai-seo-border-width) solid var(--ai-seo-neutral-200);
    color: var(--ai-seo-neutral-700);
    word-break: break-word;
}

.ai-seo-optimizer-wrap pre {
    padding: var(--ai-seo-spacing-md);
    line-height: 1.6;
    overflow-x: auto;
    white-space: pre-wrap;
    margin-bottom: var(--ai-seo-spacing-md);
}

/* List styles */
.ai-seo-optimizer-wrap ul,
.ai-seo-optimizer-wrap ol {
    margin: 0 0 var(--ai-seo-spacing-md) var(--ai-seo-spacing-md);
    padding-left: var(--ai-seo-spacing-lg);
    color: var(--ai-seo-text-light);
}

.ai-seo-optimizer-wrap ul li,
.ai-seo-optimizer-wrap ol li {
    margin-bottom: var(--ai-seo-spacing-xs);
    line-height: 1.6;
    padding-left: var(--ai-seo-spacing-xs);
}

/* Custom bullet styling */
.ai-seo-optimizer-wrap ul {
    list-style: none;
}

.ai-seo-optimizer-wrap ul li::before {
    content: "•";
    color: var(--ai-seo-primary-color);
    font-weight: bold;
    display: inline-block;
    width: 1em;
    margin-left: -1.2em;
    font-size: 1em;
    line-height: 1;
}

/* Override for internal links list - NO bullets */
.ai-seo-optimizer-wrap .ai-seo-links-list {
    margin: 0 !important;
    padding: 0 !important;
}

.ai-seo-optimizer-wrap .ai-seo-links-list li {
    padding-left: 0 !important;
    margin-left: 0 !important;
}

.ai-seo-optimizer-wrap .ai-seo-links-list li::before {
    content: none !important;
    display: none !important;
    width: 0 !important;
    margin: 0 !important;
}

.ai-seo-optimizer-wrap ol {
    list-style: decimal;
}

/* Card Container - Premium, Modern Design */
.ai-seo-box,
.ai-seo-card {
    background-color: var(--ai-seo-bg-white);
    border: var(--ai-seo-border-width) solid var(--ai-seo-border-color);
    border-radius: var(--ai-seo-border-radius-lg);
    padding: var(--ai-seo-spacing-xl);
    margin-bottom: var(--ai-seo-spacing-lg);
    box-shadow: var(--ai-seo-shadow-md);
    transition: all var(--ai-seo-transition-base);
    position: relative;
    overflow: hidden;

    /* Subtle gradient background */
    background-image: linear-gradient(to bottom right,
        var(--ai-seo-bg-white) 0%,
        rgba(249, 250, 251, 0.8) 100%);
}

/* Card hover effects */
.ai-seo-box:hover,
.ai-seo-card:hover {
    box-shadow: var(--ai-seo-shadow-lg);
    transform: translateY(-3px);
    border-color: var(--ai-seo-primary-border);
}

/* Card with accent border */
.ai-seo-box.with-accent,
.ai-seo-card.with-accent {
    border-top: 3px solid var(--ai-seo-primary-color);
}

/* Card with secondary accent */
.ai-seo-box.with-secondary-accent,
.ai-seo-card.with-secondary-accent {
    border-top: 3px solid var(--ai-seo-secondary-color);
}

/* Card with success accent */
.ai-seo-box.with-success-accent,
.ai-seo-card.with-success-accent {
    border-top: 3px solid var(--ai-seo-success-color);
}

/* Card with warning accent */
.ai-seo-box.with-warning-accent,
.ai-seo-card.with-warning-accent {
    border-top: 3px solid var(--ai-seo-warning-color);
}

/* Card with error accent */
.ai-seo-box.with-error-accent,
.ai-seo-card.with-error-accent {
    border-top: 3px solid var(--ai-seo-error-color);
}

/* Glass card effect */
.ai-seo-box.glass-card,
.ai-seo-card.glass-card {
    background-color: rgba(255, 255, 255, 0.85);
    backdrop-filter: var(--ai-seo-backdrop-filter);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: var(--ai-seo-shadow-lg);
}

/* Tab Content Container - Premium Design */
.ai-seo-tabs-container > .tab-content {
    border: var(--ai-seo-border-width) solid var(--ai-seo-border-color);
    border-top: none;
    border-radius: 0 0 var(--ai-seo-border-radius-lg) var(--ai-seo-border-radius-lg);
    margin-top: calc(-1 * var(--ai-seo-border-width));
    background-color: var(--ai-seo-bg-white);
    padding: var(--ai-seo-spacing-xl);
    box-shadow: var(--ai-seo-shadow-md);

    /* Subtle gradient background */
    background-image: linear-gradient(to bottom right,
        var(--ai-seo-bg-white) 0%,
        rgba(249, 250, 251, 0.8) 100%);
}

.ai-seo-tab-content-inner {
    padding: 0;
}

/* Loading Placeholder - Premium Design */
.ai-seo-loading-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--ai-seo-spacing-md);
    padding: var(--ai-seo-spacing-xxl) 0;
    font-size: 1em;
    color: var(--ai-seo-text-lighter);
    min-height: 200px;
    text-align: center;
    position: relative;
}

/* Enhanced loading animation */
.ai-seo-loading-placeholder .spinner {
    visibility: visible;
    opacity: 1;
    float: none;
    margin: 0;
    width: 40px;
    height: 40px;
    border: 3px solid rgba(var(--ai-seo-primary-hue), var(--ai-seo-primary-saturation), var(--ai-seo-primary-lightness), 0.1);
    border-top-color: var(--ai-seo-primary-color);
    border-radius: 50%;
    animation: ai-seo-spin 0.8s linear infinite;
    box-shadow: 0 0 15px rgba(var(--ai-seo-primary-hue), var(--ai-seo-primary-saturation), var(--ai-seo-primary-lightness), 0.2);
}

/* Loading text animation */
.ai-seo-loading-placeholder .loading-text {
    position: relative;
    font-weight: 500;
}

.ai-seo-loading-placeholder .loading-text::after {
    content: '...';
    position: absolute;
    animation: ai-seo-loading-dots 1.5s infinite;
    width: 24px;
    text-align: left;
}

@keyframes ai-seo-loading-dots {
    0% { content: '.'; }
    33% { content: '..'; }
    66% { content: '...'; }
    100% { content: '.'; }
}

.ai-seo-loading-placeholder .spinner {
    visibility: visible;
    opacity: 1;
    float: none;
    margin: 0;
    width: 24px;
    height: 24px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-top-color: var(--ai-seo-primary-color);
    border-radius: 50%;
    animation: ai-seo-spin 0.8s linear infinite;
}

/* Tooltip Styling */
[title] {
    cursor: help;
    text-decoration: none;
    border-bottom: 1px dotted var(--ai-seo-neutral-300);
}

/* ==========================================================================
   == Quantum UI Elements - Modern & Minimal ==
   ========================================================================== */

/* Buttons - Quantum Tech Design */
.ai-seo-optimizer-wrap .button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--ai-seo-spacing-sm);
    padding: 12px 22px;
    font-size: 15px;
    font-weight: 500;
    letter-spacing: 0.02em;
    line-height: 1.4;
    text-align: center;
    text-decoration: none;
    white-space: nowrap;
    border: var(--ai-seo-border-width) solid var(--ai-seo-neutral-300);
    border-radius: var(--ai-seo-border-radius);
    background-color: var(--ai-seo-bg-white);
    color: var(--ai-seo-neutral-700);
    box-shadow: var(--ai-seo-shadow-sm);
    cursor: pointer;
    transition: all 0.25s var(--ai-seo-ease-out);
    position: relative;
    overflow: hidden;
    user-select: none;
    height: auto;
    min-height: 44px;
    z-index: 1;
    backdrop-filter: var(--ai-seo-backdrop-filter-light);
}

/* Button tech glow effect on hover */
.ai-seo-optimizer-wrap .button::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
    );
    transform: translateX(-100%);
    z-index: -1;
    transition: transform 0.6s ease;
}

.ai-seo-optimizer-wrap .button:hover::after {
    transform: translateX(100%);
}

/* Button border glow effect */
.ai-seo-optimizer-wrap .button::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    border-radius: inherit;
    background: var(--ai-seo-gradient-tech);
    opacity: 0;
    z-index: -2;
    transition: opacity 0.3s ease;
}

.ai-seo-optimizer-wrap .button:hover::before {
    opacity: 0.5;
}

/* Icon styling in buttons */
.ai-seo-optimizer-wrap .button .dashicons {
    margin: 0;
    font-size: 16px;
    height: 16px;
    width: 16px;
    line-height: 1;
    vertical-align: -2px;
    transition: transform 0.3s var(--ai-seo-transition-bounce);
}

/* Button hover state */
.ai-seo-optimizer-wrap .button:hover:not(:disabled) {
    background-color: var(--ai-seo-bg-subtle);
    border-color: var(--ai-seo-neutral-400);
    color: var(--ai-seo-neutral-800);
    box-shadow: var(--ai-seo-shadow-md);
    transform: translateY(-2px);
}

/* Button hover icon animation */
.ai-seo-optimizer-wrap .button:hover:not(:disabled) .dashicons {
    transform: translateX(3px);
}

/* Button active state */
.ai-seo-optimizer-wrap .button:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: var(--ai-seo-shadow-inner);
    background-color: var(--ai-seo-bg-alt);
}

/* Button focus states */
.ai-seo-optimizer-wrap .button:focus {
    outline: none;
}

.ai-seo-optimizer-wrap .button:focus-visible {
    border-color: var(--ai-seo-primary-color);
    box-shadow: var(--ai-seo-shadow-sm), var(--ai-seo-shadow-focus-ring);
}

/* Primary Button - Tech-Focused Design */
.ai-seo-optimizer-wrap .button.button-primary {
    background: var(--ai-seo-gradient-primary);
    border-color: transparent;
    color: var(--ai-seo-primary-text);
    box-shadow: var(--ai-seo-shadow-md);
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

/* Primary button tech glow effect */
.ai-seo-optimizer-wrap .button.button-primary::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.ai-seo-optimizer-wrap .button.button-primary:hover::after {
    transform: translateX(100%);
}

.ai-seo-optimizer-wrap .button.button-primary:hover:not(:disabled) {
    background: linear-gradient(135deg,
        hsl(var(--ai-seo-primary-hue), 100%, 60%) 0%,
        hsl(var(--ai-seo-primary-hue), 100%, 50%) 100%);
    border-color: transparent;
    color: var(--ai-seo-primary-text);
    box-shadow: var(--ai-seo-shadow-lg), var(--ai-seo-glow-primary);
    transform: translateY(-2px);
}

.ai-seo-optimizer-wrap .button.button-primary:active:not(:disabled) {
    background: linear-gradient(135deg,
        hsl(var(--ai-seo-primary-hue), 100%, 50%) 0%,
        hsl(var(--ai-seo-primary-hue), 100%, 40%) 100%);
    transform: translateY(0);
    box-shadow: var(--ai-seo-shadow-inner);
}

.ai-seo-optimizer-wrap .button.button-primary:focus-visible {
    border-color: transparent;
    box-shadow: var(--ai-seo-shadow-md), var(--ai-seo-border-glow-primary);
}

/* Success Button - Premium Design */
.ai-seo-optimizer-wrap .button.button-success {
    background: var(--ai-seo-gradient-success);
    border-color: transparent;
    color: var(--ai-seo-primary-text);
    box-shadow: var(--ai-seo-shadow-md);
    font-weight: 600;
}

.ai-seo-optimizer-wrap .button.button-success:hover:not(:disabled) {
    background: linear-gradient(135deg,
        hsl(var(--ai-seo-success-hue), 80%, 45%) 0%,
        hsl(var(--ai-seo-success-hue), 85%, 35%) 100%);
    box-shadow: var(--ai-seo-shadow-lg), 0 5px 15px -3px rgba(var(--ai-seo-success-hue), 80%, 40%, 0.4);
}

/* Danger Button - Premium Design */
.ai-seo-optimizer-wrap .button.button-danger {
    background: var(--ai-seo-gradient-error);
    border-color: transparent;
    color: var(--ai-seo-primary-text);
    box-shadow: var(--ai-seo-shadow-md);
    font-weight: 600;
}

.ai-seo-optimizer-wrap .button.button-danger:hover:not(:disabled) {
    background: linear-gradient(135deg,
        hsl(var(--ai-seo-error-hue), 85%, 55%) 0%,
        hsl(var(--ai-seo-error-hue), 90%, 45%) 100%);
    box-shadow: var(--ai-seo-shadow-lg), 0 5px 15px -3px rgba(var(--ai-seo-error-hue), 85%, 50%, 0.4);
}

/* Secondary Button - Premium Design */
.ai-seo-optimizer-wrap .button.button-secondary {
    background: var(--ai-seo-gradient-secondary);
    border-color: transparent;
    color: var(--ai-seo-primary-text);
    box-shadow: var(--ai-seo-shadow-md);
    font-weight: 600;
}

.ai-seo-optimizer-wrap .button.button-secondary:hover:not(:disabled) {
    background: linear-gradient(135deg,
        hsl(var(--ai-seo-secondary-hue), 90%, 45%) 0%,
        hsl(var(--ai-seo-secondary-hue), 95%, 35%) 100%);
    box-shadow: var(--ai-seo-shadow-lg), 0 5px 15px -3px rgba(var(--ai-seo-secondary-hue), 90%, 40%, 0.4);
}

/* Disabled Button State - Premium Design */
.ai-seo-optimizer-wrap .button:disabled,
.ai-seo-optimizer-wrap .button[disabled] {
    cursor: not-allowed;
    opacity: 0.65;
    transform: none !important;
    box-shadow: none !important;
    background-color: var(--ai-seo-neutral-100);
    border-color: var(--ai-seo-neutral-200);
    color: var(--ai-seo-neutral-400);
}

.ai-seo-optimizer-wrap .button.button-primary:disabled,
.ai-seo-optimizer-wrap .button.button-primary[disabled],
.ai-seo-optimizer-wrap .button.button-secondary:disabled,
.ai-seo-optimizer-wrap .button.button-secondary[disabled],
.ai-seo-optimizer-wrap .button.button-success:disabled,
.ai-seo-optimizer-wrap .button.button-success[disabled],
.ai-seo-optimizer-wrap .button.button-danger:disabled,
.ai-seo-optimizer-wrap .button.button-danger[disabled] {
    background: var(--ai-seo-neutral-300);
    border-color: transparent;
    color: var(--ai-seo-neutral-100);
}

/* Button Sizes - Premium Design */
.ai-seo-optimizer-wrap .button.button-hero {
    padding: 14px 28px;
    font-size: 16px;
    font-weight: 600;
    border-radius: var(--ai-seo-border-radius-lg);
    min-height: 52px;
    letter-spacing: 0.01em;
}

.ai-seo-optimizer-wrap .button.button-large {
    padding: 12px 22px;
    font-size: 15px;
    min-height: 46px;
}

.ai-seo-optimizer-wrap .button.button-small {
    padding: 7px 14px;
    font-size: 13px;
    min-height: 34px;
}

.ai-seo-optimizer-wrap .button.button-small .dashicons {
    font-size: 14px;
    height: 14px;
    width: 14px;
}

/* Button with icon only - Premium Design */
.ai-seo-optimizer-wrap .button.icon-only {
    padding: 8px;
    min-width: 40px;
    border-radius: var(--ai-seo-border-radius-sm);
}

.ai-seo-optimizer-wrap .button.icon-only .dashicons {
    margin: 0;
    font-size: 18px;
    height: 18px;
    width: 18px;
}

/* Outline Button - Premium Design */
.ai-seo-optimizer-wrap .button.button-outline {
    background: transparent;
    border: 2px solid var(--ai-seo-primary-color);
    color: var(--ai-seo-primary-color);
    font-weight: 600;
}

.ai-seo-optimizer-wrap .button.button-outline:hover:not(:disabled) {
    background-color: var(--ai-seo-primary-lighter);
    border-color: var(--ai-seo-primary-dark);
    color: var(--ai-seo-primary-dark);
}

/* Spinners - Clean & Minimal */
.ai-seo-optimizer-wrap .spinner {
    display: inline-block;
    vertical-align: middle;
    visibility: hidden;
    opacity: 0;
    margin-left: var(--ai-seo-spacing-xs);
    transition: opacity 0.2s ease-in-out, visibility 0s 0.2s linear;
    width: 16px;
    height: 16px;
    margin-top: -2px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-top-color: var(--ai-seo-primary-color);
    border-radius: 50%;
    animation: ai-seo-spin 0.8s linear infinite;
}

.ai-seo-optimizer-wrap .spinner.is-active {
    visibility: visible;
    opacity: 1;
    transition-delay: 0s;
}

@keyframes ai-seo-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Spinner inside buttons */
.ai-seo-optimizer-wrap .button .spinner {
    margin-left: 0;
    margin-right: var(--ai-seo-spacing-xs);
    border-color: rgba(255, 255, 255, 0.3);
    border-top-color: var(--ai-seo-primary-text);
}

.ai-seo-optimizer-wrap .button:not(.button-primary) .spinner {
    border-color: rgba(0, 0, 0, 0.1);
    border-top-color: var(--ai-seo-neutral-700);
}

/* AJAX Result / Status Text - Clean & Minimal */
.ai-seo-status-text,
.ai-seo-ajax-result {
    display: inline-flex;
    align-items: center;
    gap: var(--ai-seo-spacing-xs);
    font-size: 13px;
    font-weight: 500;
    padding: 6px 12px;
    border-radius: var(--ai-seo-border-radius);
    border: none;
    background-color: var(--ai-seo-neutral-100);
    color: var(--ai-seo-neutral-600);
    transition: var(--ai-seo-transition-base);
    vertical-align: middle;
    margin-left: var(--ai-seo-spacing-md);
    animation: ai-seo-fadein 0.3s ease-out;
    box-shadow: var(--ai-seo-shadow-xs);
}

.ai-seo-ajax-result {
    font-weight: 500;
}

.ai-seo-ajax-result .dashicons {
    font-size: 16px;
    line-height: 1;
}

/* Success status */
.ai-seo-ajax-result.notice-success {
    color: var(--ai-seo-success-dark);
    background-color: var(--ai-seo-success-light);
    border-left: 3px solid var(--ai-seo-success-color);
}

.ai-seo-ajax-result.notice-success .dashicons::before {
    content: "\f147";
    color: var(--ai-seo-success-color);
}

/* Error status */
.ai-seo-ajax-result.notice-error {
    color: var(--ai-seo-error-dark);
    background-color: var(--ai-seo-error-light);
    border-left: 3px solid var(--ai-seo-error-color);
}

.ai-seo-ajax-result.notice-error .dashicons::before {
    content: "\f534";
    color: var(--ai-seo-error-color);
}

/* Warning status */
.ai-seo-ajax-result.notice-warning {
    color: var(--ai-seo-warning-dark);
    background-color: var(--ai-seo-warning-light);
    border-left: 3px solid var(--ai-seo-warning-color);
}

.ai-seo-ajax-result.notice-warning .dashicons::before {
    content: "\f534";
    color: var(--ai-seo-warning-color);
}

/* Info status */
.ai-seo-ajax-result.notice-info {
    color: var(--ai-seo-info-dark);
    background-color: var(--ai-seo-info-light);
    border-left: 3px solid var(--ai-seo-info-color);
}

.ai-seo-ajax-result.notice-info .dashicons::before {
    content: "\f348";
    color: var(--ai-seo-info-color);
}

/* Notices & Info Boxes - Clean & Minimal */
.ai-seo-admin-notice,
.ai-seo-info-box {
    margin: 0 0 var(--ai-seo-spacing-lg) 0;
    padding: var(--ai-seo-spacing-md);
    border-radius: var(--ai-seo-border-radius);
    box-shadow: var(--ai-seo-shadow-sm);
    display: flex;
    gap: var(--ai-seo-spacing-sm);
    align-items: flex-start;
    border: none;
    border-left: 3px solid transparent;
}

.ai-seo-admin-notice .dashicons,
.ai-seo-info-box .dashicons {
    font-size: 18px;
    flex-shrink: 0;
    margin-top: 2px;
}

.ai-seo-admin-notice p,
.ai-seo-info-box p {
    margin-bottom: var(--ai-seo-spacing-xs);
    line-height: 1.5;
}

.ai-seo-admin-notice p:last-child,
.ai-seo-info-box p:last-child {
    margin-bottom: 0;
}

/* Specific Notice Types */
.ai-seo-admin-notice.notice-error {
    border-left-color: var(--ai-seo-error-color);
    background-color: var(--ai-seo-error-light);
}

.ai-seo-admin-notice.notice-error .dashicons {
    color: var(--ai-seo-error-color);
}

.ai-seo-admin-notice.notice-warning {
    border-left-color: var(--ai-seo-warning-color);
    background-color: var(--ai-seo-warning-light);
}

.ai-seo-admin-notice.notice-warning .dashicons {
    color: var(--ai-seo-warning-color);
}

.ai-seo-admin-notice.notice-success {
    border-left-color: var(--ai-seo-success-color);
    background-color: var(--ai-seo-success-light);
}

.ai-seo-admin-notice.notice-success .dashicons {
    color: var(--ai-seo-success-color);
}

.ai-seo-info-box {
    border-left-color: var(--ai-seo-info-color);
    background-color: var(--ai-seo-info-light);
}

.ai-seo-info-box .dashicons {
    color: var(--ai-seo-info-color);
}

.ai-seo-info-box strong {
    font-weight: 600;
    color: var(--ai-seo-info-dark);
}

/* Important Top Notice - Clean & Minimal */
.ai-seo-important-notice {
    border-left-color: var(--ai-seo-warning-color);
    background-color: var(--ai-seo-warning-light);
    padding: var(--ai-seo-spacing-md);
    border-radius: var(--ai-seo-border-radius);
    margin-bottom: var(--ai-seo-spacing-lg);
}

.ai-seo-important-notice strong:first-child {
    display: block;
    margin-bottom: var(--ai-seo-spacing-sm);
    font-size: 1.1em;
    color: var(--ai-seo-warning-dark);
    font-weight: 600;
}

.ai-seo-important-notice ul {
    margin: 0;
    padding-left: var(--ai-seo-spacing-lg);
    list-style: disc;
}

.ai-seo-important-notice li {
    margin-bottom: var(--ai-seo-spacing-xs);
    line-height: 1.6;
}

.ai-seo-important-notice li strong {
    display: inline;
    font-size: 1em;
    color: var(--ai-seo-warning-dark);
    background-color: rgba(var(--ai-seo-warning-hue), 90%, 50%, 0.1);
    padding: 2px 6px;
    border-radius: var(--ai-seo-border-radius-xs);
    font-weight: 600;
    margin: 0 2px;
}

/* Fade-in animation */
@keyframes ai-seo-fadein {
    from { opacity: 0; transform: translateY(5px); }
    to { opacity: 1; transform: translateY(0); }
}

/* ==========================================================================
   == Phoenix Plugin Header ==
   ========================================================================== */

.ai-seo-header {
    background: var(--ai-seo-bg-gradient-main); /* Use main gradient */
    border: var(--ai-seo-border-width) solid var(--ai-seo-border-color); border-radius: var(--ai-seo-border-radius-xl);
    padding: var(--ai-seo-spacing-lg) var(--ai-seo-spacing-xl); margin-bottom: var(--ai-seo-spacing-lg);
    display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;
    gap: var(--ai-seo-spacing-lg); box-shadow: var(--ai-seo-shadow-lg);
}
.ai-seo-header-title { display: flex; align-items: center; gap: var(--ai-seo-spacing-md); }
.ai-seo-header-title h1 { margin: 0; padding: 0; font-size: 26px; font-weight: 700; line-height: 1.2; letter-spacing: var(--ai-seo-letter-spacing-tight); color: var(--ai-seo-secondary-color); }
.ai-seo-header-icon { font-size: 38px; color: var(--ai-seo-primary-color); line-height: 1; }
.ai-seo-version {
    font-size: 0.9em; color: var(--ai-seo-primary-text); background-color: var(--ai-seo-primary-color);
    padding: 5px 12px; border-radius: var(--ai-seo-border-radius-pill); font-weight: 600;
    vertical-align: middle; margin-left: var(--ai-seo-spacing-sm); box-shadow: 0 1px 3px hsla(var(--ai-seo-primary-hue), 76%, 58%, 0.3);
}
.ai-seo-header-meta { display: flex; align-items: center; gap: var(--ai-seo-spacing-md); flex-wrap: wrap; }
.ai-seo-header-meta .ai-seo-notice { /* Use the general notice pill style */
    padding: 6px 14px; border-radius: var(--ai-seo-border-radius-pill); font-size: 0.9em;
    display: inline-flex; align-items: center; gap: var(--ai-seo-spacing-sm); font-weight: 500;
    box-shadow: var(--ai-seo-shadow-xs);
}
.ai-seo-header-meta .ai-seo-notice a { border-bottom: 1px dotted; color: inherit; font-weight: 600; }
.ai-seo-header-meta .ai-seo-notice a:hover { border-bottom-style: solid; }
.single-file-warning { color: var(--ai-seo-warning-dark); }

/* ==========================================================================
   == Stellar Tabs - Premium Design ==
   ========================================================================== */

.ai-seo-tabs-container {
    margin-top: var(--ai-seo-spacing-xl);
}

.ai-seo-nav-tabs {
    margin-bottom: 0;
    border-bottom: var(--ai-seo-border-width-thick) solid var(--ai-seo-border-color);
    display: flex;
    flex-wrap: wrap;
    gap: var(--ai-seo-spacing-md);
    padding: 0 var(--ai-seo-spacing-md);
    position: relative;
    background: linear-gradient(to bottom, var(--ai-seo-bg-white), var(--ai-seo-bg-subtle));
    border-radius: var(--ai-seo-border-radius-lg) var(--ai-seo-border-radius-lg) 0 0;
    padding: var(--ai-seo-spacing-sm) var(--ai-seo-spacing-md) 0;
}

.ai-seo-nav-tabs .nav-tab {
    background-color: transparent;
    border: var(--ai-seo-border-width) solid transparent;
    border-bottom: none;
    margin: 0;
    margin-bottom: calc(-1 * var(--ai-seo-border-width-thick));
    padding: 16px var(--ai-seo-spacing-xl);
    font-size: 15px;
    font-weight: 600;
    color: var(--ai-seo-text-light);
    text-decoration: none;
    transition: all var(--ai-seo-transition-base);
    display: inline-flex;
    align-items: center;
    gap: var(--ai-seo-spacing-sm);
    position: relative;
    border-radius: var(--ai-seo-border-radius-lg) var(--ai-seo-border-radius-lg) 0 0;
    cursor: pointer;
    border-bottom: 4px solid transparent;
    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.01);
    overflow: hidden;
}

/* Tab shine effect */
.ai-seo-nav-tabs .nav-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    z-index: 1;
    transition: left 0.7s ease;
}

.ai-seo-nav-tabs .nav-tab:hover::before {
    left: 100%;
}

/* Tab icon styling */
.ai-seo-nav-tabs .nav-tab .dashicons {
    font-size: 18px;
    line-height: 1;
    vertical-align: -3px;
    transition: transform 0.3s var(--ai-seo-transition-bounce);
    color: var(--ai-seo-text-lighter);
}

/* Tab hover state */
.ai-seo-nav-tabs .nav-tab:hover {
    color: var(--ai-seo-primary-color);
    background-color: var(--ai-seo-primary-lighter);
    border-bottom-color: var(--ai-seo-primary-border);
    transform: translateY(-2px);
}

.ai-seo-nav-tabs .nav-tab:hover .dashicons {
    transform: translateY(-2px);
    color: var(--ai-seo-primary-color);
}

/* Active tab styling */
.ai-seo-nav-tabs .nav-tab-active,
.ai-seo-nav-tabs .nav-tab-active:hover {
    background-color: var(--ai-seo-bg-white);
    border-color: var(--ai-seo-border-color) var(--ai-seo-border-color) transparent;
    color: var(--ai-seo-primary-color);
    font-weight: 700;
    position: relative;
    z-index: 1;
    border-bottom-color: var(--ai-seo-primary-color);
    box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.05);
    transform: translateY(-3px);
}

.ai-seo-nav-tabs .nav-tab-active .dashicons,
.ai-seo-nav-tabs .nav-tab-active:hover .dashicons {
    color: var(--ai-seo-primary-color);
}

/* Active tab indicator */
.ai-seo-nav-tabs .nav-tab-active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--ai-seo-gradient-primary);
    border-radius: var(--ai-seo-border-radius-pill) var(--ai-seo-border-radius-pill) 0 0;
    box-shadow: 0 -2px 5px rgba(var(--ai-seo-primary-hue), var(--ai-seo-primary-saturation), var(--ai-seo-primary-lightness), 0.2);
}

/* Count Bubble - Premium Design */
.ai-seo-count-bubble {
    background: var(--ai-seo-gradient-error);
    color: var(--ai-seo-primary-text);
    font-size: 11px;
    line-height: 1;
    font-weight: 700;
    padding: 5px 10px;
    margin-left: var(--ai-seo-spacing-sm);
    border-radius: var(--ai-seo-border-radius-pill);
    vertical-align: middle;
    min-width: 24px;
    text-align: center;
    box-shadow: 0 2px 5px rgba(var(--ai-seo-error-hue), 85%, 55%, 0.3);
    transition: var(--ai-seo-transition-base);
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.ai-seo-count-bubble.count-0 {
    display: none;
}

.nav-tab:hover .ai-seo-count-bubble {
    background: linear-gradient(135deg,
        hsl(var(--ai-seo-error-hue), 85%, 55%) 0%,
        hsl(var(--ai-seo-error-hue), 90%, 45%) 100%);
    transform: scale(1.05);
}

/* Tab Content Animation */
.ai-seo-tabs-container > .tab-content {
    display: none;
}

.ai-seo-tabs-container > .tab-content.active {
    display: block;
    animation: ai-seo-fadein 0.5s ease-out;
}

/* Button Pulse Animation for Auto-Select & Apply */
@keyframes ai-seo-pulse-button {
    0% { transform: scale(1.05); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1.05); }
}
.ai-seo-pulse-button {
    animation: ai-seo-pulse-button 1.5s ease-in-out infinite;
}

/* Auto-select button styling */
#ai-seo-auto-select-better {
    background-color: #2ecc71;
    border-color: #27ae60;
    color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    font-weight: bold;
    position: relative;
    overflow: hidden;
}

#ai-seo-auto-select-better:hover {
    background-color: #27ae60;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    transform: translateY(-1px);
}

#ai-seo-auto-select-better:active::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    animation: ripple 0.6s linear;
}

/* Highlight posts with better scores */
.ai-seo-review-item.has-better-scores {
    border-left: 4px solid #2ecc71;
    box-shadow: 0 0 10px rgba(46, 204, 113, 0.3);
    transition: all 0.3s ease;
}

.ai-seo-review-item.has-better-scores .ai-seo-suggestion-section input[type="checkbox"]:checked {
    border-color: #2ecc71;
    background-color: #2ecc71;
}

@keyframes ripple {
    to {
        transform: translate(-50%, -50%) scale(4);
        opacity: 0;
    }
}

/* Mobile-specific styles for auto-select and apply buttons */
@media screen and (max-width: 782px) {
    #ai-seo-auto-select-better,
    #ai-seo-apply-all-visible {
        width: 100%;
        justify-content: center;
        padding: 12px 16px;
        margin-bottom: var(--ai-seo-spacing-sm);
    }

    /* Make the pulse animation more visible on mobile */
    @keyframes ai-seo-pulse-button-mobile {
        0% { transform: scale(1.02); box-shadow: 0 0 10px rgba(46, 204, 113, 0.6); }
        50% { transform: scale(1.05); box-shadow: 0 0 15px rgba(46, 204, 113, 0.8); }
        100% { transform: scale(1.02); box-shadow: 0 0 10px rgba(46, 204, 113, 0.6); }
    }

    .ai-seo-pulse-button {
        animation: ai-seo-pulse-button-mobile 1.5s ease-in-out infinite;
    }

    /* Improve Manual Process list on mobile */
    .ai-seo-post-list li {
        padding: 12px 15px;
        grid-template-columns: auto 1fr;
        grid-template-rows: auto auto;
        gap: 8px;
        margin-bottom: 6px;
    }

    .ai-seo-post-list li input[type="checkbox"] {
        grid-column: 1;
        grid-row: 1;
        width: 18px;
        height: 18px;
        min-width: 18px;
        min-height: 18px;
    }

    .ai-seo-post-list li input[type="checkbox"]:checked::after {
        left: 5px;
        top: 2px;
        width: 4px;
        height: 8px;
    }

    .ai-seo-post-list li label {
        grid-column: 2;
        grid-row: 1;
        padding: 0;
    }

    .ai-seo-post-list .post-title-id {
        font-size: 13px;
        font-weight: 600;
    }

    .seo-scores {
        grid-column: 1 / span 2;
        grid-row: 2;
        width: 100%;
        height: 30px;
        margin-left: 28px;
        margin-top: 4px;
        border-radius: 4px;
    }

    .seo-scores strong {
        font-size: 13px;
    }

    .seo-scores strong::before {
        font-size: 14px;
        margin-right: 4px;
    }
}

/* ==========================================================================
   == Phoenix Settings Tab ==
   ========================================================================== */

#tab-settings h2:first-of-type { margin-top: 0; }
#tab-settings .form-table { margin-top: 0; width: 100%; border-collapse: separate; border-spacing: 0 var(--ai-seo-spacing-md); } /* Use spacing */
#tab-settings .form-table th {
    padding: var(--ai-seo-spacing-md) var(--ai-seo-spacing-xl) var(--ai-seo-spacing-md) 0;
    width: 280px; vertical-align: top; text-align: left; font-weight: 500; /* Less heavy */
    color: var(--ai-seo-text-color); font-size: 14px; line-height: 1.5;
}
#tab-settings .form-table td { padding: var(--ai-seo-spacing-md) var(--ai-seo-spacing-lg); vertical-align: top; /* Align top with TH */ }
#tab-settings .form-table tr { border-bottom: none; } /* Remove bottom border */
#tab-settings .form-table td > *:first-child { margin-top: 0; } /* Remove top margin from first element in TD */
#tab-settings .form-table input[type="text"], #tab-settings .form-table input[type="password"],
#tab-settings .form-table input[type="url"], #tab-settings .form-table select,
#tab-settings .form-table textarea, #tab-settings .form-table #custom-model-input {
    width: 100%; max-width: 600px; padding: 12px 16px; /* More padding */
    border: var(--ai-seo-border-width) solid var(--ai-seo-border-input-color);
    border-radius: var(--ai-seo-border-radius); transition: var(--ai-seo-transition-base);
    box-shadow: var(--ai-seo-shadow-inner); font-size: 14px; background-color: var(--ai-seo-bg-white); color: var(--ai-seo-text-color);
}
#tab-settings .form-table select { padding-right: 35px; background-position: right 10px center; }
#tab-settings .form-table input:focus, #tab-settings .form-table select:focus,
#tab-settings .form-table textarea:focus, #tab-settings .form-table #custom-model-input:focus {
    border-color: var(--ai-seo-border-focus-color); box-shadow: var(--ai-seo-shadow-inner), var(--ai-seo-shadow-focus-ring); outline: none;
}
#tab-settings .form-table textarea.large-text { max-width: 750px; min-height: 140px; }
#tab-settings .form-table code { font-size: 0.95em; padding: 3px 7px; }
#tab-settings fieldset label { display: flex; align-items: center; margin-bottom: var(--ai-seo-spacing-sm); gap: var(--ai-seo-spacing-sm); cursor: pointer; font-size: 14px; }
#tab-settings fieldset input[type="checkbox"] { margin: 0; width: 16px; height: 16px; accent-color: var(--ai-seo-primary-color); }
#tab-settings ul { list-style: none; margin: var(--ai-seo-spacing-sm) 0 var(--ai-seo-spacing-md) 0; padding-left: 0; }
#tab-settings ul li { margin-bottom: var(--ai-seo-spacing-sm); line-height: 1.7; position: relative; padding-left: var(--ai-seo-spacing-lg); }
#tab-settings ul li::before {
    content: "\f147"; /* Checkmark */ font-family: dashicons; color: var(--ai-seo-primary-color);
    position: absolute; left: 0; top: 4px; font-size: 16px;
}
#tab-settings .ai-seo-test-connection-container { margin-top: var(--ai-seo-spacing-lg); display: flex; align-items: center; gap: var(--ai-seo-spacing-md); flex-wrap: wrap; }
#tab-settings .ai-seo-test-connection-container .button .dashicons { color: var(--ai-seo-info-color); vertical-align: middle; }
#tab-settings #ai-seo-test-connection-result { /* Use ajax result styles */ flex-grow: 1; min-width: 150px; }
/* Section Title Styling */
#tab-settings .ai-seo-section-title {
    font-size: 1.4em; color: var(--ai-seo-secondary-color); font-weight: 600;
    border-bottom: var(--ai-seo-border-width-thick) solid var(--ai-seo-primary-color);
    padding-bottom: var(--ai-seo-spacing-sm); margin-bottom: var(--ai-seo-spacing-lg);
    margin-top: var(--ai-seo-spacing-xl); display: flex; align-items: center; gap: var(--ai-seo-spacing-sm);
}
#tab-settings .ai-seo-section-title .dashicons { font-size: 24px; color: var(--ai-seo-primary-color); }
#tab-settings #openrouter-model-container, #tab-settings #custom-model-container {
    margin-top: var(--ai-seo-spacing-md); padding-left: var(--ai-seo-spacing-md);
    border-left: 3px solid var(--ai-seo-border-light-color); transition: var(--ai-seo-transition-base);
}
#tab-settings #openrouter-model-container:has(#custom-model-container:visible) { border-left-color: var(--ai-seo-primary-border); }

/* ==========================================================================
   == Manual / Bulk / Status / Review - Shared Controls & Lists ==
   ========================================================================== */

/* Controls Box */
.ai-seo-controls-box {
    background-color: var(--ai-seo-bg-subtle); border: var(--ai-seo-border-width) solid var(--ai-seo-border-light-color);
    border-radius: var(--ai-seo-border-radius-lg); padding: var(--ai-seo-spacing-lg);
    margin-bottom: var(--ai-seo-spacing-lg); display: flex; flex-wrap: wrap;
    align-items: center; gap: var(--ai-seo-spacing-md); box-shadow: var(--ai-seo-shadow-inner);
}
#ai-seo-manual-controls input[type="search"] {
    padding: 10px 16px; border: var(--ai-seo-border-width) solid var(--ai-seo-border-input-color);
    border-radius: var(--ai-seo-border-radius); min-width: 280px; flex-grow: 1;
    transition: var(--ai-seo-transition-base); font-size: 14px; box-shadow: var(--ai-seo-shadow-inner);
}
#ai-seo-manual-controls input[type="search"]:focus { border-color: var(--ai-seo-border-focus-color); box-shadow: var(--ai-seo-shadow-inner), var(--ai-seo-shadow-focus-ring); outline: none; }
#ai-seo-manual-controls > div { display: flex; align-items: center; gap: var(--ai-seo-spacing-md); }

/* Lists Container */
.ai-seo-list-container {
    margin-top: var(--ai-seo-spacing-lg);
    border: none;
    background-color: var(--ai-seo-bg-white);
    border-radius: var(--ai-seo-border-radius-xl);
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    position: relative;
}

.ai-seo-list-container::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--ai-seo-primary-color) 0%, var(--ai-seo-accent-color) 100%);
    z-index: 1;
}

.ai-seo-list-container .select-all-controls, .ai-seo-pagination {
    padding: var(--ai-seo-spacing-md) var(--ai-seo-spacing-xl);
    background: linear-gradient(135deg, var(--ai-seo-bg-white) 0%, var(--ai-seo-bg-subtle) 100%);
    border-bottom: var(--ai-seo-border-width) solid var(--ai-seo-border-color);
    font-size: 0.95em;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.ai-seo-pagination {
    border-top: var(--ai-seo-border-width) solid var(--ai-seo-border-color);
    border-bottom: none;
    background: linear-gradient(135deg, var(--ai-seo-bg-subtle) 0%, var(--ai-seo-bg-white) 100%);
}

.ai-seo-list-container .select-all-controls label {
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--ai-seo-spacing-sm);
    cursor: pointer;
    color: var(--ai-seo-secondary-color);
}

.ai-seo-list-container .select-all-controls input[type="checkbox"] {
    margin: 0;
    width: 20px;
    height: 20px;
    min-width: 20px;
    min-height: 20px;
    accent-color: var(--ai-seo-primary-color);
    cursor: pointer;
    position: relative;
    border: 2px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    background-color: white;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    outline: none;
}

.ai-seo-list-container .select-all-controls input[type="checkbox"]:checked {
    background-color: var(--ai-seo-primary-color);
    border-color: var(--ai-seo-primary-color);
}

.ai-seo-list-container .select-all-controls input[type="checkbox"]:checked::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.ai-seo-list-container .select-all-controls input[type="checkbox"]:hover {
    border-color: var(--ai-seo-primary-color);
    box-shadow: 0 0 0 3px rgba(var(--ai-seo-primary-hue), var(--ai-seo-primary-saturation), var(--ai-seo-primary-lightness), 0.2);
}

/* Generic Post/Item List UL */
.ai-seo-post-list, .ai-seo-failed-list {
    list-style: none;
    margin: 0;
    padding: 0;
    max-height: 65vh;
    overflow-y: auto;
}

.ai-seo-post-list li, .ai-seo-failed-item {
    padding: 16px 20px;
    margin-bottom: 8px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    display: grid;
    grid-template-columns: 32px 1fr auto;
    align-items: center;
    gap: 12px;
    transition: all 0.25s ease;
    position: relative;
    background-color: var(--ai-seo-bg-white);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.ai-seo-post-list li:last-child, .ai-seo-failed-list .ai-seo-failed-item:last-child {
    border-bottom: none;
}

.ai-seo-post-list li:hover {
    background-color: var(--ai-seo-bg-subtle);
    transform: translateX(2px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
    border-color: var(--ai-seo-primary-lighter);
}

.ai-seo-post-list li::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--ai-seo-primary-color);
    border-radius: 8px 0 0 8px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.ai-seo-post-list li:hover::before {
    opacity: 1;
}

/* Manual List Specifics */
.ai-seo-post-list li label {
    grid-column: 2;
    display: flex;
    align-items: center;
    cursor: pointer;
    width: 100%;
    min-width: 0;
    padding: 0;
    position: relative;
}

.ai-seo-post-list .post-title-id {
    color: var(--ai-seo-text-color);
    font-size: 14px;
    font-weight: 600;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    transition: color 0.2s ease;
    padding-left: 0;
    position: relative;
}

.ai-seo-post-list .post-title-id:hover {
    color: var(--ai-seo-primary-color);
}

.ai-seo-post-list li input[type="checkbox"] {
    grid-column: 1;
    margin: 0;
    width: 20px;
    height: 20px;
    min-width: 20px;
    min-height: 20px;
    cursor: pointer;
    position: relative;
    border: 2px solid var(--ai-seo-border-light-color);
    border-radius: 4px;
    background-color: white;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
    outline: none;
}

.ai-seo-post-list li input[type="checkbox"]:checked {
    background-color: var(--ai-seo-primary-color);
    border-color: var(--ai-seo-primary-color);
}

.ai-seo-post-list li input[type="checkbox"]:checked::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 4px;
    height: 9px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.ai-seo-post-list li input[type="checkbox"]:hover {
    border-color: var(--ai-seo-primary-color);
    box-shadow: 0 0 0 2px rgba(var(--ai-seo-primary-hue), var(--ai-seo-primary-saturation), var(--ai-seo-primary-lightness), 0.15);
}

/* SEO Scores (Manual List) */
.seo-scores {
    grid-column: 3;
    font-size: 0.85em;
    padding: 0;
    border-radius: 4px;
    background-color: var(--ai-seo-bg-white);
    border: 1px solid var(--ai-seo-border-light-color);
    color: var(--ai-seo-text-dark);
    min-width: 90px;
    text-align: center;
    white-space: nowrap;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    height: 28px;
}

.seo-scores:hover {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
    border-color: var(--ai-seo-primary-lighter);
}

.seo-scores strong {
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    text-align: center;
    margin: 0;
    padding: 0 8px;
    font-size: 13px;
    line-height: 1;
    border: none;
    position: relative;
    transition: all 0.2s ease;
    z-index: 1;
}

.seo-scores strong::before {
    font-family: dashicons;
    font-size: 14px;
    line-height: 1;
    margin-right: 4px;
    opacity: 0.9;
    position: relative;
    top: 1px;
}

.seo-scores .score-poor {
    color: #fff;
    background-color: #e74c3c;
}

.seo-scores .score-poor::before {
    content: "\f158"; /* Dashicons: no */
}

.seo-scores .score-fair {
    color: #fff;
    background-color: #f39c12;
}

.seo-scores .score-fair::before {
    content: "\f460"; /* Dashicons: warning */
}

.seo-scores .score-good {
    color: #fff;
    background-color: #3498db;
}

.seo-scores .score-good::before {
    content: "\f147"; /* Dashicons: yes */
}

.seo-scores .score-excellent {
    color: #fff;
    background-color: #2ecc71;
}

.seo-scores .score-excellent::before {
    content: "\f155"; /* Dashicons: star-filled */
}

.score-loading {
    font-style: italic;
    color: var(--ai-seo-text-lightest);
    opacity: 0.8;
    padding: 0 10px;
}

.score-error {
    color: var(--ai-seo-error-dark);
    font-weight: 600;
    padding: 0 10px;
}

.seo-scores [title] {
    cursor: help;
}

/* Pagination */
.ai-seo-pagination .button { padding: 8px 14px; font-size: 13px; background: var(--ai-seo-bg-white); box-shadow: var(--ai-seo-shadow-sm); }
.ai-seo-pagination .button:hover { background-color: var(--ai-seo-bg-subtle); transform: translateY(-1px); }
.ai-seo-pagination .button:disabled { background: var(--ai-seo-bg-alt); transform: none; box-shadow: none; opacity: 0.6; }
.ai-seo-pagination .ai-seo-page-info { font-size: 1em; color: var(--ai-seo-text-light); margin: 0 var(--ai-seo-spacing-md); font-weight: 500; }

/* Log Box - Dark Theme */
.ai-seo-log-container { margin-top: var(--ai-seo-spacing-lg); }
.ai-seo-log-container h3 { margin-bottom: var(--ai-seo-spacing-sm); font-size: 1.1em; color: var(--ai-seo-secondary-light); }
.ai-seo-log-box {
    background-color: var(--ai-seo-bg-dark); color: #abb2bf; border: var(--ai-seo-border-width) solid #323842;
    border-radius: var(--ai-seo-border-radius-lg); padding: var(--ai-seo-spacing-lg);
    font-family: var(--ai-seo-font-monospace); font-size: 13px; line-height: 1.8;
    white-space: pre-wrap; word-wrap: break-word; max-height: 450px; overflow-y: auto;
    margin-top: var(--ai-seo-spacing-sm); box-shadow: var(--ai-seo-shadow-lg);
}
.processing-log-entry { display: block; margin-bottom: 6px; animation: ai-seo-fadein 0.3s ease-out; }
.log-timestamp { color: #61afef; margin-right: var(--ai-seo-spacing-sm); } /* Blue */
.log-info { color: #abb2bf; } /* Default */
.log-success { color: #98c379; } /* Green */
.log-warning { color: #e5c07b; } /* Yellow */
.log-error { color: #e06c75; font-weight: bold; } /* Red */
.log-debug { color: #5c6370; font-style: italic; opacity: 0.8; } /* Gray */
.log-skipped { color: #d19a66; } /* Orange */

/* ==========================================================================
   == Phoenix Status Tab - Animated Cards & Progress ==
   ========================================================================== */

.ai-seo-status-box h3 .dashicons { font-size: 22px; color: var(--ai-seo-secondary-light); }

/* Stats Grid - Animated Cards */
#ai-seo-status-summary {
    background-color: transparent; padding: 0; border: none;
    display: grid; grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: var(--ai-seo-spacing-lg); text-align: center;
}
#ai-seo-status-summary > span { /* Stat card */
    display: flex; flex-direction: column; align-items: center; justify-content: center;
    background-color: var(--ai-seo-bg-white); padding: var(--ai-seo-spacing-lg);
    border-radius: var(--ai-seo-border-radius-lg); border: var(--ai-seo-border-width) solid var(--ai-seo-border-color);
    transition: var(--ai-seo-transition-base); box-shadow: var(--ai-seo-shadow-md);
    min-height: 120px; position: relative; overflow: hidden;
}
#ai-seo-status-summary > span::before { /* Icon pseudo-element */
    font-family: dashicons; content: ''; font-size: 28px; /* Larger icon */ opacity: 0.1;
    position: absolute; bottom: var(--ai-seo-spacing-sm); right: var(--ai-seo-spacing-md);
    transition: all var(--ai-seo-transition-fast); color: var(--ai-seo-text-lightest);
    transform: scale(1);
}
#ai-seo-status-summary > span:hover {
    transform: translateY(-5px) scale(1.03); box-shadow: var(--ai-seo-shadow-xl);
    border-color: var(--ai-seo-primary-border); z-index: 5;
    background-color: var(--ai-seo-bg-glass); backdrop-filter: var(--ai-seo-backdrop-blur);
}
#ai-seo-status-summary > span:hover::before { opacity: 0.2; transform: scale(1.1) rotate(-5deg); }
/* Icons for each status */
#ai-seo-status-summary span[data-status="pending"]::before { content: "\f139"; color: var(--ai-seo-info-color); }
#ai-seo-status-summary span[data-status="in_progress"]::before { content: "\f469"; color: var(--ai-seo-warning-color); }
#ai-seo-status-summary span[data-status="complete"]::before { content: "\f147"; color: var(--ai-seo-success-color); }
#ai-seo-status-summary span[data-status="failed"]::before { content: "\f158"; color: var(--ai-seo-error-color); }
#ai-seo-status-summary span[data-status="cancelled"]::before { content: "\f153"; color: var(--ai-seo-text-lighter); }
#ai-seo-status-summary span[data-status="total"]::before { content: "\f228"; color: var(--ai-seo-secondary-lighter); }

#ai-seo-status-summary strong { /* Label */
    font-weight: 600; color: var(--ai-seo-text-light); font-size: 0.9em;
    margin-bottom: var(--ai-seo-spacing-sm); text-transform: uppercase; letter-spacing: 0.5px;
}
#ai-seo-status-summary span span { /* Count */
    font-weight: 700; font-size: 2.6em; /* Slightly larger */ line-height: 1.1;
    transition: color var(--ai-seo-transition-fast); z-index: 1; position: relative;
}
/* Color coding counts */
#ai-seo-status-summary span[data-status="failed"] span { color: var(--ai-seo-error-color); }
#ai-seo-status-summary span[data-status="pending"] span { color: var(--ai-seo-info-color); }
#ai-seo-status-summary span[data-status="in_progress"] span { color: var(--ai-seo-warning-color); }
#ai-seo-status-summary span[data-status="complete"] span { color: var(--ai-seo-success-color); }
#ai-seo-status-summary span[data-status="cancelled"] span { color: var(--ai-seo-text-lighter); }
#ai-seo-status-summary span[data-status="total"] span { color: var(--ai-seo-secondary-color); }

/* Progress Bar - Animated */
#ai-seo-progress-bar {
    background-color: var(--ai-seo-bg-alt); border-radius: var(--ai-seo-border-radius-pill);
    height: 16px; margin: var(--ai-seo-spacing-xl) 0;
    overflow: hidden; position: relative; box-shadow: var(--ai-seo-shadow-inner);
}
#ai-seo-progress-bar-inner {
    background-color: var(--ai-seo-success-color);
    background-image: linear-gradient(45deg, hsla(0, 0%, 100%, 0.15) 25%, transparent 25%, transparent 50%, hsla(0, 0%, 100%, 0.15) 50%, hsla(0, 0%, 100%, 0.15) 75%, transparent 75%, transparent);
    background-size: 40px 40px; height: 100%; width: 0; color: var(--ai-seo-primary-text); font-weight: 600;
    text-align: center; line-height: 16px; font-size: 11px;
    transition: width 0.6s cubic-bezier(0.65, 0, 0.35, 1); white-space: nowrap; overflow: hidden;
    border-radius: var(--ai-seo-border-radius-pill); box-shadow: inset 0 -1px 1px hsla(0, 0%, 0%, 0.15);
    text-shadow: 0 1px 1px hsla(0, 0%, 0%, 0.2); animation: ai-seo-progress-bar-stripes 2s linear infinite;
}
#ai-seo-progress-bar[title] { cursor: help; }
@keyframes ai-seo-progress-bar-stripes { from { background-position: 40px 0; } to { background-position: 0 0; } }

/* Action Scheduler Links */
.ai-seo-as-links { margin-top: var(--ai-seo-spacing-lg); font-size: 0.95em; color: var(--ai-seo-text-light); text-align: center; }
.ai-seo-as-links strong { color: var(--ai-seo-text-color); margin-right: var(--ai-seo-spacing-sm); }
.ai-seo-as-links a {
    margin: 0 var(--ai-seo-spacing-sm); padding: 4px 8px; border-bottom: 1px solid transparent;
    font-weight: 500; border-radius: var(--ai-seo-border-radius-sm); transition: var(--ai-seo-transition-base);
}
.ai-seo-as-links a:hover {
    border-bottom-color: var(--ai-seo-link-hover-color); color: var(--ai-seo-link-hover-color);
    background-color: var(--ai-seo-primary-lighter); text-decoration: none;
}

/* Failed Job List - Card Style */
#ai-seo-failed-jobs-container { margin-top: var(--ai-seo-spacing-xl); }
#ai-seo-failed-jobs-list {
    margin-top: var(--ai-seo-spacing-sm); max-height: 60vh;
    display: flex; flex-direction: column; gap: var(--ai-seo-spacing-lg); padding-right: var(--ai-seo-spacing-sm);
}
.ai-seo-failed-list { border: none; }
.ai-seo-failed-item { /* Treat as a card */
    border: var(--ai-seo-border-width) solid var(--ai-seo-error-border); border-left: 5px solid var(--ai-seo-error-color);
    border-radius: var(--ai-seo-border-radius-lg); background-color: var(--ai-seo-bg-white);
    margin-bottom: 0; padding: var(--ai-seo-spacing-lg); display: flex; flex-direction: column;
    gap: var(--ai-seo-spacing-md); box-shadow: var(--ai-seo-shadow-md); transition: var(--ai-seo-transition-base);
}
.ai-seo-failed-item:hover {
    border-color: var(--ai-seo-error-dark); box-shadow: var(--ai-seo-shadow-lg);
    transform: translateY(-3px); background-image: var(--ai-seo-bg-gradient-card-hover);
}
.failed-item-header { display: flex; align-items: center; gap: var(--ai-seo-spacing-md); flex-wrap: wrap; }
.failed-item-header .post-id {
    font-size: 0.85em; color: var(--ai-seo-text-light); background-color: var(--ai-seo-bg-alt); padding: 4px 10px;
    border-radius: var(--ai-seo-border-radius-pill); cursor: help; border: var(--ai-seo-border-width) solid var(--ai-seo-border-light-color);
    font-weight: 600; flex-shrink: 0;
}
.failed-item-header strong.failed-item-title { flex-grow: 1; font-size: 1.1em; line-height: 1.4; }
.failed-item-header strong.failed-item-title a { font-weight: 600; color: var(--ai-seo-secondary-color); }
.failed-item-header strong.failed-item-title a:hover { color: var(--ai-seo-link-hover-color); }
.failed-item-header .edit-link { color: var(--ai-seo-text-lightest); display: inline-flex; align-items: center; justify-content: center; width: 28px; height: 28px; border-radius: 50%; transition: var(--ai-seo-transition-fast); }
.failed-item-header .edit-link .dashicons { font-size: 18px; }
.failed-item-header .edit-link:hover { background-color: var(--ai-seo-primary-lighter); color: var(--ai-seo-link-hover-color); }
.failed-item-error {
    font-size: 1em; display: flex; align-items: flex-start; gap: var(--ai-seo-spacing-sm);
    color: var(--ai-seo-error-dark); background-color: var(--ai-seo-error-bg);
    padding: var(--ai-seo-spacing-md); border-radius: var(--ai-seo-border-radius);
    border: var(--ai-seo-border-width) solid var(--ai-seo-error-border);
}
.failed-item-error .error-icon { margin-top: 2px; font-size: 18px; flex-shrink: 0; color: var(--ai-seo-error-color); }
.failed-item-error .error-message {
    font-family: var(--ai-seo-font-monospace); background-color: transparent; padding: 0; border: none;
    color: inherit; word-break: break-word; font-size: 0.9em; line-height: 1.6;
}

/* ==========================================================================
   == Zenith Review Tab - Professional Edition ==
   ========================================================================== */

/* Controls */
.ai-seo-review-controls {
    justify-content: flex-start;
    flex-wrap: wrap;
    padding: var(--ai-seo-spacing-lg) var(--ai-seo-spacing-xl);
    background: linear-gradient(135deg, var(--ai-seo-bg-white) 0%, var(--ai-seo-bg-subtle) 100%);
    border-radius: var(--ai-seo-border-radius-lg);
    margin-bottom: var(--ai-seo-spacing-xl);
    box-shadow: var(--ai-seo-shadow-md);
    border: 1px solid var(--ai-seo-border-color);
    position: relative;
    overflow: hidden;
}

.ai-seo-review-controls::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--ai-seo-primary-color) 0%, var(--ai-seo-accent-color) 100%);
}

.ai-seo-review-controls .button {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    z-index: 1;
}

.ai-seo-review-controls .button::after {
    content: "";
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--ai-seo-primary-color) 0%, var(--ai-seo-accent-color) 100%);
    opacity: 0;
    transition: all 0.3s ease;
}

.ai-seo-review-controls .button:hover::after {
    bottom: 0;
    opacity: 1;
}

#ai-seo-global-status {
    margin-left: auto;
    font-weight: 700;
    color: var(--ai-seo-primary-color);
    background-color: var(--ai-seo-bg-white);
    padding: 8px 16px;
    border-radius: var(--ai-seo-border-radius);
    box-shadow: var(--ai-seo-shadow-sm);
    border: 1px solid var(--ai-seo-border-light-color);
}

/* Empty/Loading States */
#ai-seo-review-list-container.ai-seo-box {
    padding: 0;
    background-color: transparent;
    border: none;
    box-shadow: none;
}

.ai-seo-review-items {
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: var(--ai-seo-spacing-xl);
}

.ai-seo-review-summary, .ai-seo-review-footer-notice {
    padding: var(--ai-seo-spacing-md) var(--ai-seo-spacing-xl);
    font-style: normal;
    color: var(--ai-seo-text-lighter);
    font-size: 0.95em;
    background-color: var(--ai-seo-bg-white);
    border-radius: var(--ai-seo-border-radius);
    box-shadow: var(--ai-seo-shadow-sm);
    border: 1px solid var(--ai-seo-border-light-color);
}

.ai-seo-no-review-items {
    border-left: 4px solid var(--ai-seo-success-color);
    background-color: var(--ai-seo-success-bg);
    color: var(--ai-seo-success-dark);
    border-color: var(--ai-seo-success-border);
    padding: var(--ai-seo-spacing-lg);
    border-radius: var(--ai-seo-border-radius);
    box-shadow: var(--ai-seo-shadow-md);
    display: flex;
    align-items: center;
    gap: var(--ai-seo-spacing-md);
    font-weight: 500;
    font-size: 1.1em;
}

.ai-seo-no-review-items .dashicons {
    color: var(--ai-seo-success-color);
    font-size: 24px;
}

/* === Review Item Card === */
.ai-seo-review-item {
    border: none;
    border-radius: var(--ai-seo-border-radius-xl);
    margin: 0 0 var(--ai-seo-spacing-xl) 0;
    background-color: var(--ai-seo-bg-white);
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    position: relative;
    display: flex;
    flex-direction: column;
}

.ai-seo-review-item:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
    transform: translateY(-5px);
    z-index: 10;
}

.ai-seo-review-item.processing {
    opacity: 0.6;
    transform: none !important;
    box-shadow: var(--ai-seo-shadow-md) !important;
    filter: grayscale(40%);
    cursor: progress;
}

.ai-seo-review-item.processing::after {
    content: '';
    position: absolute;
    inset: 0;
    background: hsla(0, 0%, 100%, 0.5);
    z-index: 100;
    pointer-events: none;
}

/* Status Indicator (Top Border) */
.ai-seo-review-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, var(--ai-seo-primary-color) 0%, var(--ai-seo-accent-color) 100%);
    transition: all 0.3s ease;
    border-radius: var(--ai-seo-border-radius-xl) var(--ai-seo-border-radius-xl) 0 0;
}

.ai-seo-review-item.needs-review::before {
    background: linear-gradient(90deg, var(--ai-seo-warning-color) 0%, var(--ai-seo-warning-dark) 100%);
    height: 8px;
}

.ai-seo-review-item:not(.needs-review)::before {
    background: linear-gradient(90deg, var(--ai-seo-success-color) 0%, var(--ai-seo-success-dark) 100%);
}

.ai-seo-review-item.processing-error::before {
    background: linear-gradient(90deg, var(--ai-seo-error-color) 0%, var(--ai-seo-error-dark) 100%);
    height: 8px;
}

.ai-seo-review-item.processing-success::before {
    background: linear-gradient(90deg, var(--ai-seo-success-color) 0%, var(--ai-seo-success-dark) 100%);
}

/* Review Item Header */
.ai-seo-review-item-header {
    display: grid;
    grid-template-columns: 1fr auto;
    align-items: center;
    gap: var(--ai-seo-spacing-lg);
    padding: var(--ai-seo-spacing-lg) var(--ai-seo-spacing-xl);
    border-bottom: 1px solid var(--ai-seo-border-light-color);
    background: linear-gradient(135deg, var(--ai-seo-bg-white) 0%, var(--ai-seo-bg-subtle) 100%);
    position: relative;
}

.ai-seo-review-status-icon {
    font-size: 22px;
    line-height: 1;
    margin-right: var(--ai-seo-spacing-sm);
    vertical-align: -3px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.7);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.needs-review .ai-seo-review-status-icon {
    color: var(--ai-seo-warning-color);
    background-color: var(--ai-seo-warning-bg);
}

.no-review-needed .ai-seo-review-status-icon,
.processing-success .ai-seo-review-status-icon {
    color: var(--ai-seo-success-color);
    background-color: var(--ai-seo-success-bg);
}

.processing-error .ai-seo-review-status-icon {
    color: var(--ai-seo-error-color);
    background-color: var(--ai-seo-error-bg);
}

.ai-seo-review-item-title-meta {
    grid-column: 1;
}

.ai-seo-review-item-title {
    margin: 0 0 8px 0;
    font-size: 1.3em;
    font-weight: 700;
    display: flex;
    align-items: center;
    line-height: 1.4;
    color: var(--ai-seo-secondary-color);
}

.ai-seo-review-item-title a {
    color: var(--ai-seo-secondary-color);
    transition: all 0.2s ease;
    text-decoration: none;
    border-bottom: 2px solid transparent;
    padding-bottom: 2px;
}

.ai-seo-review-item-title a:hover {
    color: var(--ai-seo-primary-color);
    border-bottom-color: var(--ai-seo-primary-color);
}

.ai-seo-review-item-title .post-meta-info {
    font-size: 0.8em;
    font-weight: 600;
    color: var(--ai-seo-text-lighter);
    display: inline-flex;
    gap: var(--ai-seo-spacing-xs);
    white-space: nowrap;
    margin-left: var(--ai-seo-spacing-md);
    background-color: var(--ai-seo-primary-lighter);
    color: var(--ai-seo-primary-dark);
    padding: 4px 12px;
    border-radius: var(--ai-seo-border-radius-pill);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--ai-seo-primary-border);
}

.ai-seo-item-actions {
    grid-column: 2;
    display: flex;
    align-items: center;
    gap: var(--ai-seo-spacing-md);
}

.ai-seo-item-actions .button {
    padding: 8px 16px;
    font-size: 14px;
    min-width: 100px;
    text-align: center;
    justify-content: center;
    font-weight: 600;
    border-radius: var(--ai-seo-border-radius);
    transition: all 0.2s ease;
}

.ai-seo-item-actions .button-primary {
    background: linear-gradient(135deg, var(--ai-seo-success-color) 0%, var(--ai-seo-success-dark) 100%);
    border: none;
    color: white;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.ai-seo-item-actions .button-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.ai-seo-item-actions .ai-seo-dismiss-single-btn {
    background-color: var(--ai-seo-bg-white);
    border: 1px solid var(--ai-seo-border-color);
    color: var(--ai-seo-error-color);
}

.ai-seo-item-actions .ai-seo-dismiss-single-btn:hover:not(:disabled) {
    background-color: var(--ai-seo-error-bg);
    border-color: var(--ai-seo-error-border);
    transform: translateY(-2px);
}

.ai-seo-item-actions .spinner {
    margin-left: 0;
    width: 18px;
    height: 18px;
    margin-top: -2px;
    border-top-color: var(--ai-seo-primary-color);
}

/* Review Item Body */
.ai-seo-review-item-body {
    padding: var(--ai-seo-spacing-xl);
    display: flex;
    flex-direction: column;
    gap: var(--ai-seo-spacing-xl);
    background-color: var(--ai-seo-bg-white);
}

.ai-seo-already-optimized-msg {
    border-left: 4px solid var(--ai-seo-success-color);
    background-color: var(--ai-seo-success-bg);
    color: var(--ai-seo-success-dark);
    border: 1px solid var(--ai-seo-success-border);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    padding: var(--ai-seo-spacing-lg);
    border-radius: var(--ai-seo-border-radius);
    display: flex;
    align-items: center;
    gap: var(--ai-seo-spacing-md);
    font-weight: 500;
    font-size: 1.05em;
}

.ai-seo-already-optimized-msg .dashicons {
    font-size: 24px;
    color: var(--ai-seo-success-color);
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* --- Comparison Section Styling --- */
.ai-seo-comparison-section {
    display: grid;
    grid-template-columns: minmax(180px, max-content) 1fr;
    gap: var(--ai-seo-spacing-lg) var(--ai-seo-spacing-xl);
    align-items: start;
    padding: var(--ai-seo-spacing-lg);
    margin-bottom: var(--ai-seo-spacing-xl);
    border-radius: var(--ai-seo-border-radius-lg);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
    border: none;
    background: linear-gradient(135deg, var(--ai-seo-bg-white) 0%, var(--ai-seo-bg-subtle) 100%);
}

.ai-seo-comparison-section::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, var(--ai-seo-primary-color) 0%, var(--ai-seo-accent-color) 100%);
}

.ai-seo-comparison-section:last-of-type {
    margin-bottom: 0;
}

/* Title Comparison Section */
.ai-seo-comparison-section.title-comparison {
    background: linear-gradient(135deg, var(--ai-seo-primary-lighter) 0%, rgba(255, 255, 255, 0.9) 100%);
    border-radius: var(--ai-seo-border-radius-lg);
    padding: var(--ai-seo-spacing-xl);
    margin-bottom: var(--ai-seo-spacing-xl);
    box-shadow: 0 8px 25px rgba(var(--ai-seo-primary-hue), 76%, 58%, 0.1);
}

.ai-seo-comparison-section.title-comparison::before {
    background: linear-gradient(180deg, var(--ai-seo-primary-color) 0%, var(--ai-seo-primary-dark) 100%);
    width: 6px;
}

/* Description Comparison Section */
.ai-seo-comparison-section.description-comparison {
    background: linear-gradient(135deg, var(--ai-seo-info-bg) 0%, rgba(255, 255, 255, 0.9) 100%);
    border-radius: var(--ai-seo-border-radius-lg);
    padding: var(--ai-seo-spacing-xl);
    margin-bottom: var(--ai-seo-spacing-xl);
    box-shadow: 0 8px 25px rgba(var(--ai-seo-info-hue), 80%, 58%, 0.1);
}

.ai-seo-comparison-section.description-comparison::before {
    background: linear-gradient(180deg, var(--ai-seo-info-color) 0%, var(--ai-seo-info-dark) 100%);
    width: 6px;
}

/* --- Header for each comparison (Label, Checkbox, AI Rating) --- */
.ai-seo-comparison-header {
    grid-column: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: var(--ai-seo-spacing-md);
    padding: var(--ai-seo-spacing-md);
    position: sticky;
    top: var(--ai-seo-spacing-lg);
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(5px);
    border-radius: var(--ai-seo-border-radius);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    z-index: 5;
}

.ai-seo-comparison-header .label-group {
    display: flex;
    align-items: center;
    gap: var(--ai-seo-spacing-md);
    width: 100%;
    padding-bottom: var(--ai-seo-spacing-sm);
    border-bottom: 1px solid var(--ai-seo-border-light-color);
}

.ai-seo-comparison-header input[type="checkbox"] {
    margin: 0;
    transform: scale(1.3);
    accent-color: var(--ai-seo-primary-color);
    cursor: pointer;
    flex-shrink: 0;
    margin-right: var(--ai-seo-spacing-xs);
    width: 20px;
    height: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}

.ai-seo-comparison-header strong {
    font-size: 1.2em;
    font-weight: 700;
    color: var(--ai-seo-secondary-color);
    line-height: 1.4;
    letter-spacing: 0.01em;
}

.ai-seo-comparison-header .dashicons-info-outline {
    color: var(--ai-seo-primary-color);
    cursor: help;
    font-size: 18px;
    margin-right: var(--ai-seo-spacing-xs);
    opacity: 0.8;
    transition: all 0.2s ease;
}

.ai-seo-comparison-header .dashicons-info-outline:hover {
    opacity: 1;
    transform: scale(1.1);
}

/* Improved header styling for title and description */
.ai-seo-comparison-section.title-comparison .ai-seo-comparison-header {
    background-color: rgba(var(--ai-seo-primary-hue), 76%, 95%, 0.8);
    border: 1px solid var(--ai-seo-primary-border);
}

.ai-seo-comparison-section.description-comparison .ai-seo-comparison-header {
    background-color: rgba(var(--ai-seo-info-hue), 80%, 95%, 0.8);
    border: 1px solid var(--ai-seo-info-border);
}

.ai-seo-comparison-section.title-comparison .ai-seo-comparison-header strong,
.ai-seo-comparison-section.description-comparison .ai-seo-comparison-header strong {
    font-size: 1.3em;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: var(--ai-seo-spacing-sm);
}

.ai-seo-comparison-section.title-comparison .ai-seo-comparison-header strong {
    color: var(--ai-seo-primary-dark);
}

.ai-seo-comparison-section.description-comparison .ai-seo-comparison-header strong {
    color: var(--ai-seo-info-dark);
}

.ai-seo-comparison-section.title-comparison .ai-seo-comparison-header strong::before {
    content: "\f237"; /* Title icon */
    font-family: dashicons;
    font-size: 1.3em;
    color: var(--ai-seo-primary-color);
    background-color: rgba(255, 255, 255, 0.7);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(var(--ai-seo-primary-hue), 76%, 58%, 0.2);
}

.ai-seo-comparison-section.description-comparison .ai-seo-comparison-header strong::before {
    content: "\f478"; /* Description icon */
    font-family: dashicons;
    font-size: 1.3em;
    color: var(--ai-seo-info-color);
    background-color: rgba(255, 255, 255, 0.7);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(var(--ai-seo-info-hue), 80%, 58%, 0.2);
}
/* AI Quality Rating (e.g., 9/10) */
.ai-seo-comparison-header .ai-seo-rating {
    font-size: 0.95em;
    font-weight: 700;
    background: linear-gradient(135deg, var(--ai-seo-bg-alt) 0%, var(--ai-seo-bg-subtle) 100%);
    color: var(--ai-seo-text-light);
    padding: 8px 15px;
    border-radius: var(--ai-seo-border-radius-pill);
    border: 1px solid var(--ai-seo-border-color);
    cursor: help;
    margin-top: var(--ai-seo-spacing-sm);
    align-self: flex-start;
    display: inline-flex;
    align-items: center;
    gap: var(--ai-seo-spacing-sm);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
    transition: all 0.2s ease;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.ai-seo-comparison-header .ai-seo-rating:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.12);
}

/* Enhanced rating for title and description */
.ai-seo-comparison-section.title-comparison .ai-seo-rating {
    background: linear-gradient(135deg, var(--ai-seo-primary-color) 0%, var(--ai-seo-primary-dark) 100%);
    color: var(--ai-seo-primary-text);
    border: none;
    box-shadow: 0 4px 15px rgba(var(--ai-seo-primary-hue), 76%, 58%, 0.2);
}

.ai-seo-comparison-section.description-comparison .ai-seo-rating {
    background: linear-gradient(135deg, var(--ai-seo-info-color) 0%, var(--ai-seo-info-dark) 100%);
    color: var(--ai-seo-primary-text);
    border: none;
    box-shadow: 0 4px 15px rgba(var(--ai-seo-info-hue), 80%, 58%, 0.2);
}

.ai-seo-comparison-header .ai-seo-rating::before {
    content: "\f529"; /* Star icon */
    font-family: dashicons;
    font-size: 1.2em;
    background-color: rgba(255, 255, 255, 0.2);
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

/* --- Content Area (Holds Current & Suggested Boxes) --- */
.ai-seo-comparison-content {
    grid-column: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--ai-seo-spacing-xl);
}

/* --- Individual Comparison Box --- */
.ai-seo-comparison-box {
    padding: var(--ai-seo-spacing-lg);
    border-radius: var(--ai-seo-border-radius-lg);
    border: 1px solid var(--ai-seo-border-color);
    background-color: var(--ai-seo-bg-white);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.ai-seo-comparison-box::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--ai-seo-border-color) 0%, var(--ai-seo-border-light-color) 100%);
    transition: all 0.3s ease;
}

.ai-seo-comparison-box:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    transform: translateY(-3px);
    border-color: var(--ai-seo-primary-border);
}

.ai-seo-comparison-box .box-label {
    display: flex;
    font-size: 0.85em;
    font-weight: 700;
    color: var(--ai-seo-text-lighter);
    margin-bottom: var(--ai-seo-spacing-md);
    text-transform: uppercase;
    letter-spacing: 0.08em;
    border-bottom: 1px solid var(--ai-seo-border-light-color);
    padding-bottom: var(--ai-seo-spacing-sm);
    align-items: center;
    gap: var(--ai-seo-spacing-sm);
    position: relative;
}

.ai-seo-comparison-box .box-label .dashicons {
    font-size: 16px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--ai-seo-bg-subtle);
    border-radius: 50%;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}
/* Title/Desc Content Area */
.ai-seo-comparison-box .value-content {
    line-height: 1.7;
    font-size: 1.05em;
    word-break: break-word;
    display: block;
    flex-grow: 1;
    margin-bottom: var(--ai-seo-spacing-md);
    color: var(--ai-seo-text-color);
    background-color: var(--ai-seo-bg-subtle);
    border: 1px solid var(--ai-seo-border-light-color);
    padding: var(--ai-seo-spacing-lg);
    border-radius: var(--ai-seo-border-radius-lg);
    min-height: 80px;
    box-shadow: var(--ai-seo-shadow-inner);
    position: relative; /* For positioning the tooltip */
    transition: all 0.3s ease;
}

.ai-seo-comparison-box .value-content:hover {
    box-shadow: 0 0 0 1px var(--ai-seo-primary-border);
    background-color: var(--ai-seo-bg-white);
}

.ai-seo-comparison-box.suggestion .value-content {
    background-color: var(--ai-seo-primary-lighter);
    border-color: var(--ai-seo-primary-border);
    font-weight: 500;
    box-shadow: 0 5px 15px rgba(var(--ai-seo-primary-hue), 76%, 58%, 0.1);
}

.ai-seo-comparison-box.suggestion .value-content:hover {
    background-color: rgba(var(--ai-seo-primary-hue), 76%, 95%, 0.7);
    box-shadow: 0 8px 20px rgba(var(--ai-seo-primary-hue), 76%, 58%, 0.15);
}

/* Improved Title Display */
.ai-seo-comparison-box.title-comparison .value-content {
    font-size: 1.3em;
    font-weight: 600;
    line-height: 1.5;
    color: var(--ai-seo-secondary-color);
    padding: var(--ai-seo-spacing-xl);
    border-radius: var(--ai-seo-border-radius-lg);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

/* Improved Description Display */
.ai-seo-comparison-box.description-comparison .value-content {
    font-size: 1.1em;
    line-height: 1.6;
    padding: var(--ai-seo-spacing-xl);
    white-space: pre-line; /* Preserve line breaks */
    border-radius: var(--ai-seo-border-radius-lg);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

/* Tooltip for content explanation */
.ai-seo-comparison-box .value-content::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: calc(100% + 10px);
    left: 50%;
    transform: translateX(-50%) translateY(10px);
    background-color: var(--ai-seo-bg-dark);
    color: var(--ai-seo-bg-white);
    padding: 10px 16px;
    border-radius: var(--ai-seo-border-radius);
    font-size: 0.9em;
    white-space: nowrap;
    z-index: 100;
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    visibility: hidden;
}

.ai-seo-comparison-box .value-content::before {
    content: "";
    position: absolute;
    bottom: calc(100% + 5px);
    left: 50%;
    transform: translateX(-50%) translateY(10px);
    border-width: 5px;
    border-style: solid;
    border-color: var(--ai-seo-bg-dark) transparent transparent transparent;
    z-index: 101;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    visibility: hidden;
}

.ai-seo-comparison-box .value-content:hover::after,
.ai-seo-comparison-box .value-content:hover::before {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
    visibility: visible;
}

.ai-seo-value-empty {
    color: var(--ai-seo-text-lightest);
    font-style: italic;
    opacity: 0.8;
    padding: var(--ai-seo-spacing-md);
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: var(--ai-seo-border-radius);
    text-align: center;
}
/* Current vs. Suggested Box Styles */
.ai-seo-comparison-box.current {
    background: linear-gradient(135deg, var(--ai-seo-bg-white) 0%, var(--ai-seo-bg-subtle) 100%);
}

.ai-seo-comparison-box.current::before {
    background: linear-gradient(90deg, var(--ai-seo-secondary-light) 0%, var(--ai-seo-secondary-color) 100%);
}

.ai-seo-comparison-box.current .box-label {
    color: var(--ai-seo-secondary-color);
}

.ai-seo-comparison-box.current .box-label .dashicons {
    background-color: var(--ai-seo-bg-alt);
}

.ai-seo-comparison-box.current .box-label .dashicons::before {
    content: "\f145"; /* History */
    color: var(--ai-seo-secondary-color);
}

/* Suggested Box Styles */
.ai-seo-comparison-box.suggestion {
    background: linear-gradient(135deg, var(--ai-seo-bg-white) 0%, var(--ai-seo-primary-lighter) 100%);
    border-color: var(--ai-seo-primary-border);
    box-shadow: 0 8px 25px rgba(var(--ai-seo-primary-hue), 76%, 58%, 0.08);
}

.ai-seo-comparison-box.suggestion::before {
    background: linear-gradient(90deg, var(--ai-seo-primary-color) 0%, var(--ai-seo-accent-color) 100%);
    height: 6px;
}

.ai-seo-comparison-box.suggestion .box-label {
    color: var(--ai-seo-primary-dark);
    border-bottom-color: var(--ai-seo-primary-border);
}

.ai-seo-comparison-box.suggestion .box-label .dashicons {
    background-color: var(--ai-seo-primary-lighter);
}

.ai-seo-comparison-box.suggestion .box-label .dashicons::before {
    content: "\f155"; /* Lightbulb */
    color: var(--ai-seo-primary-color);
}

/* Title Comparison Box Styles */
.ai-seo-comparison-box.title-comparison {
    border-width: 2px;
    padding: var(--ai-seo-spacing-xl);
}

.ai-seo-comparison-box.title-comparison::before {
    height: 6px;
}

.ai-seo-comparison-box.title-comparison.current {
    background: linear-gradient(135deg, var(--ai-seo-bg-white) 0%, var(--ai-seo-bg-subtle) 100%);
    border-color: var(--ai-seo-secondary-border);
}

.ai-seo-comparison-box.title-comparison.current::before {
    background: linear-gradient(90deg, var(--ai-seo-secondary-color) 0%, var(--ai-seo-secondary-dark) 100%);
}

.ai-seo-comparison-box.title-comparison.suggestion {
    background: linear-gradient(135deg, var(--ai-seo-bg-white) 0%, var(--ai-seo-primary-lighter) 100%);
    border-color: var(--ai-seo-primary-color);
    box-shadow: 0 10px 30px rgba(var(--ai-seo-primary-hue), 76%, 58%, 0.1);
}

.ai-seo-comparison-box.title-comparison.suggestion::before {
    background: linear-gradient(90deg, var(--ai-seo-primary-color) 0%, var(--ai-seo-primary-dark) 100%);
    height: 8px;
}

.ai-seo-comparison-box.title-comparison .box-label {
    font-size: 0.9em;
    padding-bottom: var(--ai-seo-spacing-md);
    border-bottom-width: 2px;
}

/* Description Comparison Box Styles */
.ai-seo-comparison-box.description-comparison {
    border-width: 2px;
    padding: var(--ai-seo-spacing-xl);
}

.ai-seo-comparison-box.description-comparison::before {
    height: 6px;
}

.ai-seo-comparison-box.description-comparison.current {
    background: linear-gradient(135deg, var(--ai-seo-bg-white) 0%, var(--ai-seo-bg-subtle) 100%);
    border-color: var(--ai-seo-secondary-border);
}

.ai-seo-comparison-box.description-comparison.current::before {
    background: linear-gradient(90deg, var(--ai-seo-secondary-color) 0%, var(--ai-seo-secondary-dark) 100%);
}

.ai-seo-comparison-box.description-comparison.suggestion {
    background: linear-gradient(135deg, var(--ai-seo-bg-white) 0%, var(--ai-seo-info-bg) 100%);
    border-color: var(--ai-seo-info-color);
    box-shadow: 0 10px 30px rgba(var(--ai-seo-info-hue), 80%, 58%, 0.1);
}

.ai-seo-comparison-box.description-comparison.suggestion::before {
    background: linear-gradient(90deg, var(--ai-seo-info-color) 0%, var(--ai-seo-info-dark) 100%);
    height: 8px;
}

.ai-seo-comparison-box.description-comparison .box-label {
    font-size: 0.9em;
    padding-bottom: var(--ai-seo-spacing-md);
    border-bottom-width: 2px;
}

/* --- *** Meta Info (Professional Edition) *** --- */
.ai-seo-comparison-box .value-meta {
    margin-top: auto;
    padding-top: var(--ai-seo-spacing-md);
    border-top: 1px solid var(--ai-seo-border-light-color);
    font-size: 0.9em;
    color: var(--ai-seo-text-light);
    display: flex;
    flex-wrap: nowrap; /* Force single line */
    justify-content: space-between; /* Score RIGHT */
    align-items: center;
    gap: var(--ai-seo-spacing-lg);
    padding: var(--ai-seo-spacing-md) 0 0 0;
}

/* Improved meta info for title and description */
.ai-seo-comparison-box.title-comparison .value-meta,
.ai-seo-comparison-box.description-comparison .value-meta {
    background-color: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(5px);
    padding: var(--ai-seo-spacing-md) var(--ai-seo-spacing-lg);
    border-radius: var(--ai-seo-border-radius);
    margin-top: var(--ai-seo-spacing-md);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--ai-seo-border-light-color);
}

/* Highlight meta info for suggested content */
.ai-seo-comparison-box.suggestion.title-comparison .value-meta {
    background-color: rgba(var(--ai-seo-primary-hue), 76%, 95%, 0.8);
    border: 1px solid var(--ai-seo-primary-border);
    box-shadow: 0 4px 15px rgba(var(--ai-seo-primary-hue), 76%, 58%, 0.1);
}

.ai-seo-comparison-box.suggestion.description-comparison .value-meta {
    background-color: rgba(var(--ai-seo-info-hue), 80%, 95%, 0.8);
    border: 1px solid var(--ai-seo-info-border);
    box-shadow: 0 4px 15px rgba(var(--ai-seo-info-hue), 80%, 58%, 0.1);
}
.ai-seo-comparison-box .char-count { /* Character Count (Left) */
    padding: 8px 15px;
    border-radius: var(--ai-seo-border-radius-pill);
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    border: 1px solid var(--ai-seo-border-color);
    font-size: 13px;
    line-height: 1;
    white-space: nowrap;
    background: linear-gradient(135deg, var(--ai-seo-bg-white) 0%, var(--ai-seo-bg-alt) 100%);
    color: var(--ai-seo-text-light);
    flex-shrink: 0;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.ai-seo-comparison-box .char-count:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08);
    border-color: var(--ai-seo-primary-border);
}

.ai-seo-comparison-box .char-count::before {
    font-family: dashicons;
    font-size: 16px;
    line-height: 1;
    content: "\f224"; /* Text */
    background-color: rgba(255, 255, 255, 0.5);
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

/* Title and Description specific char count */
.ai-seo-comparison-box.title-comparison .char-count {
    background: linear-gradient(135deg, var(--ai-seo-bg-white) 0%, var(--ai-seo-primary-lighter) 100%);
    border-color: var(--ai-seo-primary-border);
    color: var(--ai-seo-primary-dark);
}

.ai-seo-comparison-box.description-comparison .char-count {
    background: linear-gradient(135deg, var(--ai-seo-bg-white) 0%, var(--ai-seo-info-bg) 100%);
    border-color: var(--ai-seo-info-border);
    color: var(--ai-seo-info-dark);
}

/* SEO Score Display - Premium, Modern, Professional Edition */
.ai-seo-comparison-box .seo-score,
.ai-seo-value .seo-score {
    padding: 10px 18px;
    border-radius: var(--ai-seo-border-radius-lg);
    font-weight: 600;
    font-size: 14px;
    line-height: 1.2;
    white-space: nowrap;
    cursor: help;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    border: none;
    transition: all 0.3s var(--ai-seo-transition-bounce);
    letter-spacing: 0.02em;
    box-shadow: var(--ai-seo-shadow-md);
    flex-shrink: 0;
    position: relative;
    overflow: hidden;

    /* Mobile-friendly design */
    max-width: 100%;
    text-overflow: ellipsis;

    /* Gradient background */
    background-image: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0) 100%);
}

/* Shine effect */
.ai-seo-comparison-box .seo-score::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
    );
    z-index: 1;
    transition: left 0.7s ease;
}

.ai-seo-comparison-box .seo-score:hover::before {
    left: 100%;
}

/* Premium hover effect */
.ai-seo-comparison-box .seo-score:hover {
    transform: translateY(-3px) scale(1.03);
    box-shadow: var(--ai-seo-shadow-lg);
}

/* Score icon */
.ai-seo-comparison-box .seo-score .score-icon {
    font-family: dashicons;
    font-size: 18px;
    line-height: 1;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.25);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-right: 2px;
    position: relative;
    z-index: 2;
}

/* SEO Score Colors & Icons - Premium Design */
/* Poor score */
.ai-seo-comparison-box .seo-score.score-poor {
    background: var(--ai-seo-gradient-error);
    color: white;
}

.ai-seo-comparison-box .seo-score.score-poor .score-icon::before {
    content: "\f335"; /* X icon */
    color: white;
}

/* Fair score */
.ai-seo-comparison-box .seo-score.score-fair {
    background: var(--ai-seo-gradient-warning);
    color: var(--ai-seo-neutral-900);
}

.ai-seo-comparison-box .seo-score.score-fair .score-icon::before {
    content: "\f534"; /* Warning icon */
    color: var(--ai-seo-neutral-900);
}

/* Good score */
.ai-seo-comparison-box .seo-score.score-good {
    background: linear-gradient(135deg,
        hsl(95, 75%, 50%) 0%,
        hsl(95, 75%, 40%) 100%);
    color: white;
}

.ai-seo-comparison-box .seo-score.score-good .score-icon::before {
    content: "\f147"; /* Checkmark */
    color: white;
}

/* Excellent score */
.ai-seo-comparison-box .seo-score.score-excellent {
    background: var(--ai-seo-gradient-success);
    color: white;
}

.ai-seo-comparison-box .seo-score.score-excellent .score-icon::before {
    content: "\f529"; /* Star */
    color: white;
}

/* Score value emphasis */
.ai-seo-comparison-box .seo-score .score-value {
    font-weight: 700;
    font-size: 16px;
    position: relative;
    z-index: 2;
}

/* Score label */
.ai-seo-comparison-box .seo-score .score-label {
    font-weight: 500;
    opacity: 0.9;
    position: relative;
    z-index: 2;
}

/* Title and Description specific score styles */
.ai-seo-comparison-box.title-comparison .seo-score {
    box-shadow: 0 4px 12px rgba(var(--ai-seo-primary-hue), 70%, 50%, 0.2);
}

.ai-seo-comparison-box.description-comparison .seo-score {
    box-shadow: 0 4px 12px rgba(var(--ai-seo-info-hue), 70%, 50%, 0.2);
}

/* Score in Manual Process section - Premium Design */
.ai-seo-post-list .seo-score {
    font-size: 13px;
    padding: 6px 12px;
    border-radius: var(--ai-seo-border-radius);
    margin-left: 8px;
    box-shadow: var(--ai-seo-shadow-sm);
}

.ai-seo-post-list .seo-score .score-icon {
    font-size: 14px;
    width: 22px;
    height: 22px;
}

/* Score tooltip - Premium Design */
.ai-seo-comparison-box .seo-score .score-tooltip {
    position: absolute;
    bottom: calc(100% + 12px);
    left: 50%;
    transform: translateX(-50%) translateY(10px);
    background-color: var(--ai-seo-neutral-800);
    color: white;
    padding: 10px 16px;
    border-radius: var(--ai-seo-border-radius);
    font-size: 13px;
    white-space: nowrap;
    z-index: 100;
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s ease;
    box-shadow: var(--ai-seo-shadow-lg);
    visibility: hidden;
    backdrop-filter: var(--ai-seo-backdrop-blur);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Tooltip arrow */
.ai-seo-comparison-box .seo-score .score-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -8px;
    border-width: 8px;
    border-style: solid;
    border-color: var(--ai-seo-neutral-800) transparent transparent transparent;
}

.ai-seo-comparison-box .seo-score:hover .score-tooltip {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
    visibility: visible;
}

/* Score comparison - Premium Design */
.ai-seo-score-comparison {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: 10px;
}

.ai-seo-score-comparison .score-arrow {
    font-size: 20px;
    color: var(--ai-seo-primary-color);
    animation: ai-seo-pulse 1.5s infinite;
}

@keyframes ai-seo-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}


/* --- Internal Links Section - Premium Design --- */
.internal-links-section {
    margin-top: var(--ai-seo-spacing-xl);
    border-radius: var(--ai-seo-border-radius-lg);
    overflow: hidden;
    box-shadow: var(--ai-seo-shadow-md);
    border: 1px solid var(--ai-seo-border-color);
    background-color: var(--ai-seo-bg-white);
    transition: var(--ai-seo-transition-base);
}

.internal-links-section:hover {
    box-shadow: var(--ai-seo-shadow-lg);
    transform: translateY(-2px);
}

/* Section header - Premium Design */
.internal-links-section .ai-seo-comparison-header {
    grid-column: 1 / -1;
    position: static;
    border-bottom: var(--ai-seo-border-width) solid var(--ai-seo-border-color);
    padding: var(--ai-seo-spacing-md) var(--ai-seo-spacing-lg);
    margin-bottom: 0;
    background: linear-gradient(to right, var(--ai-seo-primary-lighter), var(--ai-seo-bg-white));
    position: relative;
}

/* Header accent line */
.internal-links-section .ai-seo-comparison-header::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background: var(--ai-seo-gradient-primary);
    border-radius: 0 var(--ai-seo-border-radius-pill) var(--ai-seo-border-radius-pill) 0;
}

.internal-links-section .ai-seo-comparison-header strong {
    font-size: 1.05em;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--ai-seo-spacing-sm);
    color: var(--ai-seo-neutral-800);
    letter-spacing: 0.01em;
}

.internal-links-section .ai-seo-comparison-header .dashicons {
    font-size: 20px;
    color: var(--ai-seo-primary-color);
    background-color: rgba(var(--ai-seo-primary-hue), var(--ai-seo-primary-saturation), var(--ai-seo-primary-lightness), 0.1);
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(var(--ai-seo-primary-hue), var(--ai-seo-primary-saturation), var(--ai-seo-primary-lightness), 0.2);
}

.internal-links-section .ai-seo-comparison-content {
    grid-column: 1 / -1;
    grid-template-columns: 1fr;
}

.internal-links-section .ai-seo-comparison-box {
    background-color: var(--ai-seo-bg-white);
    border: none;
    border-radius: 0;
    box-shadow: none;
    padding: var(--ai-seo-spacing-lg);
}

/* Links list - Premium Design */
.ai-seo-links-list {
    list-style: none !important;
    list-style-type: none !important;
    list-style-image: none !important;
    margin: 0 !important;
    padding: 0 !important;
    background-image: none !important;
}

.ai-seo-links-list li {
    padding: 12px var(--ai-seo-spacing-sm);
    border-bottom: 1px solid var(--ai-seo-border-light-color);
    font-size: 0.95em;
    line-height: 1.6;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--ai-seo-spacing-sm);
    list-style-type: none !important;
    list-style-image: none !important;
    background-image: none !important;
    text-indent: 0 !important;
    margin-left: 0 !important;
    padding-left: 0 !important;
    transition: all 0.25s ease;
    border-radius: var(--ai-seo-border-radius-sm);
}

.ai-seo-links-list li:hover {
    background-color: var(--ai-seo-primary-lighter);
    transform: translateX(3px);
    box-shadow: 0 2px 5px rgba(var(--ai-seo-primary-hue), var(--ai-seo-primary-saturation), var(--ai-seo-primary-lightness), 0.1);
}

/* Override any ::before pseudo-elements that might be adding bullets */
.ai-seo-links-list li::before,
.ai-seo-links-list li::after {
    content: none !important;
    display: none !important;
}

.ai-seo-links-list li:first-child {
    padding-top: 0;
}

.ai-seo-links-list li:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

/* Code styling - Premium Design */
.ai-seo-links-list li code {
    font-size: 0.9em;
    padding: 3px 8px;
    background-color: var(--ai-seo-neutral-100);
    border-radius: var(--ai-seo-border-radius-sm);
    color: var(--ai-seo-neutral-700);
    border: 1px solid var(--ai-seo-neutral-200);
    font-family: var(--ai-seo-font-monospace);
    box-shadow: var(--ai-seo-shadow-xs);
}

/* Link styling - Premium Design */
.ai-seo-links-list li a {
    font-weight: 500;
    color: var(--ai-seo-primary-color);
    text-decoration: none;
    transition: all 0.25s ease;
    position: relative;
    padding-bottom: 2px;
}

.ai-seo-links-list li a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 1px;
    background-color: var(--ai-seo-primary-color);
    transition: width 0.3s ease;
}

.ai-seo-links-list li a:hover {
    color: var(--ai-seo-primary-dark);
}

.ai-seo-links-list li a:hover::after {
    width: 100%;
}

.ai-seo-links-list li .link-target-id {
    font-size: 0.85em;
    color: var(--ai-seo-neutral-400);
    margin-left: var(--ai-seo-spacing-xs);
    background-color: var(--ai-seo-neutral-100);
    padding: 2px 6px;
    border-radius: var(--ai-seo-border-radius-xs);
    border: 1px solid var(--ai-seo-neutral-200);
}

.internal-links-section .description {
    margin-top: var(--ai-seo-spacing-md);
    font-size: 0.9em;
    color: var(--ai-seo-neutral-500);
    padding: 0 var(--ai-seo-spacing-md);
    line-height: 1.6;
}

/* Link relevance score - Premium Design */
.link-relevance-score {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-left: auto;
    font-size: 0.85em;
    font-weight: 600;
    color: var(--ai-seo-primary-color);
    background: linear-gradient(to right, var(--ai-seo-primary-lighter), rgba(var(--ai-seo-primary-hue), var(--ai-seo-primary-saturation), 97%));
    padding: 4px 10px;
    border-radius: var(--ai-seo-border-radius-pill);
    box-shadow: var(--ai-seo-shadow-sm);
    cursor: help;
    position: relative;
    right: 0;
    transition: all 0.25s ease;
    border: 1px solid var(--ai-seo-primary-border);
}

.link-relevance-score::before {
    content: "Relevance: ";
    font-weight: 400;
    color: var(--ai-seo-neutral-600);
    margin-right: 2px;
}

.link-relevance-score:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--ai-seo-shadow-md);
    background: linear-gradient(to right, var(--ai-seo-primary-lighter), var(--ai-seo-primary-light));
    color: var(--ai-seo-primary-dark);
}

/* Select all links row - Premium Design */
.ai-seo-links-list li.select-all-links {
    padding: 10px var(--ai-seo-spacing-md);
    border-bottom: 1px solid var(--ai-seo-border-color);
    font-weight: 600;
    background: linear-gradient(to right, var(--ai-seo-primary-lighter), var(--ai-seo-bg-white));
    color: var(--ai-seo-neutral-800);
    list-style-type: none !important;
    list-style-image: none !important;
    background-image: none !important;
    border-radius: 0;
}

.ai-seo-links-list li.select-all-links:hover {
    transform: none;
    box-shadow: none;
}

/* Checkbox labels - Premium Design */
.ai-seo-links-list li.select-all-links label,
.ai-seo-links-list li label.link-item {
    display: flex;
    align-items: center;
    gap: var(--ai-seo-spacing-sm);
    cursor: pointer;
    flex-grow: 1;
    padding: 3px 0;
}

/* Premium checkboxes */
.ai-seo-links-list input[type="checkbox"] {
    margin: 0;
    appearance: none;
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    border: 2px solid var(--ai-seo-neutral-300);
    border-radius: 4px;
    background-color: white;
    cursor: pointer;
    position: relative;
    transition: all 0.25s ease;
    flex-shrink: 0;
    box-shadow: var(--ai-seo-shadow-xs);
}

.ai-seo-links-list input[type="checkbox"]:checked {
    background-color: var(--ai-seo-primary-color);
    border-color: var(--ai-seo-primary-color);
    box-shadow: 0 0 0 2px var(--ai-seo-primary-focus-shadow);
}

.ai-seo-links-list input[type="checkbox"]:checked::after {
    content: "";
    position: absolute;
    left: 5px;
    top: 2px;
    width: 5px;
    height: 9px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.ai-seo-links-list input[type="checkbox"]:hover {
    border-color: var(--ai-seo-primary-color);
    transform: scale(1.1);
}

.ai-seo-links-list input[type="checkbox"]:focus {
    outline: none;
    box-shadow: 0 0 0 3px var(--ai-seo-primary-focus-shadow);
}

/* Advanced link options - Premium Design */
.advanced-link-options {
    margin-top: var(--ai-seo-spacing-md);
    padding: var(--ai-seo-spacing-lg);
    border-top: 1px solid var(--ai-seo-border-light-color);
    background: linear-gradient(to bottom, var(--ai-seo-bg-white), var(--ai-seo-bg-subtle));
    border-radius: 0 0 var(--ai-seo-border-radius-lg) var(--ai-seo-border-radius-lg);
}

.advanced-link-options h4 {
    font-size: 1em;
    margin-bottom: var(--ai-seo-spacing-sm);
    color: var(--ai-seo-primary-color);
    font-weight: 600;
    border: none;
    padding: 0;
    display: flex;
    align-items: center;
    gap: var(--ai-seo-spacing-xs);
}

.advanced-link-options h4::before {
    content: "\f111";
    font-family: dashicons;
    font-size: 14px;
    color: var(--ai-seo-primary-color);
}

.advanced-link-options label {
    display: flex;
    align-items: center;
    gap: var(--ai-seo-spacing-sm);
    font-size: 0.95em;
    color: var(--ai-seo-neutral-700);
    transition: var(--ai-seo-transition-base);
    padding: 4px 0;
}

.advanced-link-options label:hover {
    color: var(--ai-seo-primary-color);
}

.advanced-link-options .description {
    font-size: 0.9em;
    margin-top: var(--ai-seo-spacing-xs);
    color: var(--ai-seo-neutral-500);
    padding-left: 28px; /* Align with checkbox text */
    line-height: 1.6;
}


/* ==========================================================================
   == Quantum Responsive Design - Modern & Mobile-Friendly ==
   ========================================================================== */

/* --- Medium Screens (Tablets, Smaller Desktops) --- */
@media screen and (max-width: 1200px) {
    /* Stack comparison boxes */
    .ai-seo-comparison-content {
        grid-template-columns: 1fr;
    }

    .ai-seo-comparison-box {
        margin-bottom: var(--ai-seo-spacing-md);
    }

    .ai-seo-comparison-box:last-child {
        margin-bottom: 0;
    }
}

@media screen and (max-width: 960px) {
    /* Adjust card padding */
    .ai-seo-box,
    .ai-seo-card {
        padding: var(--ai-seo-spacing-md);
    }

    /* Settings table adjustments */
    #tab-settings .form-table th {
        width: 200px;
    }

    /* Status summary layout */
    #ai-seo-status-summary {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    }

    /* Stack comparison sections */
    .ai-seo-comparison-section {
        grid-template-columns: 1fr;
    }

    /* Header adjustments */
    .ai-seo-comparison-header {
        position: static;
        grid-column: 1;
        padding-bottom: var(--ai-seo-spacing-sm);
        border-bottom: var(--ai-seo-border-width) solid var(--ai-seo-border-color);
        margin-bottom: var(--ai-seo-spacing-md);
    }

    /* Single column content */
    .ai-seo-comparison-content {
        grid-column: 1;
        grid-template-columns: 1fr;
    }

    /* Review item spacing */
    .ai-seo-review-item {
        margin-left: 0;
        margin-right: 0;
    }
}

/* --- Small Screens (Mobile - WP Admin Breakpoint) --- */
@media screen and (max-width: 782px) {
    /* Base mobile optimizations */
    html {
        -webkit-text-size-adjust: 100%;
        touch-action: manipulation;
    }

    /* Container adjustments */
    .ai-seo-optimizer-wrap {
        font-size: 14px;
        padding: var(--ai-seo-spacing-md);
        margin-right: 8px;
        margin-left: 8px;
        border-radius: var(--ai-seo-border-radius);
    }

    /* Touch-friendly elements */
    .ai-seo-button,
    .ai-seo-nav-tabs .nav-tab,
    input[type="checkbox"],
    select,
    .ai-seo-post-list li label,
    .ai-seo-links-list li label {
        min-height: 40px;
        min-width: 40px;
    }

    /* Header layout */
    .ai-seo-header {
        flex-direction: column;
        align-items: flex-start;
        padding: var(--ai-seo-spacing-md);
    }

    .ai-seo-header-meta {
        width: 100%;
        gap: var(--ai-seo-spacing-sm);
        margin-top: var(--ai-seo-spacing-md);
    }

    /* Scrollable tabs */
    .ai-seo-nav-tabs {
        padding: 0;
        overflow-x: auto;
        white-space: nowrap;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: thin;
        scrollbar-color: var(--ai-seo-neutral-300) transparent;
    }

    .ai-seo-nav-tabs::-webkit-scrollbar {
        height: 4px;
    }

    .ai-seo-nav-tabs::-webkit-scrollbar-thumb {
        background-color: var(--ai-seo-neutral-300);
        border-radius: 2px;
    }

    .ai-seo-nav-tabs .nav-tab {
        padding: 10px var(--ai-seo-spacing-md);
        font-size: 13px;
        flex: 0 0 auto;
    }

    /* Content containers */
    .ai-seo-box,
    .ai-seo-card,
    .ai-seo-tabs-container > .tab-content {
        padding: var(--ai-seo-spacing-md);
    }

    /* Settings form layout */
    #tab-settings .form-table th,
    #tab-settings .form-table td {
        display: block;
        width: 100%;
        padding: var(--ai-seo-spacing-xs) 0;
    }

    #tab-settings .form-table th {
        margin-bottom: 4px;
        font-weight: 600;
        padding-top: var(--ai-seo-spacing-sm);
    }

    #tab-settings .form-table tr {
        padding-bottom: var(--ai-seo-spacing-md);
        margin-bottom: var(--ai-seo-spacing-md);
    }

    #tab-settings .form-table input[type="text"],
    #tab-settings .form-table input[type="password"],
    #tab-settings .form-table input[type="url"],
    #tab-settings .form-table select,
    #tab-settings .form-table textarea,
    #tab-settings .form-table #custom-model-input {
        max-width: 100%;
    }

    /* Controls layout */
    .ai-seo-controls-box {
        flex-direction: column;
        gap: var(--ai-seo-spacing-sm);
        align-items: stretch;
    }

    .ai-seo-controls-box > div {
        flex-direction: column;
        align-items: stretch;
        width: 100%;
    }

    #ai-seo-manual-controls input[type="search"] {
        min-width: unset;
    }

    /* Pagination */
    .ai-seo-pagination {
        flex-wrap: wrap;
        justify-content: center;
        gap: 6px;
    }

    /* Post list items */
    .ai-seo-post-list li {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--ai-seo-spacing-xs);
        padding: var(--ai-seo-spacing-sm) 0;
    }

    .ai-seo-post-list li label {
        margin-right: 0;
        width: 100%;
    }

    /* Score display */
    .seo-scores {
        width: 100%;
        justify-content: flex-start;
        margin-top: var(--ai-seo-spacing-xs);
        gap: var(--ai-seo-spacing-xs);
    }

    /* Status summary */
    #ai-seo-status-summary {
        grid-template-columns: 1fr 1fr;
        gap: var(--ai-seo-spacing-sm);
    }

    #ai-seo-status-summary > span {
        min-height: 90px;
        padding: var(--ai-seo-spacing-sm);
    }

    #ai-seo-status-summary span span {
        font-size: 1.8em;
    }

    /* Failed items */
    .ai-seo-failed-item {
        padding: var(--ai-seo-spacing-sm);
    }

    .failed-item-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--ai-seo-spacing-xs);
    }

    /* Review Tab Mobile Optimizations */
    .ai-seo-review-item-header {
        grid-template-columns: 1fr;
        padding: var(--ai-seo-spacing-sm);
    }

    .ai-seo-item-actions {
        grid-column: 1;
        margin-top: var(--ai-seo-spacing-sm);
        justify-content: flex-start;
        gap: 8px;
        flex-wrap: wrap;
    }

    .ai-seo-item-actions .button {
        flex-grow: 1;
        justify-content: center;
        font-size: 13px;
        padding: 6px 10px;
    }

    /* Comparison sections */
    .ai-seo-comparison-section {
        grid-template-columns: 1fr;
    }

    .ai-seo-comparison-header {
        padding-bottom: var(--ai-seo-spacing-xs);
        border-bottom: var(--ai-seo-border-width) solid var(--ai-seo-border-color);
        margin-bottom: var(--ai-seo-spacing-sm);
        width: 100%;
        position: static;
        flex-direction: row;
        flex-wrap: wrap;
        align-items: center;
    }

    .ai-seo-comparison-header .ai-seo-rating {
        margin-left: auto;
        font-size: 0.85em;
        padding: 6px 10px;
    }

    .ai-seo-comparison-content {
        grid-column: 1;
        grid-template-columns: 1fr;
    }

    .ai-seo-comparison-box {
        padding: var(--ai-seo-spacing-sm);
        margin-bottom: var(--ai-seo-spacing-sm);
    }

    .ai-seo-comparison-box .value-content {
        font-size: 0.95em;
        padding: var(--ai-seo-spacing-sm);
        min-height: 60px;
    }

    .ai-seo-comparison-box .value-meta {
        gap: var(--ai-seo-spacing-xs);
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;
        padding: var(--ai-seo-spacing-xs) var(--ai-seo-spacing-sm);
    }

    .ai-seo-comparison-box .seo-score {
        margin-left: 0;
        padding: 4px 8px;
        font-size: 11px;
        flex-shrink: 0;
    }

    .ai-seo-comparison-box .seo-score::before {
        font-size: 14px;
        width: 18px;
        height: 18px;
    }

    .ai-seo-comparison-box .char-count {
        padding: 4px 8px;
        font-size: 11px;
    }

    /* Mobile-Optimized Internal Links Section */
    .internal-links-section {
        margin-top: var(--ai-seo-spacing-md);
        border-radius: var(--ai-seo-border-radius);
    }

    .internal-links-section .ai-seo-comparison-header {
        padding: 8px 10px;
        margin-bottom: 0;
        background-color: var(--ai-seo-bg-subtle);
        border-bottom: 1px solid var(--ai-seo-border-color);
    }

    .internal-links-section .ai-seo-comparison-header strong {
        font-size: 0.9em;
        font-weight: 600;
        color: var(--ai-seo-neutral-700);
    }

    .internal-links-section .ai-seo-comparison-header .dashicons {
        font-size: 16px;
        width: 16px;
        height: 16px;
        color: var(--ai-seo-primary-color);
    }

    .internal-links-section .ai-seo-comparison-box {
        padding: 0;
        border: none;
        box-shadow: none;
        background-color: transparent;
    }

    /* Compact links list */
    .ai-seo-links-list {
        font-size: 0.85em;
    }

    /* Select all row */
    .ai-seo-links-list li.select-all-links {
        position: sticky;
        top: 0;
        z-index: 5;
        padding: 8px 10px !important;
        background-color: var(--ai-seo-bg-subtle);
        border-bottom: 1px solid var(--ai-seo-border-color);
    }

    /* Regular link rows */
    .ai-seo-links-list li {
        padding: 8px 10px !important;
        margin: 0 !important;
        flex-direction: row;
        flex-wrap: wrap;
        border-bottom: 1px solid var(--ai-seo-border-light-color);
    }

    /* Link item layout */
    .ai-seo-links-list li label.link-item {
        padding: 4px 0 !important;
        margin: 0 !important;
        flex: 1 1 auto;
        min-width: 0;
        display: flex;
        align-items: center;
    }

    /* Modern checkboxes for mobile */
    .ai-seo-links-list input[type="checkbox"] {
        width: 16px !important;
        height: 16px !important;
        min-width: 16px !important;
        min-height: 16px !important;
        margin: 0 8px 0 0 !important;
        padding: 0 !important;
        vertical-align: middle;
        position: relative;
        border: 1px solid var(--ai-seo-neutral-300);
        border-radius: 3px;
        background-color: white;
        cursor: pointer;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        box-shadow: var(--ai-seo-shadow-xs);
        transition: all 0.2s ease;
        flex-shrink: 0;
    }

    .ai-seo-links-list input[type="checkbox"]:hover {
        border-color: var(--ai-seo-primary-color);
    }

    .ai-seo-links-list input[type="checkbox"]:focus {
        outline: none;
        box-shadow: 0 0 0 2px var(--ai-seo-primary-focus-shadow);
    }

    .ai-seo-links-list input[type="checkbox"]:checked {
        background-color: var(--ai-seo-primary-color);
        border-color: var(--ai-seo-primary-color);
    }

    .ai-seo-links-list input[type="checkbox"]:checked::after {
        content: "";
        position: absolute;
        left: 5px;
        top: 2px;
        width: 4px;
        height: 8px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
    }
}

    /* Link text row */
    .ai-seo-link-item-text {
        width: 100%;
        padding: 4px 0 4px 20px !important;
        word-break: break-word;
        font-size: 0.9em;
        line-height: 1.3;
    }

    .ai-seo-links-list li a {
        color: var(--ai-seo-link-color);
        text-decoration: none;
        display: inline;
    }

    /* Compact relevance score */
    .link-relevance-score {
        font-size: 0.7em;
        padding: 2px 6px !important;
        border-radius: 4px;
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        color: white;
        margin: 0 !important;
        white-space: nowrap;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        float: right;
        position: relative;
        right: 0;
    }

    /* Advanced options section */
    .advanced-link-options {
        padding: 10px;
        border-top: 1px solid var(--ai-seo-border-color);
        background-color: var(--ai-seo-bg-subtle);
        margin-top: 0;
        font-size: 0.9em;
    }

    .advanced-link-options h4 {
        margin: 0 0 8px 0;
        font-size: 0.95em;
    }

    .advanced-link-options label {
        display: flex;
        align-items: center;
        padding: 4px 0;
        font-size: 0.9em;
    }

/* --- Extra Small Screens (Less than 600px) --- */
@media screen and (max-width: 600px) {
    .ai-seo-optimizer-wrap { font-size: 14px; padding: var(--ai-seo-spacing-sm); margin-right: 5px; }
    .ai-seo-header-title h1 { font-size: 22px; } /* Slightly larger */
    .ai-seo-nav-tabs .nav-tab { font-size: 13px; padding: 10px; }
    .ai-seo-box, .ai-seo-card, .ai-seo-tabs-container > .tab-content { padding: var(--ai-seo-spacing-md); }
    .ai-seo-post-list li { padding: var(--ai-seo-spacing-md); }
    .seo-scores { padding: 5px 10px; font-size: 0.85em; }
    #ai-seo-status-summary { grid-template-columns: 1fr; }
    .ai-seo-review-item-body { padding: var(--ai-seo-spacing-md); gap: var(--ai-seo-spacing-md); }
    .ai-seo-comparison-box .value-meta { gap: var(--ai-seo-spacing-xs); }
    .ai-seo-comparison-box .char-count, .ai-seo-comparison-box .seo-score { padding: 5px 10px; font-size: 11px; }
    .ai-seo-comparison-box .seo-score::before { font-size: 14px; }
    .ai-seo-button { padding: 10px 14px; font-size: 14px; }
    input[type="text"], input[type="url"], input[type="email"], input[type="number"],
    input[type="password"], input[type="search"], select, textarea { font-size: 16px; /* Prevent iOS zoom */ }

    /* Enhanced Mobile Experience for Extra Small Screens */
    .ai-seo-review-item {
        margin-bottom: var(--ai-seo-spacing-md);
    }

    .ai-seo-review-item-header {
        padding: var(--ai-seo-spacing-sm);
    }

    .ai-seo-review-item-title {
        font-size: 16px;
    }

    /* Further optimize internal links for small screens */
    .internal-links-section {
        margin-bottom: 70px; /* Space for fixed buttons */
    }

    .internal-links-section .ai-seo-comparison-header {
        padding: 6px 8px;
        font-size: 0.85em;
    }

    .internal-links-section .ai-seo-comparison-header .dashicons {
        font-size: 14px;
        width: 14px;
        height: 14px;
    }

    /* Even more compact list */
    .ai-seo-links-list {
        font-size: 0.8em;
    }

    /* Tighter padding */
    .ai-seo-links-list li {
        padding: 4px 6px !important;
    }

    .ai-seo-links-list li.select-all-links {
        padding: 4px 6px !important;
    }

    /* Smaller professional checkboxes */
    .ai-seo-links-list input[type="checkbox"] {
        width: 16px !important;
        height: 16px !important;
        min-width: 16px !important;
        min-height: 16px !important;
        margin-right: 6px !important;
        border-width: 1.5px !important;
    }

    .ai-seo-links-list input[type="checkbox"]:checked::after {
        left: 4px;
        top: 1px;
        width: 4px;
        height: 8px;
        border-width: 0 1.5px 1.5px 0;
    }

    /* Optimize link text display */
    .ai-seo-link-item-text {
        padding: 3px 0 3px 16px !important;
        font-size: 0.85em;
        line-height: 1.2;
    }

    /* Smaller relevance score */
    .link-relevance-score {
        font-size: 0.65em;
        padding: 1px 4px !important;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .link-relevance-score::before {
        content: "R: ";
    }

    /* Compact advanced options */
    .advanced-link-options {
        padding: 8px;
        font-size: 0.85em;
    }

    .advanced-link-options h4 {
        font-size: 0.9em;
        margin-bottom: 6px;
    }

    .advanced-link-options label {
        padding: 3px 0;
        font-size: 0.85em;
    }

    /* Fixed action buttons at bottom of screen */
    .ai-seo-review-controls {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 100;
        padding: var(--ai-seo-spacing-sm);
        background-color: var(--ai-seo-bg-white);
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        border-top: 1px solid var(--ai-seo-border-color);
        margin-bottom: 0;
        display: flex;
        justify-content: center;
        gap: var(--ai-seo-spacing-sm);
    }

    .ai-seo-review-controls .ai-seo-button {
        flex: 1;
        min-width: 0;
        padding: 10px 8px;
        font-size: 13px;
        text-align: center;
        white-space: nowrap;
    }

    /* Improve suggestion section on mobile */
    .suggestion-header {
        padding: 8px 12px;
    }

    .suggestion-label {
        font-size: 0.9em;
    }

    .suggestion-current, .suggestion-suggested {
        padding: var(--ai-seo-spacing-sm);
    }

    .suggestion-current > .value, .suggestion-suggested > .value {
        padding: 8px;
        min-height: 80px;
        font-size: 0.95em;
    }

    /* Improve score display on mobile */
    .suggestion-current .score-percent, .suggestion-suggested .score-percent {
        padding: 4px 10px;
        min-width: 60px;
        font-size: 0.9em;
    }
}

/* --- Very Small Screens (iPhone SE / Small Mobile) --- */
@media screen and (max-width: 375px) {
    .ai-seo-optimizer-wrap { font-size: 13px; padding: var(--ai-seo-spacing-xs); }
    .ai-seo-header-title h1 { font-size: 18px; }
    .ai-seo-nav-tabs .nav-tab { font-size: 12px; padding: 8px 6px; }

    /* Ultra-optimized internal links for very small screens */
    .internal-links-section .ai-seo-comparison-header {
        padding: 4px 6px;
        font-size: 0.8em;
    }

    .internal-links-section .ai-seo-comparison-header .dashicons {
        font-size: 12px;
        width: 12px;
        height: 12px;
    }

    /* Micro-sized list */
    .ai-seo-links-list {
        font-size: 0.75em;
    }

    /* Minimal padding */
    .ai-seo-links-list li {
        padding: 3px 4px !important;
    }

    .ai-seo-links-list li.select-all-links {
        padding: 3px 4px !important;
    }

    /* Tiny checkboxes */
    .ai-seo-links-list input[type="checkbox"] {
        width: 10px !important;
        height: 10px !important;
        min-width: 10px !important;
        min-height: 10px !important;
        margin-right: 3px !important;
        border-width: 1px;
    }

    .ai-seo-links-list input[type="checkbox"]:checked::after {
        left: 2px;
        top: 0px;
        width: 3px;
        height: 5px;
        border-width: 0 1px 1px 0;
    }

    /* Optimize link text display */
    .ai-seo-link-item-text {
        padding: 2px 0 2px 13px !important;
        font-size: 0.8em;
        line-height: 1.2;
        word-break: break-all;
    }

    /* Micro relevance score */
    .link-relevance-score {
        font-size: 0.65em;
        padding: 1px 3px !important;
    }

    /* Ultra-compact advanced options */
    .advanced-link-options {
        padding: 6px;
        font-size: 0.8em;
    }

    .advanced-link-options h4 {
        font-size: 0.85em;
        margin-bottom: 4px;
    }

    .advanced-link-options label {
        padding: 2px 0;
        font-size: 0.8em;
    }

    /* Make buttons more compact */
    .ai-seo-review-controls .ai-seo-button {
        padding: 8px 6px;
        font-size: 12px;
    }

    /* Simplify score display */
    .suggestion-current .score-percent, .suggestion-suggested .score-percent {
        padding: 3px 8px;
        min-width: 50px;
    }

    /* Ensure checkboxes are easily tappable */
    input[type="checkbox"] {
        min-width: 24px;
        min-height: 24px;
        margin: 0 8px 0 0;
    }
}

/* --- Print Styles --- */
@media print {
    .ai-seo-header, .ai-seo-nav-tabs, .ai-seo-controls-box, .button, .ai-seo-pagination, .ai-seo-item-actions, #wpadminbar, #adminmenumain { display: none !important; }
    .ai-seo-optimizer-wrap { box-shadow: none; border: none; padding: 0; margin: 0 !important; width: 100%; }
    .ai-seo-box, .ai-seo-card, .ai-seo-tabs-container > .tab-content { box-shadow: none; border: 1px solid #ccc; margin-bottom: 1cm; padding: 1cm; }
    .ai-seo-review-item, .ai-seo-comparison-box { page-break-inside: avoid; }
    a { color: #333; text-decoration: none; }
    a[href^="http"]::after { content: " [" attr(href) "]"; font-size: 0.8em; word-break: break-all; }
    .ai-seo-comparison-section, .ai-seo-comparison-content { grid-template-columns: 1fr !important; } /* Stack everything for print */
    .ai-seo-comparison-header { position: static !important; }
    .seo-score { background-color: #eee !important; color: #333 !important; border-color: #ccc !important; text-shadow: none !important; } /* Basic score print */
}

/* ==========================================================================
   == Mobile Enhancements ==
   ========================================================================== */
/* Mobile enhancements integrated directly into the main CSS file */

/* ==========================================================================
   == Phoenix Review Tab - ULTRA PREMIUM COMPARISON DESIGN ==
   ========================================================================== */

/* --- Review Item Card - Enhanced Visual Design --- */
.ai-seo-review-item {
    border: none;
    border-radius: var(--ai-seo-border-radius-xl);
    margin: 0 0 var(--ai-seo-spacing-xl) 0;
    background-color: var(--ai-seo-bg-white);
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    position: relative;
}

.ai-seo-review-item:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
    transform: translateY(-5px);
    z-index: 10;
}

/* --- Suggestion Section Container - Dramatically Enhanced --- */
.ai-seo-suggestion-section {
    border: none;
    border-radius: var(--ai-seo-border-radius-lg);
    background-color: var(--ai-seo-bg-white);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    margin-bottom: var(--ai-seo-spacing-lg);
    position: relative;
}

/* --- Suggestion Header - Premium Design --- */
.suggestion-header {
    display: flex;
    align-items: center;
    gap: var(--ai-seo-spacing-md);
    padding: var(--ai-seo-spacing-md) var(--ai-seo-spacing-lg);
    background: linear-gradient(135deg, var(--ai-seo-bg-subtle) 0%, var(--ai-seo-bg-white) 100%);
    border-bottom: 2px solid var(--ai-seo-primary-border);
    position: relative;
}

.suggestion-header::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background-color: var(--ai-seo-primary-color);
}

.suggestion-header input[type="checkbox"] {
    margin: 0;
    transform: scale(1.3);
    accent-color: var(--ai-seo-primary-color);
    cursor: pointer;
    flex-shrink: 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.suggestion-label {
    font-weight: 700;
    font-size: 1.2em;
    color: var(--ai-seo-secondary-color);
    flex-grow: 1;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.suggestion-rating {
    font-size: 0.9em;
    font-weight: 700;
    background-color: var(--ai-seo-primary-color);
    color: white;
    padding: 5px 12px;
    border-radius: var(--ai-seo-border-radius-pill);
    flex-shrink: 0;
    white-space: nowrap;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: relative;
}

.suggestion-rating::before {
    content: "AI RATING";
    position: absolute;
    top: -18px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 9px;
    font-weight: 700;
    color: var(--ai-seo-text-lighter);
    white-space: nowrap;
    letter-spacing: 0.5px;
}

/* --- Suggestion Details Container --- */
.suggestion-details {
    padding: 0;
    line-height: 1.6;
    font-size: 1em;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    position: relative;
}

/* --- Current vs Suggested Comparison - Side by Side --- */
.suggestion-current,
.suggestion-suggested {
    padding: var(--ai-seo-spacing-lg);
    display: flex;
    flex-direction: column;
    position: relative;
    border-radius: var(--ai-seo-border-radius);
}

.suggestion-current {
    background-color: var(--ai-seo-bg-white);
    border: 1px solid var(--ai-seo-border-light-color);
    position: relative;
}

.suggestion-current::before {
    content: "CURRENT";
    position: absolute;
    top: 10px;
    left: 15px;
    font-size: 10px;
    font-weight: 700;
    color: var(--ai-seo-text-lighter);
    letter-spacing: 0.5px;
}

.suggestion-suggested {
    background-color: var(--ai-seo-primary-lightest);
    border: 1px solid var(--ai-seo-primary-border);
    position: relative;
}

.suggestion-suggested::before {
    content: "RECOMMENDED";
    position: absolute;
    top: 10px;
    left: 15px;
    font-size: 10px;
    font-weight: 700;
    color: var(--ai-seo-primary-color);
    letter-spacing: 0.5px;
}

/* --- Value Text Styling --- */
.suggestion-current > .value,
.suggestion-suggested > .value {
    margin-top: 20px;
    padding: var(--ai-seo-spacing-md);
    background-color: white;
    border-radius: var(--ai-seo-border-radius);
    border: 1px solid var(--ai-seo-border-light-color);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    font-size: 1.05em;
    line-height: 1.6;
    flex-grow: 1;
    position: relative;
    min-height: 100px; /* Ensure consistent height */
    display: flex;
    align-items: center;
}

.suggestion-suggested > .value {
    border: 1px solid var(--ai-seo-primary-border);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
    background-color: white;
}

/* Add a subtle highlight to the suggested value */
.suggestion-suggested > .value::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
                rgba(var(--ai-seo-primary-hue), 100%, 98%, 0.3) 0%,
                rgba(255, 255, 255, 0) 50%);
    pointer-events: none;
    border-radius: var(--ai-seo-border-radius);
}

/* --- Meta Information (Character Count & Score) --- */
.suggestion-current > .meta,
.suggestion-suggested > .meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--ai-seo-spacing-md);
    padding: 0 var(--ai-seo-spacing-md);
}

/* --- Character Count --- */
.suggestion-current .chars,
.suggestion-suggested .chars {
    font-size: 0.85em;
    color: var(--ai-seo-text-lighter);
    background-color: var(--ai-seo-bg-subtle);
    padding: 3px 10px;
    border-radius: var(--ai-seo-border-radius-pill);
    border: 1px solid var(--ai-seo-border-light-color);
}

/* --- Score Percentage - Ultra Professional Edition 2.0 --- */
.suggestion-current .score-percent,
.suggestion-suggested .score-percent {
    font-size: 1.1em;
    font-weight: 700;
    padding: 10px 16px;
    border-radius: var(--ai-seo-border-radius-pill);
    line-height: 1;
    text-align: center;
    min-width: 80px;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    z-index: 5;
    overflow: hidden;
    letter-spacing: 0.5px;
    text-transform: none;
    border: none;
}

/* Score background glow effect */
.suggestion-current .score-percent::after,
.suggestion-suggested .score-percent::after {
    content: "%";
    font-size: 0.7em;
    font-weight: 600;
    opacity: 0.9;
    margin-left: 2px;
    position: relative;
    top: -0.3em;
}

/* Score label */
.suggestion-current .score-percent::before,
.suggestion-suggested .score-percent::before {
    content: "CURRENT SCORE";
    position: absolute;
    top: -24px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 11px;
    font-weight: 700;
    color: var(--ai-seo-text-lighter);
    white-space: nowrap;
    letter-spacing: 0.8px;
    background-color: rgba(255, 255, 255, 0.95);
    padding: 4px 10px;
    border-radius: 6px;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.suggestion-suggested .score-percent::before {
    content: "SUGGESTED SCORE";
    background-color: rgba(255, 255, 255, 0.95);
    color: var(--ai-seo-primary-dark);
    font-weight: 800;
}

/* --- Score Gauge Design --- */
.score-gauge-wrapper {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* --- Score Color Coding - Ultra Professional Edition 2.0 --- */
.suggestion-current .score-percent.score-poor {
    background: linear-gradient(135deg, var(--ai-seo-error-color) 0%, var(--ai-seo-error-dark) 100%);
    color: white;
    border: none;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    box-shadow: 0 8px 20px rgba(var(--ai-seo-error-hue), 80%, 55%, 0.3);
    position: relative;
}

.suggestion-current .score-percent.score-poor::before {
    color: var(--ai-seo-error-dark);
    background-color: rgba(255, 255, 255, 0.95);
    font-weight: 700;
}

.suggestion-current .score-percent.score-fair {
    background: linear-gradient(135deg, var(--ai-seo-warning-color) 0%, var(--ai-seo-warning-dark) 100%);
    color: white;
    border: none;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    box-shadow: 0 8px 20px rgba(var(--ai-seo-warning-hue), 95%, 55%, 0.3);
    position: relative;
}

.suggestion-current .score-percent.score-fair::before {
    color: var(--ai-seo-warning-dark);
    background-color: rgba(255, 255, 255, 0.95);
    font-weight: 700;
}

.suggestion-current .score-percent.score-good {
    background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
    color: white;
    border: none;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3);
    position: relative;
}

.suggestion-current .score-percent.score-good::before {
    color: #388E3C;
    background-color: rgba(255, 255, 255, 0.95);
    font-weight: 700;
}

.suggestion-current .score-percent.score-excellent {
    background: linear-gradient(135deg, #2ECC71 0%, #27AE60 100%);
    color: white;
    border: none;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    box-shadow: 0 8px 20px rgba(46, 204, 113, 0.35);
    font-weight: 800;
    position: relative;
}

.suggestion-current .score-percent.score-excellent::before {
    color: #27AE60;
    background-color: rgba(255, 255, 255, 0.95);
    font-weight: 700;
}

/* --- Suggested Score - Ultra Premium Styling 2.0 --- */
.suggestion-suggested .score-percent {
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.12);
    font-size: 1.15em;
    padding: 12px 18px;
    transform: scale(1.05);
    font-weight: 700;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
    z-index: 10;
}

/* Add shine effect to suggested score */
.suggestion-suggested .score-percent::before {
    content: "RECOMMENDED SCORE";
    font-size: 11px;
    z-index: 2;
}

.suggestion-suggested .score-percent::after {
    content: "%";
    position: relative;
    display: inline-flex;
    align-items: center;
    z-index: 2;
    font-size: 0.7em;
    top: -0.3em;
}

/* Add shine animation effect */
.suggestion-suggested .score-percent::before {
    z-index: 2;
}

/* Shine animation */
@keyframes shine {
    0% {
        left: -100%;
        opacity: 0;
    }
    20% {
        opacity: 0.3;
    }
    100% {
        left: 100%;
        opacity: 0;
    }
}

/* Add shine element */
.suggestion-suggested .score-percent .shine-effect {
    position: absolute;
    top: 0;
    left: -100%;
    width: 60%;
    height: 100%;
    background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.5) 50%,
        rgba(255, 255, 255, 0) 100%
    );
    animation: shine 3s infinite;
    z-index: 1;
    pointer-events: none;
}

/* Show improvement percentage when hovering over the score */
.suggestion-suggested .score-percent:hover {
    transform: scale(1.15);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.25);
}

/* Add a subtle glow effect */
.suggestion-suggested .score-percent {
    position: relative;
}

.suggestion-suggested .score-percent::before {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

/* Add a subtle pulsating glow effect */
.suggestion-suggested .score-percent::after {
    content: "%";
}

.suggestion-suggested .score-percent {
    animation: subtle-glow 3s infinite alternate;
}

@keyframes subtle-glow {
    0% {
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.18);
    }
    100% {
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.25), 0 0 15px rgba(var(--ai-seo-primary-hue), 70%, 60%, 0.3);
    }
}

/* Enhanced Score Comparison Section 2.0 */
.ai-seo-score-comparison {
    text-align: center;
    padding: 16px;
    margin-top: 12px;
    font-size: 1em;
    font-weight: 600;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(250, 250, 250, 0.95) 100%);
    border-radius: var(--ai-seo-border-radius);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(10px);
    border: 1px solid var(--ai-seo-border-light-color);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    position: relative;
    overflow: hidden;
}

/* Add a subtle highlight border */
.ai-seo-score-comparison::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg,
        var(--ai-seo-primary-color) 0%,
        var(--ai-seo-primary-light) 50%,
        var(--ai-seo-primary-color) 100%);
    opacity: 0.8;
}

.ai-seo-score-comparison .dashicons {
    font-size: 20px;
    width: 20px;
    height: 20px;
    line-height: 1;
}

.ai-seo-score-comparison .score-improved {
    color: var(--ai-seo-success-color);
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 700;
    padding: 8px 16px;
    border-radius: var(--ai-seo-border-radius);
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.1) 0%, rgba(46, 204, 113, 0.15) 100%);
    border: 1px solid rgba(46, 204, 113, 0.2);
    box-shadow: 0 3px 8px rgba(46, 204, 113, 0.1);
    position: relative;
    overflow: hidden;
}

.ai-seo-score-comparison .score-improved::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, rgba(46, 204, 113, 0.5) 0%, rgba(46, 204, 113, 0.8) 100%);
    opacity: 0.8;
}

.ai-seo-score-comparison .score-improved .dashicons {
    background: linear-gradient(135deg, var(--ai-seo-success-color) 0%, var(--ai-seo-success-dark) 100%);
    color: white;
    border-radius: 50%;
    padding: 3px;
    font-size: 16px;
    box-shadow: 0 3px 8px rgba(46, 204, 113, 0.3);
}

.ai-seo-score-comparison .score-decreased {
    color: var(--ai-seo-error-color);
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 700;
    padding: 8px 16px;
    border-radius: var(--ai-seo-border-radius);
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.1) 0%, rgba(231, 76, 60, 0.15) 100%);
    border: 1px solid rgba(231, 76, 60, 0.2);
    box-shadow: 0 3px 8px rgba(231, 76, 60, 0.1);
    position: relative;
    overflow: hidden;
}

.ai-seo-score-comparison .score-decreased::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, rgba(231, 76, 60, 0.5) 0%, rgba(231, 76, 60, 0.8) 100%);
    opacity: 0.8;
}

.ai-seo-score-comparison .score-decreased .dashicons {
    background: linear-gradient(135deg, var(--ai-seo-error-color) 0%, var(--ai-seo-error-dark) 100%);
    color: white;
    border-radius: 50%;
    padding: 3px;
    font-size: 16px;
    box-shadow: 0 3px 8px rgba(231, 76, 60, 0.3);
}

.ai-seo-score-comparison .score-unchanged {
    color: var(--ai-seo-text-light);
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 700;
    padding: 8px 16px;
    border-radius: var(--ai-seo-border-radius);
    background: linear-gradient(135deg, rgba(149, 165, 166, 0.1) 0%, rgba(149, 165, 166, 0.15) 100%);
    border: 1px solid rgba(149, 165, 166, 0.2);
    box-shadow: 0 3px 8px rgba(149, 165, 166, 0.1);
    position: relative;
    overflow: hidden;
}

.ai-seo-score-comparison .score-unchanged::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, rgba(149, 165, 166, 0.5) 0%, rgba(149, 165, 166, 0.8) 100%);
    opacity: 0.8;
}

.ai-seo-score-comparison .score-unchanged .dashicons {
    background: linear-gradient(135deg, var(--ai-seo-text-light) 0%, var(--ai-seo-text-dark) 100%);
    color: white;
    border-radius: 50%;
    padding: 3px;
    font-size: 16px;
    box-shadow: 0 3px 8px rgba(149, 165, 166, 0.3);
}

/* Score Badge - Ultra Professional Edition 2.0 */
.score-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 34px;
    height: 34px;
    padding: 0 10px;
    border-radius: 17px;
    font-size: 15px;
    font-weight: 800;
    margin-left: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.18);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    overflow: hidden;
    letter-spacing: 0.5px;
}

.score-improved .score-badge {
    background: linear-gradient(135deg, #2ECC71 0%, #27AE60 100%);
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.score-improved .score-badge:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 15px rgba(46, 204, 113, 0.3);
}

.score-decreased .score-badge {
    background: linear-gradient(135deg, var(--ai-seo-error-color) 0%, var(--ai-seo-error-dark) 100%);
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.score-decreased .score-badge:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 15px rgba(231, 76, 60, 0.3);
}

/* Add a subtle shine effect to the badge */
.score-badge::after {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 50%;
    height: 100%;
    background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.3) 50%,
        rgba(255, 255, 255, 0) 100%
    );
    animation: shine 3s infinite;
    z-index: 1;
}

/* Mini Score Display for Manual Process Section */
.mini-score-percent {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 30px;
    height: 24px;
    padding: 0 6px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 700;
    position: relative;
    overflow: hidden;
    color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.mini-score-percent::after {
    content: "%";
    font-size: 9px;
    opacity: 0.8;
    margin-left: 1px;
}

.mini-score-percent.score-poor {
    background: linear-gradient(135deg, var(--ai-seo-error-color) 0%, var(--ai-seo-error-dark) 100%);
}

.mini-score-percent.score-fair {
    background: linear-gradient(135deg, var(--ai-seo-warning-color) 0%, var(--ai-seo-warning-dark) 100%);
}

.mini-score-percent.score-good {
    background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
}

.mini-score-percent.score-excellent {
    background: linear-gradient(135deg, #2ECC71 0%, #27AE60 100%);
    position: relative;
}

/* Mini shine effect */
.mini-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 50%;
    height: 100%;
    background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.4) 50%,
        rgba(255, 255, 255, 0) 100%
    );
    animation: shine 3s infinite;
    z-index: 1;
}

/* Schema Markup Controls */
.ai-seo-schema-markup-controls {
    background-color: var(--ai-seo-bg-white);
    border: var(--ai-seo-border-width) solid var(--ai-seo-primary-lighter);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border-radius: var(--ai-seo-border-radius-lg);
    padding: 15px 20px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    position: relative;
}

.ai-seo-schema-markup-controls::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, var(--ai-seo-primary-color), var(--ai-seo-accent-color));
    border-radius: var(--ai-seo-border-radius-lg) 0 0 var(--ai-seo-border-radius-lg);
}

.ai-seo-schema-markup-controls label {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    color: var(--ai-seo-secondary-color);
    cursor: pointer;
}

.ai-seo-schema-markup-controls input[type="checkbox"] {
    width: 22px;
    height: 22px;
    min-width: 22px;
    min-height: 22px;
    accent-color: var(--ai-seo-primary-color);
    cursor: pointer;
    position: relative;
    border: 2px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    background-color: white;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    outline: none;
    margin: 0;
}

.ai-seo-schema-markup-controls input[type="checkbox"]:checked {
    background-color: var(--ai-seo-primary-color);
    border-color: var(--ai-seo-primary-color);
}

.ai-seo-schema-markup-controls input[type="checkbox"]:checked::after {
    content: '';
    position: absolute;
    left: 7px;
    top: 3px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.ai-seo-schema-markup-controls input[type="checkbox"]:hover {
    border-color: var(--ai-seo-primary-color);
    box-shadow: 0 0 0 3px rgba(var(--ai-seo-primary-hue), var(--ai-seo-primary-saturation), var(--ai-seo-primary-lightness), 0.2);
}

.ai-seo-schema-markup-controls .dashicons {
    color: var(--ai-seo-primary-color);
    font-size: 20px;
    width: 20px;
    height: 20px;
}

/* Schema markup checkbox in post list */
.ai-seo-post-list .schema-markup-checkbox {
    margin-left: 8px;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.ai-seo-post-list .schema-markup-checkbox input[type="checkbox"] {
    width: 16px;
    height: 16px;
    min-width: 16px;
    min-height: 16px;
    margin: 0;
    position: relative;
    top: -1px;
}

.ai-seo-post-list .schema-markup-checkbox input[type="checkbox"]:checked::after {
    left: 5px;
    top: 2px;
    width: 3px;
    height: 7px;
}

.ai-seo-post-list .schema-markup-checkbox .dashicons {
    color: var(--ai-seo-primary-color);
    font-size: 16px;
    width: 16px;
    height: 16px;
}

@media screen and (max-width: 782px) {
    .ai-seo-schema-markup-controls {
        flex-direction: column;
        align-items: flex-start;
        padding: 12px 15px 12px 20px;
    }

    .ai-seo-schema-markup-controls label {
        font-size: 14px;
    }

    .ai-seo-post-list .schema-markup-checkbox {
        margin-left: 5px;
    }

    .ai-seo-post-list .schema-markup-checkbox input[type="checkbox"] {
        width: 14px;
        height: 14px;
        min-width: 14px;
        min-height: 14px;
    }

    .ai-seo-post-list .schema-markup-checkbox input[type="checkbox"]:checked::after {
        left: 4px;
        top: 1px;
        width: 3px;
        height: 6px;
    }
}

.ai-seo-schema-markup-controls .dashicons {
    color: var(--ai-seo-primary-color);
    font-size: 18px;
}

/* Suggested Score Colors - Ultra Professional Edition 2.0 */
.suggestion-suggested .score-percent.score-poor {
    background: linear-gradient(135deg, var(--ai-seo-error-color) 0%, var(--ai-seo-error-dark) 100%);
    color: white;
    border: none;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    box-shadow: 0 8px 20px rgba(var(--ai-seo-error-hue), 80%, 55%, 0.3);
    position: relative;
}

.suggestion-suggested .score-percent.score-poor::before {
    color: var(--ai-seo-error-dark);
    background-color: rgba(255, 255, 255, 0.95);
    font-weight: 700;
}

.suggestion-suggested .score-percent.score-fair {
    background: linear-gradient(135deg, var(--ai-seo-warning-color) 0%, var(--ai-seo-warning-dark) 100%);
    color: white;
    border: none;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    box-shadow: 0 8px 20px rgba(var(--ai-seo-warning-hue), 95%, 55%, 0.3);
    position: relative;
}

.suggestion-suggested .score-percent.score-fair::before {
    color: var(--ai-seo-warning-dark);
    background-color: rgba(255, 255, 255, 0.95);
    font-weight: 700;
}

.suggestion-suggested .score-percent.score-good {
    background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
    color: white;
    border: none;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3);
    position: relative;
}

.suggestion-suggested .score-percent.score-good::before {
    color: #388E3C;
    background-color: rgba(255, 255, 255, 0.95);
    font-weight: 700;
}

.suggestion-suggested .score-percent.score-excellent {
    background: linear-gradient(135deg, #2ECC71 0%, #27AE60 100%);
    color: white;
    border: none;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    box-shadow: 0 8px 20px rgba(46, 204, 113, 0.35);
    position: relative;
    overflow: hidden;
    animation: subtle-pulse 3s infinite alternate;
}

.suggestion-suggested .score-percent.score-excellent::before {
    color: #27AE60;
    background-color: rgba(255, 255, 255, 0.95);
    font-weight: 700;
}

/* Shine effect for excellent scores - already defined above */

.suggestion-suggested .score-percent.score-excellent::after {
    content: "%";
    position: relative;
    z-index: 2;
}

/* Add shine effect */
.suggestion-suggested .score-percent.score-excellent .shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 50%;
    height: 100%;
    background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.4) 50%,
        rgba(255, 255, 255, 0) 100%
    );
    animation: shine 3s infinite;
    z-index: 1;
}

/* Pulse animation for excellent scores */
@keyframes ai-seo-pulse-score {
    0% { transform: scale(1.05); box-shadow: 0 8px 20px rgba(46, 204, 113, 0.35); }
    50% { transform: scale(1.12); box-shadow: 0 12px 30px rgba(46, 204, 113, 0.5); }
    100% { transform: scale(1.05); box-shadow: 0 8px 20px rgba(46, 204, 113, 0.35); }
}

.suggestion-suggested .score-percent.score-excellent {
    animation: ai-seo-pulse-score 3s infinite;
}

/* Highlight for better scores when auto-selecting */
.highlight-better-score {
    box-shadow: 0 0 0 3px rgba(46, 204, 113, 0.5) !important;
    animation: ai-seo-highlight-pulse 2s infinite !important;
}

@keyframes ai-seo-highlight-pulse {
    0% { box-shadow: 0 8px 20px rgba(46, 204, 113, 0.35), 0 0 0 3px rgba(46, 204, 113, 0.5); }
    50% { box-shadow: 0 12px 30px rgba(46, 204, 113, 0.5), 0 0 0 5px rgba(46, 204, 113, 0.7); }
    100% { box-shadow: 0 8px 20px rgba(46, 204, 113, 0.35), 0 0 0 3px rgba(46, 204, 113, 0.5); }
}

@keyframes ai-seo-pulse {
    0% { transform: scale(1.05); }
    50% { transform: scale(1.12); }
    100% { transform: scale(1.05); }
}

/* --- Visual Comparison Arrow --- */
.suggestion-details::after {
    content: "\f344"; /* Dashicon: arrow-right */
    font-family: dashicons;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 24px;
    color: var(--ai-seo-primary-color);
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background-color: white;
    border-radius: 50%;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    z-index: 10;
    border: 1px solid var(--ai-seo-primary-border);
}

/* --- Responsive Design for Mobile --- */
@media screen and (max-width: 782px) {
    .suggestion-details {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .suggestion-details::after {
        top: calc(50% - 30px);
        transform: translate(-50%, -50%) rotate(90deg);
        width: 36px;
        height: 36px;
        line-height: 36px;
        font-size: 20px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        background-color: var(--ai-seo-primary-color);
        color: white;
        border: none;
    }

    .suggestion-current::before,
    .suggestion-suggested::before {
        top: 8px;
        left: 12px;
        font-size: 9px;
    }

    .suggestion-current > .value,
    .suggestion-suggested > .value {
        margin-top: 18px;
        padding: var(--ai-seo-spacing-sm);
        font-size: 1em;
        min-height: 80px;
    }

    /* Improved mobile score display */
    .suggestion-current .score-percent,
    .suggestion-suggested .score-percent {
        font-size: 1.1em;
        padding: 10px 16px;
        min-width: 70px;
        margin: 0 auto;
    }

    .suggestion-suggested .score-percent {
        font-size: 1.2em;
        padding: 12px 18px;
        transform: scale(1.05);
    }

    .suggestion-current .score-percent::before,
    .suggestion-suggested .score-percent::before {
        top: -18px;
        font-size: 9px;
        padding: 2px 6px;
    }

    /* Improved mobile score comparison */
    .ai-seo-score-comparison {
        padding: 8px;
        margin: 10px auto 0;
        max-width: 90%;
        font-size: 0.9em;
    }

    .ai-seo-score-comparison .dashicons {
        font-size: 16px;
        width: 16px;
        height: 16px;
    }

    /* Better mobile meta layout */
    .suggestion-current > .meta,
    .suggestion-suggested > .meta {
        flex-direction: column;
        gap: 10px;
        align-items: center;
    }

    .suggestion-current .char-count,
    .suggestion-suggested .char-count {
        margin-bottom: 5px;
    }
}

/* --- Extra Small Screens --- */
@media screen and (max-width: 600px) {
    .suggestion-header {
        padding: var(--ai-seo-spacing-sm) var(--ai-seo-spacing-md);
    }

    .suggestion-label {
        font-size: 1.1em;
    }

    .suggestion-rating {
        font-size: 0.85em;
        padding: 4px 10px;
    }

    .suggestion-current,
    .suggestion-suggested {
        padding: var(--ai-seo-spacing-md);
    }
}

/* --- Print Styles for Review Suggestions --- */
@media print {
    .suggestion-details {
        grid-template-columns: 1fr;
        gap: 1cm;
    }

    .suggestion-details::after {
        display: none;
    }

    .suggestion-current,
    .suggestion-suggested {
        padding: 0.5cm;
        border: 1px solid #ccc;
        page-break-inside: avoid;
    }

    .suggestion-current > .value,
    .suggestion-suggested > .value {
        border: 1px solid #eee;
        box-shadow: none;
    }

    .score-percent {
        background-color: #eee !important;
        color: #333 !important;
        border: 1px solid #ccc !important;
        text-shadow: none !important;
        box-shadow: none !important;
        animation: none !important;
    }
}

/* ==========================================================================
   == Enhanced Comparison Row Styles for Review AI Suggestions ==
   ========================================================================== */

/* --- Comparison Row (Current vs. Suggested) - Enhanced Design --- */
.ai-seo-comparison-row {
    margin-bottom: var(--ai-seo-spacing-lg);
    display: grid;
    grid-template-columns: minmax(180px, max-content) 1fr;
    gap: var(--ai-seo-spacing-md);
    align-items: start;
    padding: var(--ai-seo-spacing-lg);
    border-radius: var(--ai-seo-border-radius-lg);
    background-color: var(--ai-seo-bg-white);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--ai-seo-border-light-color);

    /* Mobile-friendly design */
    max-width: 100%;
    overflow-wrap: break-word;
    word-wrap: break-word;
    hyphens: auto;
}

.ai-seo-comparison-row:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
    border-color: var(--ai-seo-primary-border);
}

.ai-seo-comparison-row:last-child {
    margin-bottom: 0;
}

/* Element-specific styling */
.ai-seo-comparison-row.element-title {
    background: linear-gradient(135deg, var(--ai-seo-bg-white) 0%, var(--ai-seo-primary-lighter) 100%);
    border-color: var(--ai-seo-primary-border);
}

.ai-seo-comparison-row.element-description {
    background: linear-gradient(135deg, var(--ai-seo-bg-white) 0%, var(--ai-seo-info-bg) 100%);
    border-color: var(--ai-seo-info-border);
}

.ai-seo-comparison-row.element-h1 {
    background: linear-gradient(135deg, var(--ai-seo-bg-white) 0%, var(--ai-seo-bg-subtle) 100%);
}

/* --- Label Column - Enhanced --- */
.ai-seo-comparison-label {
    grid-column: 1;
    display: flex;
    align-items: center;
    gap: var(--ai-seo-spacing-sm);
    padding: var(--ai-seo-spacing-md);
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: var(--ai-seo-border-radius);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    position: relative;
    border: 1px solid var(--ai-seo-border-light-color);
}

.ai-seo-comparison-label strong {
    font-weight: 700;
    color: var(--ai-seo-secondary-color);
    font-size: 1.1em;
    display: flex;
    align-items: center;
    gap: var(--ai-seo-spacing-sm);
}

.ai-seo-comparison-label .dashicons {
    color: var(--ai-seo-primary-color);
    font-size: 18px;
    background-color: rgba(255, 255, 255, 0.8);
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.ai-seo-comparison-row.element-description .ai-seo-comparison-label .dashicons {
    color: var(--ai-seo-info-color);
}

.ai-seo-comparison-label .ai-seo-rating {
    margin-left: var(--ai-seo-spacing-sm);
    font-size: 0.9em;
    font-weight: 700;
    color: var(--ai-seo-primary-text);
    background: linear-gradient(135deg, var(--ai-seo-primary-color) 0%, var(--ai-seo-primary-dark) 100%);
    padding: 5px 12px;
    border-radius: var(--ai-seo-border-radius-pill);
    border: none;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: var(--ai-seo-spacing-sm);
    margin-top: var(--ai-seo-spacing-sm);
}

.ai-seo-comparison-row.element-description .ai-seo-comparison-label .ai-seo-rating {
    background: linear-gradient(135deg, var(--ai-seo-info-color) 0%, var(--ai-seo-info-dark) 100%);
}

.ai-seo-comparison-label .ai-seo-rating::before {
    content: "\f529"; /* Star icon */
    font-family: dashicons;
    font-size: 1.1em;
    background-color: rgba(255, 255, 255, 0.2);
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.ai-seo-comparison-label input[type="checkbox"] {
    margin: 0;
    transform: scale(1.3);
    accent-color: var(--ai-seo-primary-color);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border-radius: 3px;
    width: 20px;
    height: 20px;
    cursor: pointer;
}

.ai-seo-comparison-label .ai-seo-no-apply-icon {
    color: var(--ai-seo-text-lighter);
    font-size: 18px;
    cursor: help;
    background-color: var(--ai-seo-bg-subtle);
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

/* --- Values Column - Enhanced Comparison Design --- */
.ai-seo-comparison-values {
    grid-column: 2;
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: var(--ai-seo-spacing-md);
    align-items: stretch;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
}

.ai-seo-value {
    padding: var(--ai-seo-spacing-md);
    border-radius: var(--ai-seo-border-radius-lg);
    border: var(--ai-seo-border-width) solid var(--ai-seo-border-light-color);
    background-color: var(--ai-seo-bg-white);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.ai-seo-value:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
    z-index: 5;
}

.ai-seo-value::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    transition: all 0.3s ease;
}

/* Value Header with Label */
.ai-seo-value .value-header {
    margin: -var(--ai-seo-spacing-md);
    margin-bottom: var(--ai-seo-spacing-md);
    padding: var(--ai-seo-spacing-sm) var(--ai-seo-spacing-md);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(240, 240, 240, 0.8) 100%);
    border-bottom: 1px solid var(--ai-seo-border-light-color);
    display: flex;
    align-items: center;
    justify-content: center;
}

.ai-seo-value .value-label {
    font-size: 0.9em;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    text-align: center;
}

.ai-seo-value .value-content {
    line-height: 1.7;
    word-break: break-word;
    display: block;
    margin-bottom: var(--ai-seo-spacing-md);
    padding: var(--ai-seo-spacing-md);
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: var(--ai-seo-border-radius);
    border: 1px solid var(--ai-seo-border-light-color);
    flex-grow: 1;
    font-size: 1.05em;
    max-width: 100%;
    overflow-wrap: break-word;
    word-wrap: break-word;
    hyphens: auto;
}

/* Current Value Styling */
.ai-seo-value.current {
    background: linear-gradient(135deg, var(--ai-seo-bg-white) 0%, var(--ai-seo-bg-subtle) 100%);
}

.ai-seo-value.current::before {
    background: linear-gradient(90deg, var(--ai-seo-secondary-light) 0%, var(--ai-seo-secondary-color) 100%);
}

.ai-seo-value.current .value-header {
    background: linear-gradient(135deg, var(--ai-seo-bg-subtle) 0%, rgba(220, 220, 220, 0.8) 100%);
}

.ai-seo-value.current .value-label {
    color: var(--ai-seo-secondary-color);
}

/* Suggested Value Styling */
.ai-seo-value.suggestion {
    background: linear-gradient(135deg, var(--ai-seo-bg-white) 0%, var(--ai-seo-primary-lighter) 100%);
    border-color: var(--ai-seo-primary-border);
}

.ai-seo-value.suggestion::before {
    background: linear-gradient(90deg, var(--ai-seo-primary-color) 0%, var(--ai-seo-primary-dark) 100%);
    height: 5px;
}

.ai-seo-value.suggestion .value-header {
    background: linear-gradient(135deg, var(--ai-seo-primary-lighter) 0%, rgba(230, 230, 250, 0.8) 100%);
    border-bottom-color: var(--ai-seo-primary-border);
}

.ai-seo-value.suggestion .value-label {
    color: var(--ai-seo-primary-dark);
}

.ai-seo-value.suggestion .value-content {
    background-color: rgba(var(--ai-seo-primary-hue), 76%, 95%, 0.3);
    border-color: var(--ai-seo-primary-border);
}

/* Comparison Arrow */
.comparison-arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--ai-seo-primary-color);
}

.comparison-arrow .dashicons {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--ai-seo-bg-white);
    border-radius: 50%;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--ai-seo-primary-border);
    transition: all 0.3s ease;
}

.ai-seo-comparison-row:hover .comparison-arrow .dashicons {
    transform: scale(1.1);
    box-shadow: 0 6px 15px rgba(var(--ai-seo-primary-hue), 76%, 58%, 0.2);
}

/* Value Meta Information */
.ai-seo-value .value-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--ai-seo-spacing-sm) var(--ai-seo-spacing-md);
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: var(--ai-seo-border-radius);
    border: 1px solid var(--ai-seo-border-light-color);
    font-size: 0.9em;
}

.ai-seo-value.suggestion .value-meta {
    background-color: rgba(var(--ai-seo-primary-hue), 76%, 95%, 0.3);
    border-color: var(--ai-seo-primary-border);
}

/* Score Comparison */
.ai-seo-score-comparison {
    grid-column: 1 / span 2;
    text-align: center;
    margin-top: var(--ai-seo-spacing-sm);
    font-weight: 600;
    padding: var(--ai-seo-spacing-sm) var(--ai-seo-spacing-md);
    border-radius: var(--ai-seo-border-radius-pill);
    display: inline-block;
    width: fit-content;
    margin-left: auto;
    margin-right: auto;
}

.score-comparison {
    padding: 6px 14px;
    border-radius: var(--ai-seo-border-radius-pill);
    font-weight: 600;
    font-size: 0.9em;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
    display: inline-flex;
    align-items: center;
    gap: var(--ai-seo-spacing-sm);
}

.score-comparison::before {
    font-family: dashicons;
    font-size: 16px;
    margin-right: 4px;
}

.score-comparison.score-improved {
    background: linear-gradient(135deg, var(--ai-seo-success-color) 0%, var(--ai-seo-success-dark) 100%);
    color: white;
}

.score-comparison.score-improved::before {
    content: "\f142"; /* Arrow up */
}

.score-comparison.score-decreased {
    background: linear-gradient(135deg, var(--ai-seo-error-color) 0%, var(--ai-seo-error-dark) 100%);
    color: white;
}

.score-comparison.score-decreased::before {
    content: "\f140"; /* Arrow down */
}

.score-comparison.score-unchanged {
    background: linear-gradient(135deg, var(--ai-seo-text-lighter) 0%, var(--ai-seo-text-light) 100%);
    color: white;
}

.score-comparison.score-unchanged::before {
    content: "\f460"; /* Minus */
}

/* ==========================================================================
   == Stellar Responsive Design System - Premium Mobile Experience ==
   ========================================================================== */

/* --- Premium Responsive Breakpoints --- */
/*
   - Mobile Small: 320px - 479px
   - Mobile Large: 480px - 767px
   - Tablet: 768px - 1023px
   - Desktop Small: 1024px - 1279px
   - Desktop Large: 1280px+
*/

/* Premium Responsive Design - Core Adjustments */
@media screen and (max-width: 1279px) {
    /* Slightly reduce spacing on smaller desktops */
    .ai-seo-optimizer-wrap {
        padding: var(--ai-seo-spacing-lg);
    }

    /* Adjust button sizes for better touch targets */
    .ai-seo-optimizer-wrap .button {
        padding: 12px 20px;
        min-height: 44px;
    }

    /* Enhance card hover effects */
    .ai-seo-box:hover,
    .ai-seo-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--ai-seo-shadow-xl);
    }
}

@media screen and (max-width: 1023px) {
    /* Tablet Optimizations - Premium Design */
    .ai-seo-optimizer-wrap {
        margin: var(--ai-seo-spacing-md) 15px var(--ai-seo-spacing-md) 0;
        padding: var(--ai-seo-spacing-lg);
        border-radius: var(--ai-seo-border-radius-lg);
    }

    /* Refined header size */
    .ai-seo-header-title h1 {
        font-size: 24px;
        letter-spacing: -0.01em;
    }

    /* Enhanced header appearance */
    .ai-seo-header {
        background: var(--ai-seo-gradient-header);
        border-radius: var(--ai-seo-border-radius);
        box-shadow: var(--ai-seo-shadow-md);
    }

    .ai-seo-header-title {
        color: white;
    }

    /* Refined tab padding */
    .ai-seo-nav-tabs .nav-tab {
        padding: 14px var(--ai-seo-spacing-md);
        border-radius: var(--ai-seo-border-radius) var(--ai-seo-border-radius) 0 0;
    }

    /* Enhanced tab hover effects */
    .ai-seo-nav-tabs .nav-tab:hover {
        transform: translateY(-3px);
    }

    /* Settings table adjustments */
    #tab-settings .form-table th {
        width: 220px;
        padding-right: var(--ai-seo-spacing-md);
    }

    /* Improve form readability */
    .form-table td input[type="text"],
    .form-table td input[type="password"],
    .form-table td select,
    .form-table td textarea {
        width: 100%;
        max-width: 100%;
        font-size: 15px;
        padding: 10px;
        border-radius: var(--ai-seo-border-radius-sm);
    }
}

@media screen and (max-width: 782px) {
    /* Mobile Large & WordPress Admin Breakpoint - Premium Design */
    .ai-seo-optimizer-wrap {
        margin: var(--ai-seo-spacing-md) 12px;
        padding: var(--ai-seo-spacing-lg);
        border-radius: var(--ai-seo-border-radius-lg);
        box-shadow: var(--ai-seo-shadow-lg);
        border-top: 3px solid var(--ai-seo-primary-color);
    }

    /* Header becomes stacked - Premium Design */
    .ai-seo-header {
        flex-direction: column;
        align-items: flex-start;
        padding: var(--ai-seo-spacing-lg);
        margin: calc(-1 * var(--ai-seo-spacing-lg));
        margin-bottom: var(--ai-seo-spacing-lg);
        background: var(--ai-seo-gradient-header);
        border-radius: var(--ai-seo-border-radius-lg) var(--ai-seo-border-radius-lg) 0 0;
        box-shadow: var(--ai-seo-shadow-md);
    }

    .ai-seo-header-title {
        color: white;
    }

    .ai-seo-header-title h1 {
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .ai-seo-header-meta {
        width: 100%;
        justify-content: flex-start;
        margin-top: var(--ai-seo-spacing-sm);
    }

    .ai-seo-header-meta .ai-seo-version {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    /* Tabs become vertical for better mobile UX */
    .ai-seo-tabs-container {
        margin-top: var(--ai-seo-spacing-lg);
    }

    .ai-seo-nav-tabs {
        display: flex;
        flex-direction: column;
        padding: 0;
        gap: 1px;
        border-bottom: none;
        border-radius: var(--ai-seo-border-radius-lg);
        overflow: hidden;
        box-shadow: var(--ai-seo-shadow-md);
    }

    .ai-seo-nav-tabs .nav-tab {
        flex: 1;
        padding: 14px var(--ai-seo-spacing-md);
        font-size: 15px;
        white-space: nowrap;
        margin: 0;
        border-radius: 0;
        border: none;
        border-bottom: var(--ai-seo-border-width) solid var(--ai-seo-border-color);
        background: linear-gradient(to right, var(--ai-seo-bg-white), var(--ai-seo-bg-subtle));
        justify-content: flex-start;
    }

    .ai-seo-nav-tabs .nav-tab:last-child {
        border-bottom: none;
    }

    .ai-seo-nav-tabs .nav-tab .dashicons {
        font-size: 18px;
        width: 24px;
        height: 24px;
        margin-right: var(--ai-seo-spacing-sm);
    }

    .ai-seo-nav-tabs .nav-tab-active,
    .ai-seo-nav-tabs .nav-tab-active:hover {
        background: linear-gradient(to right, var(--ai-seo-primary-lighter), var(--ai-seo-bg-white));
        border-left: 3px solid var(--ai-seo-primary-color);
        padding-left: calc(var(--ai-seo-spacing-md) - 3px);
        transform: none;
        box-shadow: none;
    }

    .ai-seo-nav-tabs .nav-tab-active::after {
        display: none;
    }

    /* Tab content padding adjustment - Premium Design */
    .ai-seo-tabs-container > .tab-content {
        padding: var(--ai-seo-spacing-lg);
        margin-top: var(--ai-seo-spacing-md);
        border-radius: var(--ai-seo-border-radius-lg);
        box-shadow: var(--ai-seo-shadow-md);
    }

    /* Settings table becomes stacked - Premium Design */
    #tab-settings .form-table,
    #tab-settings .form-table tbody,
    #tab-settings .form-table tr,
    #tab-settings .form-table th,
    #tab-settings .form-table td {
        display: block;
        width: 100%;
        padding: 0;
        margin: 0;
    }

    #tab-settings .form-table th {
        padding: var(--ai-seo-spacing-md) 0 var(--ai-seo-spacing-xs) 0;
        font-weight: 600;
        color: var(--ai-seo-primary-color);
        font-size: 16px;
    }

    #tab-settings .form-table td {
        padding: 0 0 var(--ai-seo-spacing-lg) 0;
    }

    /* Form controls - Premium Mobile Design */
    input[type="text"],
    input[type="password"],
    input[type="email"],
    input[type="number"],
    input[type="search"],
    input[type="tel"],
    input[type="url"],
    select,
    textarea {
        font-size: 16px !important; /* Prevents iOS zoom on focus */
        padding: 12px;
        border-radius: var(--ai-seo-border-radius-sm);
        box-shadow: var(--ai-seo-shadow-sm);
        border-color: var(--ai-seo-border-color);
    }

    input[type="text"]:focus,
    input[type="password"]:focus,
    input[type="email"]:focus,
    input[type="number"]:focus,
    input[type="search"]:focus,
    input[type="tel"]:focus,
    input[type="url"]:focus,
    select:focus,
    textarea:focus {
        box-shadow: var(--ai-seo-shadow-focus-ring);
        border-color: var(--ai-seo-primary-color);
    }

    /* Button groups stack on mobile - Premium Design */
    .ai-seo-button-group {
        flex-direction: column;
        align-items: stretch;
        gap: var(--ai-seo-spacing-md);
        width: 100%;
    }

    .ai-seo-button-group .button {
        margin: 0;
        justify-content: center;
        min-height: 48px;
        font-size: 15px;
        padding: 12px 20px;
    }

    /* Enhanced button appearance */
    .ai-seo-optimizer-wrap .button.button-primary,
    .ai-seo-optimizer-wrap .button.button-secondary,
    .ai-seo-optimizer-wrap .button.button-success {
        box-shadow: var(--ai-seo-shadow-lg);
    }

    /* Comparison section adjustments */
    .ai-seo-comparison-row {
        grid-template-columns: 1fr;
    }

    .ai-seo-comparison-values {
        grid-template-columns: 1fr;
        grid-column: 1;
    }

    .comparison-arrow {
        transform: rotate(90deg);
        margin: var(--ai-seo-spacing-sm) 0;
        justify-self: center;
    }

    .ai-seo-score-comparison {
        grid-column: 1;
    }

    /* Make checkboxes larger for touch */
    .ai-seo-comparison-label input[type="checkbox"],
    .ai-seo-links-list input[type="checkbox"] {
        transform: scale(1.5);
        margin: 0 var(--ai-seo-spacing-sm);
        min-width: 24px;
        min-height: 24px;
    }

    /* Adjust status messages */
    .ai-seo-status-text, .ai-seo-ajax-result {
        display: block;
        margin: var(--ai-seo-spacing-md) 0 0 0;
        text-align: center;
    }
}

@media screen and (max-width: 479px) {
    /* Mobile Small Optimizations - Premium Design */
    .ai-seo-optimizer-wrap {
        margin: var(--ai-seo-spacing-sm) 8px;
        padding: var(--ai-seo-spacing-md);
        font-size: 14px;
        border-radius: var(--ai-seo-border-radius);
    }

    .ai-seo-header-title h1 {
        font-size: 20px;
        line-height: 1.3;
    }

    .ai-seo-header-icon {
        font-size: 28px;
        margin-right: var(--ai-seo-spacing-sm);
    }

    .ai-seo-header {
        padding: var(--ai-seo-spacing-md);
        margin: calc(-1 * var(--ai-seo-spacing-md));
        margin-bottom: var(--ai-seo-spacing-md);
    }

    /* Refined tab styling for small screens */
    .ai-seo-nav-tabs .nav-tab {
        padding: 12px var(--ai-seo-spacing-md);
        font-size: 14px;
    }

    .ai-seo-nav-tabs .nav-tab .dashicons {
        font-size: 18px;
        margin-right: var(--ai-seo-spacing-xs);
    }

    /* Tab content adjustments */
    .ai-seo-tabs-container > .tab-content {
        padding: var(--ai-seo-spacing-md);
        border-radius: var(--ai-seo-border-radius);
    }

    /* Optimized buttons for very small screens */
    .ai-seo-optimizer-wrap .button {
        padding: 10px 16px;
        font-size: 14px;
        border-radius: var(--ai-seo-border-radius);
        min-height: 44px;
    }

    /* Button icons */
    .ai-seo-optimizer-wrap .button .dashicons {
        font-size: 18px;
        width: 18px;
        height: 18px;
    }

    /* Enhanced notices */
    .ai-seo-admin-notice, .ai-seo-info-box {
        padding: var(--ai-seo-spacing-md);
        flex-direction: column;
        align-items: flex-start;
        border-radius: var(--ai-seo-border-radius);
        box-shadow: var(--ai-seo-shadow-md);
        border-left: 3px solid var(--ai-seo-primary-color);
    }

    .ai-seo-admin-notice.notice-warning,
    .ai-seo-info-box.notice-warning {
        border-left-color: var(--ai-seo-warning-color);
    }

    .ai-seo-admin-notice.notice-error,
    .ai-seo-info-box.notice-error {
        border-left-color: var(--ai-seo-error-color);
    }

    .ai-seo-admin-notice.notice-success,
    .ai-seo-info-box.notice-success {
        border-left-color: var(--ai-seo-success-color);
    }

    /* Optimize list container for small screens - Premium Design */
    .ai-seo-list-container {
        border-radius: var(--ai-seo-border-radius);
        box-shadow: var(--ai-seo-shadow-md);
        border: 1px solid var(--ai-seo-border-color);
        overflow: hidden;
    }

    .ai-seo-list-container .select-all-controls,
    .ai-seo-pagination {
        padding: 12px 16px;
        flex-wrap: wrap;
        gap: 10px;
        background: linear-gradient(to right, var(--ai-seo-primary-lighter), var(--ai-seo-bg-white));
        border-bottom: 1px solid var(--ai-seo-border-color);
    }

    .ai-seo-list-container .select-all-controls label {
        font-size: 14px;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: var(--ai-seo-spacing-xs);
    }

    /* Enhanced post list items */
    .ai-seo-post-list {
        padding: var(--ai-seo-spacing-sm);
    }

    .ai-seo-post-list li {
        padding: 12px;
        margin-bottom: 8px;
        border-radius: var(--ai-seo-border-radius-sm);
        box-shadow: var(--ai-seo-shadow-sm);
        border: 1px solid var(--ai-seo-border-light-color);
        transition: all 0.25s ease;
    }

    .ai-seo-post-list li:hover {
        box-shadow: var(--ai-seo-shadow-md);
        transform: translateY(-2px);
        border-color: var(--ai-seo-primary-border);
    }

    .ai-seo-post-list .post-title {
        font-weight: 600;
        color: var(--ai-seo-primary-color);
        margin-bottom: 6px;
    }

    /* Enhanced score display */
    .seo-scores {
        min-width: 100%;
        margin-left: 24px;
        height: 28px;
        border-radius: var(--ai-seo-border-radius-sm);
        background: linear-gradient(to right, var(--ai-seo-bg-subtle), var(--ai-seo-bg-white));
        box-shadow: var(--ai-seo-shadow-sm);
        border: 1px solid var(--ai-seo-border-light-color);
    }

    .seo-scores strong {
        font-size: 12px;
        padding: 0 8px;
        font-weight: 600;
    }

    .seo-scores strong::before {
        font-size: 14px;
        margin-right: 4px;
    }

    /* Schema checkbox container */
    .schema-checkbox-container {
        margin-top: 10px;
        padding-top: 10px;
        border-top: 1px solid var(--ai-seo-border-light-color);
    }

    /* Improved form inputs */
    input[type="text"],
    input[type="password"],
    input[type="email"],
    input[type="number"],
    input[type="search"],
    input[type="tel"],
    input[type="url"],
    select,
    textarea {
        font-size: 16px !important; /* Prevents iOS zoom on focus */
        padding: 10px;
        height: auto;
        min-height: 44px;
    }
}

    /* Optimize review items */
    .ai-seo-review-item {
        padding: var(--ai-seo-spacing-sm);
        margin-bottom: var(--ai-seo-spacing-md);
    }

    .ai-seo-review-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--ai-seo-spacing-xs);
    }

    .ai-seo-review-title {
        font-size: 15px;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    /* Optimize comparison rows */
    .ai-seo-comparison-row {
        padding: var(--ai-seo-spacing-sm);
        margin-bottom: var(--ai-seo-spacing-md);
    }

    .ai-seo-comparison-label {
        padding: var(--ai-seo-spacing-sm);
        flex-wrap: wrap;
    }

    .ai-seo-comparison-label strong {
        font-size: 1em;
        width: 100%;
    }

    .ai-seo-comparison-label .ai-seo-rating {
        padding: 4px 10px;
        font-size: 0.8em;
    }

    .ai-seo-comparison-label input[type="checkbox"] {
        transform: scale(1.6);
        margin-right: var(--ai-seo-spacing-sm);
    }

    /* Optimize comparison values */
    .ai-seo-value {
        padding: var(--ai-seo-spacing-sm);
    }

    .ai-seo-value .value-header {
        margin: -var(--ai-seo-spacing-sm);
        margin-bottom: var(--ai-seo-spacing-sm);
        padding: 6px var(--ai-seo-spacing-sm);
    }

    .ai-seo-value .value-label {
        font-size: 0.8em;
    }

    .ai-seo-value .value-content {
        padding: var(--ai-seo-spacing-sm);
        margin-bottom: var(--ai-seo-spacing-sm);
        font-size: 0.95em;
        line-height: 1.5;
    }

    .ai-seo-value .value-meta {
        padding: var(--ai-seo-spacing-xs) var(--ai-seo-spacing-sm);
        flex-direction: column;
        align-items: flex-start;
        gap: var(--ai-seo-spacing-xs);
    }

    .ai-seo-value .char-count,
    .ai-seo-value .seo-score {
        width: 100%;
        justify-content: center;
        padding: 6px 10px;
        font-size: 0.8em;
    }

    .ai-seo-value .char-count::before,
    .ai-seo-value .seo-score::before {
        font-size: 14px;
        width: 20px;
        height: 20px;
    }

    /* Optimize comparison arrow */
    .comparison-arrow {
        margin: var(--ai-seo-spacing-xs) 0;
    }

    .comparison-arrow .dashicons {
        font-size: 20px;
        width: 32px;
        height: 32px;
    }

    /* Optimize score display */
    .seo-score-display {
        transform: scale(0.85);
        transform-origin: left center;
    }

    .score-comparison {
        padding: 4px 10px;
        font-size: 0.8em;
    }

    /* Optimize internal links section */
    .ai-seo-links-list li {
        padding: var(--ai-seo-spacing-sm);
    }

    .ai-seo-link-item-content {
        flex-direction: column;
        align-items: flex-start;
    }

    .ai-seo-link-meta {
        margin-top: var(--ai-seo-spacing-xs);
    }

    /* Optimize global status message */
    #ai-seo-global-status {
        font-size: 0.9em;
        padding: var(--ai-seo-spacing-sm);
        text-align: center;
        width: 100%;
    }

/* Touch Device Optimizations - Premium Design */
@media (hover: none) {
    /* Increase touch targets for better accessibility */
    .ai-seo-optimizer-wrap .button,
    .ai-seo-nav-tabs .nav-tab,
    .ai-seo-comparison-label input[type="checkbox"],
    .ai-seo-links-list input[type="checkbox"],
    .ai-seo-apply-element-cb,
    .link-item-cb {
        min-height: 48px; /* Larger than Apple's recommended minimum touch target size */
        padding: 12px 18px;
    }

    /* Enhance checkbox touch targets */
    .ai-seo-comparison-label input[type="checkbox"],
    .ai-seo-links-list input[type="checkbox"],
    .ai-seo-apply-element-cb,
    .link-item-cb {
        transform: scale(1.2);
        margin: 0 var(--ai-seo-spacing-sm);
    }

    /* Modify hover effects for touch devices */
    .ai-seo-box:hover,
    .ai-seo-card:hover,
    .ai-seo-value:hover,
    .ai-seo-comparison-box .seo-score:hover,
    .ai-seo-links-list li:hover,
    .ai-seo-post-list li:hover {
        transform: none;
        box-shadow: var(--ai-seo-shadow-md);
    }

    /* Disable hover animations that don't work well on touch */
    .ai-seo-nav-tabs .nav-tab:hover::before,
    .ai-seo-optimizer-wrap .button:hover::before,
    .ai-seo-comparison-box .seo-score::before {
        display: none;
    }

    /* Enhanced active state for touch feedback */
    .ai-seo-box:active,
    .ai-seo-card:active,
    .ai-seo-value:active,
    .ai-seo-comparison-box .seo-score:active,
    .ai-seo-links-list li:active,
    .ai-seo-post-list li:active,
    .ai-seo-optimizer-wrap .button:active {
        transform: scale(0.98);
        transition: transform 0.1s ease-out;
        box-shadow: var(--ai-seo-shadow-inner);
        background-color: var(--ai-seo-bg-subtle);
    }

    /* Special active states for colored buttons */
    .ai-seo-optimizer-wrap .button.button-primary:active,
    .ai-seo-optimizer-wrap .button.button-secondary:active,
    .ai-seo-optimizer-wrap .button.button-success:active {
        background-color: var(--ai-seo-primary-dark);
    }

    /* Increase spacing for better touch targets */
    .ai-seo-links-list li {
        padding: 14px var(--ai-seo-spacing-md);
    }

    /* Enhance form controls for touch */
    input[type="text"],
    input[type="password"],
    input[type="email"],
    input[type="number"],
    input[type="search"],
    input[type="tel"],
    input[type="url"],
    select,
    textarea {
        padding: 12px;
        min-height: 48px;
    }
}

/* Print Styles - Premium Design */
@media print {
    /* Reset container styles for print */
    .ai-seo-optimizer-wrap {
        margin: 0;
        padding: 0;
        box-shadow: none;
        background: white;
        color: black;
        font-size: 12pt;
        border: none;
    }

    /* Enhance header for print */
    .ai-seo-header {
        background: none !important;
        border-bottom: 2pt solid #000;
        padding: 0 0 10pt 0;
        margin-bottom: 20pt;
    }

    .ai-seo-header-title {
        color: black;
    }

    .ai-seo-header-title h1 {
        font-size: 18pt;
        color: black;
    }

    /* Hide interactive elements */
    .ai-seo-nav-tabs,
    .ai-seo-button-group,
    .ai-seo-apply-element-cb,
    .link-item-cb,
    .ai-seo-pagination,
    .select-all-controls,
    button,
    .button,
    input[type="button"],
    input[type="submit"] {
        display: none !important;
    }

    /* Show all tab content */
    .ai-seo-tabs-container > .tab-content {
        display: block !important;
        border: none;
        box-shadow: none;
        padding: 0;
        margin: 0 0 20pt 0;
        page-break-inside: avoid;
    }

    /* Optimize cards and boxes for print */
    .ai-seo-box,
    .ai-seo-card,
    .ai-seo-comparison-box,
    .ai-seo-value {
        border: 1pt solid #ccc;
        box-shadow: none;
        background: white;
        page-break-inside: avoid;
        margin-bottom: 15pt;
    }

    /* Optimize links for print */
    a {
        color: #000;
        text-decoration: underline;
    }

    /* Add URLs after links */
    a[href]:after {
        content: " (" attr(href) ")";
        font-size: 90%;
        color: #333;
    }

    /* Don't show URLs for internal links */
    a[href^="#"]:after,
    a[href^="javascript:"]:after {
        content: "";
    }

    /* Ensure proper page breaks */
    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
        page-break-inside: avoid;
    }

    img {
        page-break-inside: avoid;
        max-width: 100% !important;
    }

    /* Optimize tables for print */
    table {
        border-collapse: collapse;
    }

    table, th, td {
        border: 1pt solid #ccc;
    }

    th, td {
        padding: 5pt;
    }
}

