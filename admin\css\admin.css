/**
 * Ultra Featured Image Optimizer - Admin Styles
 * Modern, responsive admin interface
 */

/* ==========================================================================
   Variables and Base Styles
   ========================================================================== */

:root {
    --ufio-primary: #2271b1;
    --ufio-primary-dark: #135e96;
    --ufio-success: #00a32a;
    --ufio-warning: #dba617;
    --ufio-error: #d63638;
    --ufio-info: #72aee6;
    --ufio-gray-100: #f6f7f7;
    --ufio-gray-200: #dcdcde;
    --ufio-gray-300: #c3c4c7;
    --ufio-gray-700: #50575e;
    --ufio-gray-900: #1d2327;
    --ufio-border-radius: 4px;
    --ufio-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    --ufio-transition: all 0.2s ease-in-out;
}

/* ==========================================================================
   Layout Components
   ========================================================================== */

.ufio-admin-wrap {
    margin: 20px 20px 0 2px;
    max-width: 1200px;
}

.ufio-header {
    background: #fff;
    border: 1px solid var(--ufio-gray-200);
    border-radius: var(--ufio-border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--ufio-box-shadow);
}

.ufio-header h1 {
    margin: 0 0 10px 0;
    color: var(--ufio-gray-900);
    font-size: 24px;
    font-weight: 600;
}

.ufio-header .description {
    color: var(--ufio-gray-700);
    font-size: 14px;
    margin: 0;
}

.ufio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.ufio-card {
    background: #fff;
    border: 1px solid var(--ufio-gray-200);
    border-radius: var(--ufio-border-radius);
    padding: 20px;
    box-shadow: var(--ufio-box-shadow);
    transition: var(--ufio-transition);
}

.ufio-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.ufio-card h2 {
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--ufio-gray-900);
}

.ufio-card h3 {
    margin: 0 0 10px 0;
    font-size: 16px;
    font-weight: 500;
    color: var(--ufio-gray-700);
}

/* ==========================================================================
   Statistics and Metrics
   ========================================================================== */

.ufio-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.ufio-stat-card {
    background: linear-gradient(135deg, var(--ufio-primary), var(--ufio-primary-dark));
    color: #fff;
    padding: 20px;
    border-radius: var(--ufio-border-radius);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.ufio-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
    pointer-events: none;
}

.ufio-stat-number {
    font-size: 32px;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 5px;
}

.ufio-stat-label {
    font-size: 14px;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.ufio-stat-card.success {
    background: linear-gradient(135deg, var(--ufio-success), #007a1f);
}

.ufio-stat-card.warning {
    background: linear-gradient(135deg, var(--ufio-warning), #b8860b);
}

.ufio-stat-card.error {
    background: linear-gradient(135deg, var(--ufio-error), #b32d2e);
}

/* ==========================================================================
   Progress Bars and Indicators
   ========================================================================== */

.ufio-progress {
    background: var(--ufio-gray-100);
    border-radius: 10px;
    height: 20px;
    overflow: hidden;
    margin: 10px 0;
    position: relative;
}

.ufio-progress-bar {
    background: linear-gradient(90deg, var(--ufio-primary), var(--ufio-info));
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
    position: relative;
}

.ufio-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.ufio-progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    font-weight: 600;
    color: var(--ufio-gray-900);
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

/* ==========================================================================
   Buttons and Controls
   ========================================================================== */

.ufio-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border: none;
    border-radius: var(--ufio-border-radius);
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--ufio-transition);
    position: relative;
    overflow: hidden;
}

.ufio-button:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.ufio-button:hover:before {
    left: 100%;
}

.ufio-button.primary {
    background: var(--ufio-primary);
    color: #fff;
}

.ufio-button.primary:hover {
    background: var(--ufio-primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(34, 113, 177, 0.3);
}

.ufio-button.success {
    background: var(--ufio-success);
    color: #fff;
}

.ufio-button.success:hover {
    background: #007a1f;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 163, 42, 0.3);
}

.ufio-button.warning {
    background: var(--ufio-warning);
    color: #fff;
}

.ufio-button.warning:hover {
    background: #b8860b;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(219, 166, 23, 0.3);
}

.ufio-button.danger {
    background: var(--ufio-error);
    color: #fff;
}

.ufio-button.danger:hover {
    background: #b32d2e;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(214, 54, 56, 0.3);
}

.ufio-button.secondary {
    background: var(--ufio-gray-100);
    color: var(--ufio-gray-700);
    border: 1px solid var(--ufio-gray-200);
}

.ufio-button.secondary:hover {
    background: var(--ufio-gray-200);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.ufio-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

.ufio-button-group {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* ==========================================================================
   Forms and Inputs
   ========================================================================== */

.ufio-form-group {
    margin-bottom: 20px;
}

.ufio-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--ufio-gray-900);
}

.ufio-form-group input,
.ufio-form-group select,
.ufio-form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--ufio-gray-300);
    border-radius: var(--ufio-border-radius);
    font-size: 14px;
    transition: var(--ufio-transition);
}

.ufio-form-group input:focus,
.ufio-form-group select:focus,
.ufio-form-group textarea:focus {
    outline: none;
    border-color: var(--ufio-primary);
    box-shadow: 0 0 0 2px rgba(34, 113, 177, 0.1);
}

.ufio-form-group .description {
    margin-top: 5px;
    font-size: 12px;
    color: var(--ufio-gray-700);
}

/* ==========================================================================
   Tables
   ========================================================================== */

.ufio-table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    border-radius: var(--ufio-border-radius);
    overflow: hidden;
    box-shadow: var(--ufio-box-shadow);
}

.ufio-table th,
.ufio-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--ufio-gray-200);
}

.ufio-table th {
    background: var(--ufio-gray-100);
    font-weight: 600;
    color: var(--ufio-gray-900);
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 0.5px;
}

.ufio-table tr:hover {
    background: var(--ufio-gray-100);
}

.ufio-table tr:last-child td {
    border-bottom: none;
}

/* ==========================================================================
   Notifications and Alerts
   ========================================================================== */

.ufio-notice {
    padding: 15px 20px;
    border-radius: var(--ufio-border-radius);
    margin-bottom: 20px;
    border-left: 4px solid;
    display: flex;
    align-items: center;
    gap: 10px;
}

.ufio-notice.success {
    background: #f0f9ff;
    border-color: var(--ufio-success);
    color: #0f5132;
}

.ufio-notice.warning {
    background: #fff8e1;
    border-color: var(--ufio-warning);
    color: #8a6914;
}

.ufio-notice.error {
    background: #fef2f2;
    border-color: var(--ufio-error);
    color: #991b1b;
}

.ufio-notice.info {
    background: #f0f9ff;
    border-color: var(--ufio-info);
    color: #1e40af;
}

.ufio-notice-icon {
    font-size: 18px;
    flex-shrink: 0;
}

/* ==========================================================================
   Loading States
   ========================================================================== */

.ufio-loading {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: var(--ufio-gray-700);
}

.ufio-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid var(--ufio-gray-300);
    border-top: 2px solid var(--ufio-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ==========================================================================
   Processing Queue
   ========================================================================== */

.ufio-queue-status {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.ufio-queue-item {
    background: var(--ufio-gray-100);
    padding: 15px;
    border-radius: var(--ufio-border-radius);
    text-align: center;
}

.ufio-queue-number {
    font-size: 24px;
    font-weight: 700;
    color: var(--ufio-primary);
    margin-bottom: 5px;
}

.ufio-queue-label {
    font-size: 12px;
    color: var(--ufio-gray-700);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ==========================================================================
   SEO Score Display
   ========================================================================== */

.ufio-seo-score {
    display: flex;
    align-items: center;
    gap: 15px;
}

.ufio-seo-score .score {
    background: var(--ufio-primary);
    color: #fff;
    padding: 8px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 14px;
}

.ufio-seo-score .recommendations {
    flex: 1;
}

.recommendations-list {
    margin: 0;
    padding: 0;
    list-style: none;
}

.recommendations-list li {
    padding: 4px 0;
    font-size: 12px;
}

.recommendations-list li.warning {
    color: var(--ufio-warning);
}

.recommendations-list li.error {
    color: var(--ufio-error);
}

.recommendations-list li.success {
    color: var(--ufio-success);
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
    .ufio-admin-wrap {
        margin: 10px 10px 0 2px;
    }
    
    .ufio-grid {
        grid-template-columns: 1fr;
    }
    
    .ufio-stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
    
    .ufio-button-group {
        flex-direction: column;
    }
    
    .ufio-button {
        justify-content: center;
    }
    
    .ufio-queue-status {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .ufio-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .ufio-queue-status {
        grid-template-columns: 1fr;
    }
    
    .ufio-seo-score {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}

/* ==========================================================================
   Dark Mode Support
   ========================================================================== */

@media (prefers-color-scheme: dark) {
    :root {
        --ufio-gray-100: #2c3338;
        --ufio-gray-200: #3c434a;
        --ufio-gray-300: #50575e;
        --ufio-gray-700: #c3c4c7;
        --ufio-gray-900: #f0f0f1;
    }
    
    .ufio-card,
    .ufio-header {
        background: #1d2327;
        border-color: var(--ufio-gray-300);
    }
    
    .ufio-table {
        background: #1d2327;
    }
    
    .ufio-form-group input,
    .ufio-form-group select,
    .ufio-form-group textarea {
        background: #1d2327;
        border-color: var(--ufio-gray-300);
        color: var(--ufio-gray-900);
    }
}
