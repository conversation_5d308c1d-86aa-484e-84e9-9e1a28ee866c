<?php
/**
 * SEO Processor Class
 * 
 * Handles SEO analysis and optimization with advanced algorithms
 * 
 * @package AiSeoOptimizerUltra
 * @since 9.0.0
 */

namespace AiSeoOptimizerUltra\Core;

use Exception;
use WP_Error;

/**
 * SEO Processor Class
 * 
 * Main SEO processing engine with optimization algorithms
 */
final class SeoProcessor {
    private static $instance = null;
    private $processing_queue = [];
    
    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Private constructor
     */
    private function __construct() {
        // Initialize processor
    }
    
    /**
     * Process post SEO optimization
     */
    public function process_post($post_id, $options = []) {
        try {
            \AiSeoUltraMonitor::check_memory_limit('seo_processing');
            
            $post_id = absint($post_id);
            if (!$post_id) {
                return new WP_Error('invalid_post_id', 'Invalid post ID');
            }
            
            $post = get_post($post_id);
            if (!$post) {
                return new WP_Error('post_not_found', 'Post not found');
            }
            
            Logger::info("Starting SEO processing for post {$post_id}: {$post->post_title}");
            
            // Extract post data
            $post_data = $this->extract_post_data($post);
            
            // Calculate local scores
            $local_scores = $this->calculate_local_scores($post_data);
            
            // Get AI suggestions if enabled and API key is available
            $ai_suggestions = [];
            if ($this->should_use_ai($options)) {
                $ai_suggestions = $this->get_ai_suggestions($post_data, $options);
            }
            
            // Combine results
            $seo_data = [
                'post_id' => $post_id,
                'title_score' => $local_scores['title']['score'],
                'description_score' => $local_scores['description']['score'],
                'overall_score' => $this->calculate_overall_score($local_scores, $ai_suggestions),
                'suggested_title' => $ai_suggestions['title'] ?? $this->generate_local_title_suggestion($post_data),
                'suggested_description' => $ai_suggestions['meta_description'] ?? $this->generate_local_description_suggestion($post_data),
                'suggested_h1' => $ai_suggestions['h1'] ?? $post_data['title'],
                'keywords' => $this->format_keywords($post_data['keywords']),
                'internal_links' => $ai_suggestions['internal_links'] ?? $this->suggest_internal_links($post_data),
                'schema_data' => $this->generate_schema_data($post_data),
                'last_processed' => current_time('mysql')
            ];
            
            // Save to database
            $saved = Database::insert_seo_data($post_id, $seo_data);
            
            if (!$saved) {
                return new WP_Error('save_failed', 'Failed to save SEO data');
            }
            
            // Clear caches
            Cache::clear_post_cache($post_id);
            
            Logger::info("SEO processing completed for post {$post_id}");
            
            return $seo_data;
            
        } catch (Exception $e) {
            Logger::error("SEO processing failed for post {$post_id}: " . $e->getMessage());
            return new WP_Error('processing_failed', $e->getMessage());
        }
    }
    
    /**
     * Extract post data for analysis
     */
    private function extract_post_data($post) {
        $post_id = $post->ID;
        
        // Get keywords
        $keywords = $this->extract_keywords($post_id);
        
        // Extract content elements
        $content = apply_filters('the_content', $post->post_content);
        $h1 = $this->extract_h1($content);
        $h2s = $this->extract_headings($content, 'h2', 5);
        $first_paragraph = $this->extract_first_paragraph($content);
        
        // Get meta description
        $meta_description = $this->get_current_meta_description($post_id);
        
        // Get taxonomy terms
        $taxonomy_terms = $this->get_taxonomy_terms($post_id);
        
        return [
            'post_id' => $post_id,
            'title' => $post->post_title,
            'content' => $content,
            'excerpt' => $post->post_excerpt,
            'h1' => $h1,
            'h2s' => $h2s,
            'first_paragraph' => $first_paragraph,
            'meta_description' => $meta_description,
            'keywords' => $keywords,
            'taxonomy_terms' => $taxonomy_terms,
            'word_count' => $this->count_words($content),
            'modified_gmt' => $post->post_modified_gmt,
            'post_type' => $post->post_type,
            'post_status' => $post->post_status
        ];
    }
    
    /**
     * Extract keywords from post
     */
    private function extract_keywords($post_id) {
        $keywords = ['primary' => '', 'secondary' => []];
        
        // Check SEO plugins first
        $seo_plugin = $this->get_active_seo_plugin();
        
        if ($seo_plugin === 'yoast') {
            $primary = get_post_meta($post_id, '_yoast_wpseo_focuskw', true);
            $keywords['primary'] = $primary ?: '';
            
            // Get additional keywords
            $additional = get_post_meta($post_id, '_yoast_wpseo_keywords', true);
            if ($additional) {
                $decoded = json_decode($additional, true);
                if ($decoded && is_array($decoded)) {
                    $keywords['secondary'] = array_column($decoded, 'keyword');
                }
            }
        } elseif ($seo_plugin === 'rankmath') {
            $focus_keywords = get_post_meta($post_id, 'rank_math_focus_keyword', true);
            if ($focus_keywords) {
                $keyword_array = array_map('trim', explode(',', $focus_keywords));
                $keywords['primary'] = array_shift($keyword_array) ?: '';
                $keywords['secondary'] = $keyword_array;
            }
        }
        
        // Fallback to our own meta fields
        if (empty($keywords['primary'])) {
            $keywords['primary'] = get_post_meta($post_id, '_ai_seo_ultra_primary_keyword', true) ?: '';
        }
        
        if (empty($keywords['secondary'])) {
            $secondary = get_post_meta($post_id, '_ai_seo_ultra_secondary_keywords', true);
            if ($secondary) {
                $keywords['secondary'] = array_map('trim', explode(',', $secondary));
            }
        }
        
        return $keywords;
    }
    
    /**
     * Calculate local SEO scores
     */
    private function calculate_local_scores($post_data) {
        $title_score = $this->calculate_title_score($post_data);
        $description_score = $this->calculate_description_score($post_data);
        
        return [
            'title' => $title_score,
            'description' => $description_score
        ];
    }
    
    /**
     * Calculate title score
     */
    private function calculate_title_score($post_data) {
        $title = $post_data['title'];
        $primary_keyword = $post_data['keywords']['primary'];
        $secondary_keywords = $post_data['keywords']['secondary'];
        
        $score = 0;
        $max_score = 100;
        
        // Length score (30 points)
        $length = mb_strlen($title);
        if ($length >= 50 && $length <= 60) {
            $score += 30;
        } elseif ($length >= 40 && $length <= 70) {
            $score += 20;
        } elseif ($length >= 30 && $length <= 80) {
            $score += 10;
        }
        
        // Keyword inclusion (40 points)
        if (!empty($primary_keyword)) {
            $title_lower = mb_strtolower($title);
            $keyword_lower = mb_strtolower($primary_keyword);
            
            if (mb_strpos($title_lower, $keyword_lower) !== false) {
                $score += 30;
                
                // Bonus for keyword position
                $position = mb_strpos($title_lower, $keyword_lower);
                if ($position < 20) {
                    $score += 10;
                }
            }
        }
        
        // Secondary keywords (15 points)
        if (!empty($secondary_keywords)) {
            $title_lower = mb_strtolower($title);
            $found_secondary = 0;
            
            foreach ($secondary_keywords as $keyword) {
                if (mb_strpos($title_lower, mb_strtolower($keyword)) !== false) {
                    $found_secondary++;
                }
            }
            
            $score += min($found_secondary * 5, 15);
        }
        
        // Readability (15 points)
        $word_count = str_word_count($title);
        if ($word_count >= 5 && $word_count <= 12) {
            $score += 15;
        } elseif ($word_count >= 3 && $word_count <= 15) {
            $score += 10;
        }
        
        // Penalties
        $penalties = 0;
        
        // Excessive capitalization
        if (preg_match_all('/[A-Z]/', $title) > strlen($title) * 0.3) {
            $penalties += 10;
        }
        
        // Excessive punctuation
        if (preg_match_all('/[!?]/', $title) > 2) {
            $penalties += 5;
        }
        
        $final_score = max(0, min($max_score, $score - $penalties));
        
        return [
            'score' => $final_score,
            'class' => $this->get_score_class($final_score)
        ];
    }
    
    /**
     * Calculate description score
     */
    private function calculate_description_score($post_data) {
        $description = $post_data['meta_description'];
        $primary_keyword = $post_data['keywords']['primary'];
        $secondary_keywords = $post_data['keywords']['secondary'];
        
        if (empty($description)) {
            return ['score' => 0, 'class' => 'score-poor'];
        }
        
        $score = 0;
        $max_score = 100;
        
        // Length score (25 points)
        $length = mb_strlen($description);
        if ($length >= 150 && $length <= 160) {
            $score += 25;
        } elseif ($length >= 140 && $length <= 170) {
            $score += 20;
        } elseif ($length >= 120 && $length <= 180) {
            $score += 15;
        } elseif ($length >= 100 && $length <= 200) {
            $score += 10;
        }
        
        // Keyword inclusion (35 points)
        if (!empty($primary_keyword)) {
            $desc_lower = mb_strtolower($description);
            $keyword_lower = mb_strtolower($primary_keyword);
            
            if (mb_strpos($desc_lower, $keyword_lower) !== false) {
                $score += 25;
                
                // Bonus for early placement
                $position = mb_strpos($desc_lower, $keyword_lower);
                if ($position < 50) {
                    $score += 10;
                }
            }
        }
        
        // Secondary keywords (20 points)
        if (!empty($secondary_keywords)) {
            $desc_lower = mb_strtolower($description);
            $found_secondary = 0;
            
            foreach ($secondary_keywords as $keyword) {
                if (mb_strpos($desc_lower, mb_strtolower($keyword)) !== false) {
                    $found_secondary++;
                }
            }
            
            $score += min($found_secondary * 7, 20);
        }
        
        // Call to action (10 points)
        $cta_patterns = [
            'learn more', 'read more', 'discover', 'find out',
            'get started', 'try now', 'download', 'contact'
        ];
        
        $desc_lower = mb_strtolower($description);
        foreach ($cta_patterns as $pattern) {
            if (mb_strpos($desc_lower, $pattern) !== false) {
                $score += 10;
                break;
            }
        }
        
        // Uniqueness (10 points)
        if (!$this->is_duplicate_description($description, $post_data['post_id'])) {
            $score += 10;
        }
        
        $final_score = min($max_score, $score);
        
        return [
            'score' => $final_score,
            'class' => $this->get_score_class($final_score)
        ];
    }
    
    /**
     * Get score class for styling
     */
    private function get_score_class($score) {
        if ($score >= 80) return 'score-excellent';
        if ($score >= 60) return 'score-good';
        if ($score >= 40) return 'score-fair';
        return 'score-poor';
    }
    
    /**
     * Check if should use AI
     */
    private function should_use_ai($options) {
        $api_key = Options::get('ai_seo_ultra_api_key');
        return !empty($api_key) && ($options['use_ai'] ?? true);
    }
    
    /**
     * Get AI suggestions
     */
    private function get_ai_suggestions($post_data, $options) {
        try {
            $api_client = ApiClient::get_instance();
            $response = $api_client->generate_seo_content($post_data, $options);
            
            if (is_wp_error($response)) {
                Logger::warning('AI suggestions failed: ' . $response->get_error_message());
                return [];
            }
            
            return $response;
            
        } catch (Exception $e) {
            Logger::error('AI suggestions error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Calculate overall score
     */
    private function calculate_overall_score($local_scores, $ai_suggestions) {
        $title_score = $local_scores['title']['score'];
        $description_score = $local_scores['description']['score'];
        
        // Weight the scores
        $overall = ($title_score * 0.4) + ($description_score * 0.4);
        
        // Add bonus for AI suggestions
        if (!empty($ai_suggestions)) {
            $overall += 20; // Bonus for having AI suggestions
        }
        
        return min(100, round($overall));
    }
    
    /**
     * Helper methods (simplified implementations)
     */
    private function extract_h1($content) {
        if (preg_match('/<h1[^>]*>(.*?)<\/h1>/is', $content, $matches)) {
            return wp_strip_all_tags($matches[1]);
        }
        return '';
    }
    
    private function extract_headings($content, $tag, $limit) {
        $headings = [];
        if (preg_match_all("/<{$tag}[^>]*>(.*?)<\/{$tag}>/is", $content, $matches, PREG_SET_ORDER)) {
            foreach (array_slice($matches, 0, $limit) as $match) {
                $headings[] = wp_strip_all_tags($match[1]);
            }
        }
        return $headings;
    }
    
    private function extract_first_paragraph($content) {
        if (preg_match('/<p[^>]*>(.*?)<\/p>/is', $content, $matches)) {
            return wp_trim_words(wp_strip_all_tags($matches[1]), 30);
        }
        return '';
    }
    
    private function get_current_meta_description($post_id) {
        // Check various SEO plugins and fallbacks
        $description = get_post_meta($post_id, '_yoast_wpseo_metadesc', true);
        if (empty($description)) {
            $description = get_post_meta($post_id, 'rank_math_description', true);
        }
        if (empty($description)) {
            $post = get_post($post_id);
            $description = $post->post_excerpt ?: wp_trim_words($post->post_content, 25);
        }
        return $description;
    }
    
    private function get_taxonomy_terms($post_id) {
        $terms = [];
        $taxonomies = get_object_taxonomies(get_post_type($post_id));
        
        foreach ($taxonomies as $taxonomy) {
            $post_terms = get_the_terms($post_id, $taxonomy);
            if ($post_terms && !is_wp_error($post_terms)) {
                $terms = array_merge($terms, wp_list_pluck($post_terms, 'name'));
            }
        }
        
        return $terms;
    }
    
    private function count_words($content) {
        return str_word_count(wp_strip_all_tags($content));
    }
    
    private function get_active_seo_plugin() {
        if (defined('WPSEO_VERSION')) return 'yoast';
        if (defined('RANK_MATH_VERSION')) return 'rankmath';
        return 'none';
    }
    
    private function format_keywords($keywords) {
        $formatted = [];
        if (!empty($keywords['primary'])) {
            $formatted[] = $keywords['primary'];
        }
        if (!empty($keywords['secondary'])) {
            $formatted = array_merge($formatted, $keywords['secondary']);
        }
        return implode(', ', $formatted);
    }
    
    private function suggest_internal_links($post_data) {
        // Simplified internal link suggestions
        return [];
    }
    
    private function generate_schema_data($post_data) {
        // Basic schema data
        return [
            '@type' => 'Article',
            'headline' => $post_data['title'],
            'wordCount' => $post_data['word_count']
        ];
    }
    
    private function generate_local_title_suggestion($post_data) {
        // Simple local title optimization
        $title = $post_data['title'];
        $keyword = $post_data['keywords']['primary'];
        
        if (!empty($keyword) && stripos($title, $keyword) === false) {
            return $keyword . ': ' . $title;
        }
        
        return $title;
    }
    
    private function generate_local_description_suggestion($post_data) {
        // Simple local description generation
        $description = $post_data['first_paragraph'];
        $keyword = $post_data['keywords']['primary'];
        
        if (!empty($keyword) && !empty($description)) {
            return wp_trim_words($description . ' Learn more about ' . $keyword . '.', 25);
        }
        
        return $description;
    }
    
    private function is_duplicate_description($description, $post_id) {
        global $wpdb;
        
        $count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->postmeta} 
             WHERE meta_key IN ('_yoast_wpseo_metadesc', 'rank_math_description') 
             AND meta_value = %s AND post_id != %d",
            $description,
            $post_id
        ));
        
        return $count > 0;
    }
    
    /**
     * Prevent cloning and unserialization
     */
    private function __clone() {}
    public function __wakeup() {
        throw new Exception('Cannot unserialize singleton');
    }
}
