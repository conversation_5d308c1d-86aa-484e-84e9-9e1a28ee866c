<?php
/**
 * Frontend Manager Class
 * 
 * Manages frontend functionality including meta tags and schema output
 * 
 * @package AiSeoOptimizerUltra
 * @since 9.0.0
 */

namespace AiSeoOptimizerUltra\Frontend;

use AiSeoOptimizerUltra\Core\Logger;
use AiSeoOptimizerUltra\Core\Cache;
use AiSeoOptimizerUltra\Core\Options;
use Exception;

/**
 * Frontend Manager Class
 * 
 * Handles all frontend-related functionality
 */
final class FrontendManager {
    private static $instance = null;
    private $hooks_registered = false;
    
    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Private constructor
     */
    private function __construct() {
        $this->init_frontend();
    }
    
    /**
     * Initialize frontend functionality
     */
    private function init_frontend() {
        $this->register_frontend_hooks();
    }
    
    /**
     * Register frontend hooks
     */
    private function register_frontend_hooks() {
        if ($this->hooks_registered) {
            return;
        }
        
        // Meta tags output
        add_action('wp_head', [$this, 'output_meta_tags'], 1);
        
        // Schema markup output
        add_action('wp_footer', [$this, 'output_schema_markup'], 99);
        
        // Open Graph tags
        add_action('wp_head', [$this, 'output_open_graph_tags'], 5);
        
        // Twitter Card tags
        add_action('wp_head', [$this, 'output_twitter_card_tags'], 6);
        
        // Canonical URL
        add_action('wp_head', [$this, 'output_canonical_url'], 2);
        
        // Meta robots
        add_action('wp_head', [$this, 'output_meta_robots'], 3);
        
        $this->hooks_registered = true;
        Logger::debug('Frontend hooks registered');
    }
    
    /**
     * Output meta tags
     */
    public function output_meta_tags() {
        if (!is_singular()) {
            return;
        }
        
        try {
            $post_id = get_the_ID();
            if (!$post_id) {
                return;
            }
            
            // Get cached meta tags
            $cache_key = "meta_tags_{$post_id}";
            $meta_tags = Cache::get($cache_key, 'meta');
            
            if ($meta_tags === false) {
                $meta_tags = $this->generate_meta_tags($post_id);
                Cache::set($cache_key, $meta_tags, 'meta', HOUR_IN_SECONDS);
            }
            
            // Output meta tags
            foreach ($meta_tags as $tag) {
                echo $tag . "\n";
            }
            
        } catch (Exception $e) {
            Logger::error('Error outputting meta tags: ' . $e->getMessage());
        }
    }
    
    /**
     * Generate meta tags for a post
     */
    private function generate_meta_tags($post_id) {
        $meta_tags = [];
        $post = get_post($post_id);
        
        if (!$post) {
            return $meta_tags;
        }
        
        // Get SEO data
        $seo_data = $this->get_post_seo_data($post_id);
        
        // Meta description
        $description = $seo_data['suggested_description'] ?? $this->get_meta_description($post_id);
        if (!empty($description)) {
            $meta_tags[] = '<meta name="description" content="' . esc_attr($description) . '">';
        }
        
        // Meta keywords (if enabled)
        $keywords = $seo_data['keywords'] ?? '';
        if (!empty($keywords) && Options::get('ai_seo_ultra_include_keywords', false)) {
            $meta_tags[] = '<meta name="keywords" content="' . esc_attr($keywords) . '">';
        }
        
        // Author meta
        $author_id = $post->post_author;
        if ($author_id) {
            $author_name = get_the_author_meta('display_name', $author_id);
            $meta_tags[] = '<meta name="author" content="' . esc_attr($author_name) . '">';
        }
        
        // Article published/modified time
        $meta_tags[] = '<meta property="article:published_time" content="' . esc_attr(get_the_date('c', $post_id)) . '">';
        $meta_tags[] = '<meta property="article:modified_time" content="' . esc_attr(get_the_modified_date('c', $post_id)) . '">';
        
        return $meta_tags;
    }
    
    /**
     * Output Open Graph tags
     */
    public function output_open_graph_tags() {
        if (!is_singular()) {
            return;
        }
        
        try {
            $post_id = get_the_ID();
            if (!$post_id) {
                return;
            }
            
            $cache_key = "og_tags_{$post_id}";
            $og_tags = Cache::get($cache_key, 'meta');
            
            if ($og_tags === false) {
                $og_tags = $this->generate_open_graph_tags($post_id);
                Cache::set($cache_key, $og_tags, 'meta', HOUR_IN_SECONDS);
            }
            
            foreach ($og_tags as $property => $content) {
                echo '<meta property="' . esc_attr($property) . '" content="' . esc_attr($content) . '">' . "\n";
            }
            
        } catch (Exception $e) {
            Logger::error('Error outputting Open Graph tags: ' . $e->getMessage());
        }
    }
    
    /**
     * Generate Open Graph tags
     */
    private function generate_open_graph_tags($post_id) {
        $og_tags = [];
        $post = get_post($post_id);
        $seo_data = $this->get_post_seo_data($post_id);
        
        // Basic OG tags
        $og_tags['og:type'] = 'article';
        $og_tags['og:title'] = $seo_data['suggested_title'] ?? get_the_title($post_id);
        $og_tags['og:description'] = $seo_data['suggested_description'] ?? $this->get_meta_description($post_id);
        $og_tags['og:url'] = get_permalink($post_id);
        $og_tags['og:site_name'] = get_bloginfo('name');
        
        // Featured image
        if (has_post_thumbnail($post_id)) {
            $image_id = get_post_thumbnail_id($post_id);
            $image_url = wp_get_attachment_image_url($image_id, 'large');
            if ($image_url) {
                $og_tags['og:image'] = $image_url;
                
                // Image dimensions
                $image_meta = wp_get_attachment_metadata($image_id);
                if ($image_meta) {
                    $og_tags['og:image:width'] = $image_meta['width'] ?? '';
                    $og_tags['og:image:height'] = $image_meta['height'] ?? '';
                }
            }
        }
        
        // Article specific tags
        $og_tags['article:author'] = get_author_posts_url($post->post_author);
        $og_tags['article:published_time'] = get_the_date('c', $post_id);
        $og_tags['article:modified_time'] = get_the_modified_date('c', $post_id);
        
        // Categories and tags
        $categories = get_the_category($post_id);
        if ($categories) {
            foreach (array_slice($categories, 0, 3) as $category) {
                $og_tags['article:section'] = $category->name;
                break; // Only first category
            }
        }
        
        $tags = get_the_tags($post_id);
        if ($tags) {
            foreach (array_slice($tags, 0, 5) as $tag) {
                $og_tags['article:tag'] = $tag->name;
            }
        }
        
        return $og_tags;
    }
    
    /**
     * Output Twitter Card tags
     */
    public function output_twitter_card_tags() {
        if (!is_singular()) {
            return;
        }
        
        try {
            $post_id = get_the_ID();
            if (!$post_id) {
                return;
            }
            
            $cache_key = "twitter_tags_{$post_id}";
            $twitter_tags = Cache::get($cache_key, 'meta');
            
            if ($twitter_tags === false) {
                $twitter_tags = $this->generate_twitter_card_tags($post_id);
                Cache::set($cache_key, $twitter_tags, 'meta', HOUR_IN_SECONDS);
            }
            
            foreach ($twitter_tags as $name => $content) {
                echo '<meta name="' . esc_attr($name) . '" content="' . esc_attr($content) . '">' . "\n";
            }
            
        } catch (Exception $e) {
            Logger::error('Error outputting Twitter Card tags: ' . $e->getMessage());
        }
    }
    
    /**
     * Generate Twitter Card tags
     */
    private function generate_twitter_card_tags($post_id) {
        $twitter_tags = [];
        $seo_data = $this->get_post_seo_data($post_id);
        
        // Card type
        $twitter_tags['twitter:card'] = has_post_thumbnail($post_id) ? 'summary_large_image' : 'summary';
        
        // Basic tags
        $twitter_tags['twitter:title'] = $seo_data['suggested_title'] ?? get_the_title($post_id);
        $twitter_tags['twitter:description'] = $seo_data['suggested_description'] ?? $this->get_meta_description($post_id);
        
        // Image
        if (has_post_thumbnail($post_id)) {
            $image_url = wp_get_attachment_image_url(get_post_thumbnail_id($post_id), 'large');
            if ($image_url) {
                $twitter_tags['twitter:image'] = $image_url;
            }
        }
        
        // Site handle (if configured)
        $twitter_handle = Options::get('ai_seo_ultra_twitter_handle', '');
        if (!empty($twitter_handle)) {
            $twitter_tags['twitter:site'] = '@' . ltrim($twitter_handle, '@');
        }
        
        return $twitter_tags;
    }
    
    /**
     * Output canonical URL
     */
    public function output_canonical_url() {
        if (!is_singular()) {
            return;
        }
        
        try {
            $post_id = get_the_ID();
            if (!$post_id) {
                return;
            }
            
            $canonical_url = $this->get_canonical_url($post_id);
            if ($canonical_url) {
                echo '<link rel="canonical" href="' . esc_url($canonical_url) . '">' . "\n";
            }
            
        } catch (Exception $e) {
            Logger::error('Error outputting canonical URL: ' . $e->getMessage());
        }
    }
    
    /**
     * Get canonical URL for post
     */
    private function get_canonical_url($post_id) {
        // Check for custom canonical URL
        $custom_canonical = get_post_meta($post_id, '_ai_seo_ultra_canonical_url', true);
        if (!empty($custom_canonical)) {
            return $custom_canonical;
        }
        
        // Default to permalink
        return get_permalink($post_id);
    }
    
    /**
     * Output meta robots
     */
    public function output_meta_robots() {
        if (!is_singular()) {
            return;
        }
        
        try {
            $post_id = get_the_ID();
            if (!$post_id) {
                return;
            }
            
            $robots = $this->get_meta_robots($post_id);
            if (!empty($robots)) {
                echo '<meta name="robots" content="' . esc_attr($robots) . '">' . "\n";
            }
            
        } catch (Exception $e) {
            Logger::error('Error outputting meta robots: ' . $e->getMessage());
        }
    }
    
    /**
     * Get meta robots directive
     */
    private function get_meta_robots($post_id) {
        $robots = [];
        
        // Check post meta for custom robots
        $custom_robots = get_post_meta($post_id, '_ai_seo_ultra_meta_robots', true);
        if (!empty($custom_robots)) {
            return $custom_robots;
        }
        
        // Default robots based on post status
        $post = get_post($post_id);
        if ($post && $post->post_status === 'publish') {
            $robots[] = 'index';
            $robots[] = 'follow';
        } else {
            $robots[] = 'noindex';
            $robots[] = 'nofollow';
        }
        
        return implode(', ', $robots);
    }
    
    /**
     * Output schema markup
     */
    public function output_schema_markup() {
        if (!is_singular()) {
            return;
        }
        
        try {
            $post_id = get_the_ID();
            if (!$post_id) {
                return;
            }
            
            // Check if schema is enabled
            if (!Options::get('ai_seo_ultra_schema_enabled', true)) {
                return;
            }
            
            $cache_key = "schema_markup_{$post_id}";
            $schema = Cache::get($cache_key, 'schema');
            
            if ($schema === false) {
                $schema = $this->generate_schema_markup($post_id);
                Cache::set($cache_key, $schema, 'schema', WEEK_IN_SECONDS);
            }
            
            if (!empty($schema)) {
                echo '<script type="application/ld+json">' . wp_json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . '</script>' . "\n";
            }
            
        } catch (Exception $e) {
            Logger::error('Error outputting schema markup: ' . $e->getMessage());
        }
    }
    
    /**
     * Generate schema markup
     */
    private function generate_schema_markup($post_id) {
        $post = get_post($post_id);
        if (!$post) {
            return null;
        }
        
        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'Article',
            'headline' => get_the_title($post_id),
            'description' => $this->get_meta_description($post_id),
            'url' => get_permalink($post_id),
            'datePublished' => get_the_date('c', $post_id),
            'dateModified' => get_the_modified_date('c', $post_id),
            'author' => [
                '@type' => 'Person',
                'name' => get_the_author_meta('display_name', $post->post_author),
                'url' => get_author_posts_url($post->post_author)
            ],
            'publisher' => [
                '@type' => 'Organization',
                'name' => get_bloginfo('name'),
                'url' => home_url()
            ]
        ];
        
        // Add featured image if available
        if (has_post_thumbnail($post_id)) {
            $image_url = wp_get_attachment_image_url(get_post_thumbnail_id($post_id), 'large');
            if ($image_url) {
                $schema['image'] = $image_url;
            }
        }
        
        // Add word count
        $content = get_post_field('post_content', $post_id);
        $word_count = str_word_count(wp_strip_all_tags($content));
        if ($word_count > 0) {
            $schema['wordCount'] = $word_count;
        }
        
        return $schema;
    }
    
    /**
     * Get post SEO data
     */
    private function get_post_seo_data($post_id) {
        return \AiSeoOptimizerUltra\Core\Database::get_seo_data($post_id) ?: [];
    }
    
    /**
     * Get meta description
     */
    private function get_meta_description($post_id) {
        // Try to get from SEO plugins first
        $description = '';
        
        // Yoast SEO
        if (function_exists('YoastSEO')) {
            $description = get_post_meta($post_id, '_yoast_wpseo_metadesc', true);
        }
        
        // RankMath
        if (empty($description) && function_exists('rank_math')) {
            $description = get_post_meta($post_id, 'rank_math_description', true);
        }
        
        // Fallback to excerpt
        if (empty($description)) {
            $post = get_post($post_id);
            if ($post && !empty($post->post_excerpt)) {
                $description = $post->post_excerpt;
            } else {
                // Generate from content
                $content = wp_strip_all_tags($post->post_content);
                $description = wp_trim_words($content, 25);
            }
        }
        
        return trim($description);
    }
    
    /**
     * Prevent cloning and unserialization
     */
    private function __clone() {}
    public function __wakeup() {
        throw new Exception('Cannot unserialize singleton');
    }
}
