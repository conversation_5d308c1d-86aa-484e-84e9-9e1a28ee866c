<?php
/**
 * Admin interface and functionality
 * 
 * @package UltraFeaturedImageOptimizer
 * @subpackage Admin
 */

namespace UltraFeaturedImageOptimizer;

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Admin interface with modern, responsive design
 */
class Admin {
    
    /**
     * Dependencies
     */
    private $database;
    private $cache;
    private $logger;
    private $background_processor;
    
    /**
     * Admin pages
     * @var array
     */
    private $admin_pages = [];
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->database = new Database();
        $this->cache = new Cache();
        $this->logger = new Logger();
        $this->background_processor = new BackgroundProcessor();
    }
    
    /**
     * Initialize admin functionality
     */
    public function init() {
        // Admin menu
        add_action('admin_menu', [$this, 'add_admin_menu']);
        add_action('admin_init', [$this, 'register_settings']);
        
        // Admin scripts and styles
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_assets']);
        
        // AJAX handlers
        add_action('wp_ajax_ufio_test_api', [$this, 'ajax_test_api']);
        add_action('wp_ajax_ufio_get_stats', [$this, 'ajax_get_stats']);
        add_action('wp_ajax_ufio_bulk_process', [$this, 'ajax_bulk_process']);
        add_action('wp_ajax_ufio_clear_cache', [$this, 'ajax_clear_cache']);
        
        // Dashboard widget
        add_action('wp_dashboard_setup', [$this, 'add_dashboard_widget']);
        
        // Post meta boxes
        add_action('add_meta_boxes', [$this, 'add_meta_boxes']);
        add_action('save_post', [$this, 'save_meta_box_data']);
        
        // Media library enhancements
        add_filter('attachment_fields_to_edit', [$this, 'add_attachment_fields'], 10, 2);
        add_filter('attachment_fields_to_save', [$this, 'save_attachment_fields'], 10, 2);
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        // Main menu page
        $this->admin_pages['main'] = add_menu_page(
            __('Ultra Image Optimizer', 'ultra-featured-image-optimizer'),
            __('Ultra Images', 'ultra-featured-image-optimizer'),
            'manage_options',
            'ultra-featured-image-optimizer',
            [$this, 'render_main_page'],
            'dashicons-format-image',
            30
        );
        
        // Settings page
        $this->admin_pages['settings'] = add_submenu_page(
            'ultra-featured-image-optimizer',
            __('Settings', 'ultra-featured-image-optimizer'),
            __('Settings', 'ultra-featured-image-optimizer'),
            'manage_options',
            'ufio-settings',
            [$this, 'render_settings_page']
        );
        
        // Tools page
        $this->admin_pages['tools'] = add_submenu_page(
            'ultra-featured-image-optimizer',
            __('Tools', 'ultra-featured-image-optimizer'),
            __('Tools', 'ultra-featured-image-optimizer'),
            'manage_options',
            'ufio-tools',
            [$this, 'render_tools_page']
        );
        
        // Analytics page
        $this->admin_pages['analytics'] = add_submenu_page(
            'ultra-featured-image-optimizer',
            __('Analytics', 'ultra-featured-image-optimizer'),
            __('Analytics', 'ultra-featured-image-optimizer'),
            'manage_options',
            'ufio-analytics',
            [$this, 'render_analytics_page']
        );
    }
    
    /**
     * Register settings
     */
    public function register_settings() {
        register_setting('ufio_settings', 'ufio_options', [
            'sanitize_callback' => [$this, 'sanitize_options'],
        ]);
        
        // General settings section
        add_settings_section(
            'ufio_general',
            __('General Settings', 'ultra-featured-image-optimizer'),
            [$this, 'render_general_section'],
            'ufio_settings'
        );
        
        // AI settings section
        add_settings_section(
            'ufio_ai',
            __('AI Configuration', 'ultra-featured-image-optimizer'),
            [$this, 'render_ai_section'],
            'ufio_settings'
        );
        
        // SEO settings section
        add_settings_section(
            'ufio_seo',
            __('SEO Optimization', 'ultra-featured-image-optimizer'),
            [$this, 'render_seo_section'],
            'ufio_settings'
        );
        
        // Performance settings section
        add_settings_section(
            'ufio_performance',
            __('Performance', 'ultra-featured-image-optimizer'),
            [$this, 'render_performance_section'],
            'ufio_settings'
        );
        
        $this->add_settings_fields();
    }
    
    /**
     * Add settings fields
     */
    private function add_settings_fields() {
        $fields = [
            // General settings
            'auto_assign_featured' => [
                'title' => __('Auto-assign Featured Images', 'ultra-featured-image-optimizer'),
                'section' => 'ufio_general',
                'type' => 'checkbox',
            ],
            'auto_insert_content' => [
                'title' => __('Auto-insert Content Images', 'ultra-featured-image-optimizer'),
                'section' => 'ufio_general',
                'type' => 'checkbox',
            ],
            'included_post_types' => [
                'title' => __('Included Post Types', 'ultra-featured-image-optimizer'),
                'section' => 'ufio_general',
                'type' => 'multiselect',
                'options' => $this->get_post_types(),
            ],
            
            // AI settings
            'use_ai_processing' => [
                'title' => __('Enable AI Processing', 'ultra-featured-image-optimizer'),
                'section' => 'ufio_ai',
                'type' => 'checkbox',
            ],
            'gemini_api_key' => [
                'title' => __('Gemini API Key', 'ultra-featured-image-optimizer'),
                'section' => 'ufio_ai',
                'type' => 'password',
                'description' => __('Get your API key from Google AI Studio', 'ultra-featured-image-optimizer'),
            ],
            
            // SEO settings
            'enable_seo_optimization' => [
                'title' => __('Enable SEO Optimization', 'ultra-featured-image-optimizer'),
                'section' => 'ufio_seo',
                'type' => 'checkbox',
            ],
            'enable_structured_data' => [
                'title' => __('Enable Structured Data', 'ultra-featured-image-optimizer'),
                'section' => 'ufio_seo',
                'type' => 'checkbox',
            ],
            'enable_lazy_loading' => [
                'title' => __('Enable Lazy Loading', 'ultra-featured-image-optimizer'),
                'section' => 'ufio_seo',
                'type' => 'checkbox',
            ],
            
            // Performance settings
            'background_processing' => [
                'title' => __('Background Processing', 'ultra-featured-image-optimizer'),
                'section' => 'ufio_performance',
                'type' => 'checkbox',
            ],
            'cache_duration' => [
                'title' => __('Cache Duration (seconds)', 'ultra-featured-image-optimizer'),
                'section' => 'ufio_performance',
                'type' => 'number',
                'min' => 300,
                'max' => 604800,
            ],
        ];
        
        foreach ($fields as $field_id => $field) {
            add_settings_field(
                $field_id,
                $field['title'],
                [$this, 'render_field'],
                'ufio_settings',
                $field['section'],
                array_merge($field, ['field_id' => $field_id])
            );
        }
    }
    
    /**
     * Enqueue admin assets
     * 
     * @param string $hook Current admin page hook
     */
    public function enqueue_admin_assets($hook) {
        // Only load on plugin pages
        if (!in_array($hook, $this->admin_pages)) {
            return;
        }
        
        // Styles
        wp_enqueue_style(
            'ufio-admin',
            UFIO_PLUGIN_URL . 'admin/css/admin.css',
            [],
            UFIO_VERSION
        );
        
        // Scripts
        wp_enqueue_script(
            'ufio-admin',
            UFIO_PLUGIN_URL . 'admin/js/admin.js',
            ['jquery', 'wp-util'],
            UFIO_VERSION,
            true
        );
        
        // Localize script
        wp_localize_script('ufio-admin', 'ufioAdmin', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('ufio_admin_nonce'),
            'strings' => [
                'processing' => __('Processing...', 'ultra-featured-image-optimizer'),
                'completed' => __('Completed', 'ultra-featured-image-optimizer'),
                'error' => __('Error', 'ultra-featured-image-optimizer'),
                'confirm_bulk' => __('Are you sure you want to process all posts?', 'ultra-featured-image-optimizer'),
                'confirm_clear_cache' => __('Are you sure you want to clear all cache?', 'ultra-featured-image-optimizer'),
            ],
        ]);
    }
    
    /**
     * Render main page
     */
    public function render_main_page() {
        $stats = $this->get_dashboard_stats();
        
        include UFIO_TEMPLATES_DIR . 'admin/main-page.php';
    }
    
    /**
     * Render settings page
     */
    public function render_settings_page() {
        if (isset($_GET['settings-updated'])) {
            add_settings_error(
                'ufio_messages',
                'ufio_message',
                __('Settings saved successfully!', 'ultra-featured-image-optimizer'),
                'updated'
            );
        }
        
        include UFIO_TEMPLATES_DIR . 'admin/settings-page.php';
    }
    
    /**
     * Render tools page
     */
    public function render_tools_page() {
        include UFIO_TEMPLATES_DIR . 'admin/tools-page.php';
    }
    
    /**
     * Render analytics page
     */
    public function render_analytics_page() {
        $analytics_data = $this->get_analytics_data();
        
        include UFIO_TEMPLATES_DIR . 'admin/analytics-page.php';
    }
    
    /**
     * Get dashboard statistics
     * 
     * @return array Statistics
     */
    private function get_dashboard_stats() {
        $cache_key = 'dashboard_stats';
        $cached_stats = $this->cache->get($cache_key, Cache::GROUP_STATS);
        
        if ($cached_stats !== false) {
            return $cached_stats;
        }
        
        $stats = [
            'total_posts' => wp_count_posts()->publish,
            'posts_with_featured' => $this->count_posts_with_featured_images(),
            'total_images' => wp_count_attachments('image')['image'],
            'optimized_images' => $this->count_optimized_images(),
            'queue_pending' => $this->background_processor->get_queue_status()['pending'],
            'cache_size' => $this->cache->get_stats()['file_cache_size'],
        ];
        
        $stats['featured_coverage'] = $stats['total_posts'] > 0 
            ? round(($stats['posts_with_featured'] / $stats['total_posts']) * 100, 1)
            : 0;
        
        $stats['optimization_coverage'] = $stats['total_images'] > 0
            ? round(($stats['optimized_images'] / $stats['total_images']) * 100, 1)
            : 0;
        
        // Cache for 5 minutes
        $this->cache->set($cache_key, $stats, Cache::GROUP_STATS, 300);
        
        return $stats;
    }
    
    /**
     * Get analytics data
     * 
     * @return array Analytics data
     */
    private function get_analytics_data() {
        return [
            'performance_stats' => $this->logger->get_performance_stats(30),
            'processing_history' => $this->get_processing_history(),
            'error_summary' => $this->get_error_summary(),
            'cache_stats' => $this->cache->get_stats(),
        ];
    }
    
    /**
     * Count posts with featured images
     * 
     * @return int Count
     */
    private function count_posts_with_featured_images() {
        global $wpdb;
        
        return (int) $wpdb->get_var(
            "SELECT COUNT(DISTINCT p.ID) 
             FROM {$wpdb->posts} p 
             INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id 
             WHERE p.post_status = 'publish' 
             AND p.post_type IN ('post', 'page') 
             AND pm.meta_key = '_thumbnail_id' 
             AND pm.meta_value != ''"
        );
    }
    
    /**
     * Count optimized images
     * 
     * @return int Count
     */
    private function count_optimized_images() {
        global $wpdb;
        
        return (int) $wpdb->get_var(
            "SELECT COUNT(*) FROM {$wpdb->prefix}ufio_image_metadata 
             WHERE last_optimized IS NOT NULL"
        );
    }
    
    /**
     * Get processing history
     * 
     * @return array Processing history
     */
    private function get_processing_history() {
        global $wpdb;
        
        return $wpdb->get_results(
            "SELECT 
                DATE(created_at) as date,
                COUNT(*) as total_processed,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as successful,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed
             FROM {$wpdb->prefix}ufio_processing_queue 
             WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
             GROUP BY DATE(created_at)
             ORDER BY date DESC",
            ARRAY_A
        );
    }
    
    /**
     * Get error summary
     * 
     * @return array Error summary
     */
    private function get_error_summary() {
        $logs = $this->logger->get_recent_logs(100, Logger::LEVEL_ERROR);
        
        $error_types = [];
        foreach ($logs as $log) {
            $error_type = $this->categorize_error($log['message']);
            $error_types[$error_type] = ($error_types[$error_type] ?? 0) + 1;
        }
        
        return $error_types;
    }
    
    /**
     * Categorize error message
     * 
     * @param string $message Error message
     * @return string Error category
     */
    private function categorize_error($message) {
        if (strpos($message, 'API') !== false) {
            return 'API Errors';
        } elseif (strpos($message, 'image') !== false) {
            return 'Image Processing';
        } elseif (strpos($message, 'database') !== false) {
            return 'Database Errors';
        } elseif (strpos($message, 'cache') !== false) {
            return 'Cache Errors';
        } else {
            return 'Other Errors';
        }
    }
    
    /**
     * Get available post types
     * 
     * @return array Post types
     */
    private function get_post_types() {
        $post_types = get_post_types(['public' => true], 'objects');
        $options = [];
        
        foreach ($post_types as $post_type) {
            $options[$post_type->name] = $post_type->label;
        }
        
        return $options;
    }
    
    /**
     * Render settings field
     * 
     * @param array $args Field arguments
     */
    public function render_field($args) {
        $field_id = $args['field_id'];
        $type = $args['type'];
        $value = ufio()->get_option($field_id, '');
        $name = "ufio_options[{$field_id}]";
        
        switch ($type) {
            case 'checkbox':
                printf(
                    '<input type="checkbox" id="%s" name="%s" value="1" %s />',
                    esc_attr($field_id),
                    esc_attr($name),
                    checked(1, $value, false)
                );
                break;
                
            case 'password':
                printf(
                    '<input type="password" id="%s" name="%s" value="%s" class="regular-text" />',
                    esc_attr($field_id),
                    esc_attr($name),
                    esc_attr($value)
                );
                break;
                
            case 'number':
                printf(
                    '<input type="number" id="%s" name="%s" value="%s" min="%d" max="%d" class="small-text" />',
                    esc_attr($field_id),
                    esc_attr($name),
                    esc_attr($value),
                    $args['min'] ?? 0,
                    $args['max'] ?? 999999
                );
                break;
                
            case 'multiselect':
                $selected_values = is_array($value) ? $value : [];
                echo '<select multiple id="' . esc_attr($field_id) . '" name="' . esc_attr($name) . '[]" class="regular-text">';
                foreach ($args['options'] as $option_value => $option_label) {
                    printf(
                        '<option value="%s" %s>%s</option>',
                        esc_attr($option_value),
                        selected(in_array($option_value, $selected_values), true, false),
                        esc_html($option_label)
                    );
                }
                echo '</select>';
                break;
                
            default:
                printf(
                    '<input type="text" id="%s" name="%s" value="%s" class="regular-text" />',
                    esc_attr($field_id),
                    esc_attr($name),
                    esc_attr($value)
                );
        }
        
        if (!empty($args['description'])) {
            printf('<p class="description">%s</p>', esc_html($args['description']));
        }
    }
    
    /**
     * Sanitize options
     * 
     * @param array $options Raw options
     * @return array Sanitized options
     */
    public function sanitize_options($options) {
        $sanitized = [];
        
        foreach ($options as $key => $value) {
            switch ($key) {
                case 'gemini_api_key':
                    $sanitized[$key] = sanitize_text_field($value);
                    break;
                    
                case 'cache_duration':
                    $sanitized[$key] = max(300, min(604800, absint($value)));
                    break;
                    
                case 'included_post_types':
                    $sanitized[$key] = is_array($value) ? array_map('sanitize_text_field', $value) : [];
                    break;
                    
                default:
                    if (is_bool($value) || in_array($value, ['0', '1', 0, 1])) {
                        $sanitized[$key] = (bool) $value;
                    } else {
                        $sanitized[$key] = sanitize_text_field($value);
                    }
            }
        }
        
        return $sanitized;
    }
    
    // Section callbacks
    public function render_general_section() {
        echo '<p>' . __('Configure general plugin behavior and automation settings.', 'ultra-featured-image-optimizer') . '</p>';
    }
    
    public function render_ai_section() {
        echo '<p>' . __('Configure AI-powered image analysis and optimization.', 'ultra-featured-image-optimizer') . '</p>';
    }
    
    public function render_seo_section() {
        echo '<p>' . __('Configure SEO optimization features for better search engine visibility.', 'ultra-featured-image-optimizer') . '</p>';
    }
    
    public function render_performance_section() {
        echo '<p>' . __('Configure performance and caching settings.', 'ultra-featured-image-optimizer') . '</p>';
    }
    
    /**
     * Add dashboard widget
     */
    public function add_dashboard_widget() {
        wp_add_dashboard_widget(
            'ufio_dashboard_widget',
            __('Ultra Image Optimizer Status', 'ultra-featured-image-optimizer'),
            [$this, 'render_dashboard_widget']
        );
    }
    
    /**
     * Render dashboard widget
     */
    public function render_dashboard_widget() {
        $stats = $this->get_dashboard_stats();
        include UFIO_TEMPLATES_DIR . 'admin/dashboard-widget.php';
    }
    
    /**
     * Add meta boxes
     */
    public function add_meta_boxes() {
        $post_types = ufio()->get_option('included_post_types', ['post', 'page']);
        
        foreach ($post_types as $post_type) {
            add_meta_box(
                'ufio_image_optimization',
                __('Image Optimization', 'ultra-featured-image-optimizer'),
                [$this, 'render_meta_box'],
                $post_type,
                'side',
                'default'
            );
        }
    }
    
    /**
     * Render meta box
     * 
     * @param \WP_Post $post Post object
     */
    public function render_meta_box($post) {
        wp_nonce_field('ufio_meta_box', 'ufio_meta_box_nonce');
        
        $processing_status = get_post_meta($post->ID, '_ufio_processing_status', true);
        $last_processed = get_post_meta($post->ID, '_ufio_last_processed', true);
        
        include UFIO_TEMPLATES_DIR . 'admin/meta-box.php';
    }
    
    /**
     * Save meta box data
     * 
     * @param int $post_id Post ID
     */
    public function save_meta_box_data($post_id) {
        if (!isset($_POST['ufio_meta_box_nonce']) || 
            !wp_verify_nonce($_POST['ufio_meta_box_nonce'], 'ufio_meta_box')) {
            return;
        }
        
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }
        
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
        
        // Process the post if requested
        if (isset($_POST['ufio_process_images'])) {
            $this->background_processor->add_to_queue(
                'process_featured_image',
                $post_id,
                'post',
                BackgroundProcessor::PRIORITY_HIGH
            );
            
            update_post_meta($post_id, '_ufio_processing_status', 'queued');
        }
    }
    
    /**
     * Add attachment fields
     * 
     * @param array $fields Existing fields
     * @param \WP_Post $post Attachment post
     * @return array Modified fields
     */
    public function add_attachment_fields($fields, $post) {
        if (!wp_attachment_is_image($post->ID)) {
            return $fields;
        }
        
        $seo_optimizer = new SEOOptimizer();
        $seo_score = $seo_optimizer->calculate_seo_score($post->ID);
        $recommendations = $seo_optimizer->get_seo_recommendations($post->ID);
        
        $fields['ufio_seo_score'] = [
            'label' => __('SEO Score', 'ultra-featured-image-optimizer'),
            'input' => 'html',
            'html' => sprintf(
                '<div class="ufio-seo-score">
                    <div class="score">%.1f/1.0</div>
                    <div class="recommendations">%s</div>
                </div>',
                $seo_score,
                $this->format_recommendations($recommendations)
            ),
        ];
        
        return $fields;
    }
    
    /**
     * Save attachment fields
     * 
     * @param array $post Attachment data
     * @param array $attachment Attachment fields
     * @return array Modified attachment data
     */
    public function save_attachment_fields($post, $attachment) {
        // Custom field processing would go here
        return $post;
    }
    
    /**
     * Format SEO recommendations
     * 
     * @param array $recommendations Recommendations
     * @return string Formatted HTML
     */
    private function format_recommendations($recommendations) {
        if (empty($recommendations)) {
            return '<span class="success">' . __('All good!', 'ultra-featured-image-optimizer') . '</span>';
        }
        
        $html = '<ul class="recommendations-list">';
        foreach ($recommendations as $rec) {
            $html .= sprintf(
                '<li class="%s">%s</li>',
                esc_attr($rec['type']),
                esc_html($rec['message'])
            );
        }
        $html .= '</ul>';
        
        return $html;
    }
    
    // AJAX handlers
    public function ajax_test_api() {
        check_ajax_referer('ufio_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
        }
        
        $ai_handler = new AIHandler();
        $result = $ai_handler->test_api_connection();
        
        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result);
        }
    }
    
    public function ajax_get_stats() {
        check_ajax_referer('ufio_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
        }
        
        $stats = $this->get_dashboard_stats();
        wp_send_json_success($stats);
    }
    
    public function ajax_bulk_process() {
        check_ajax_referer('ufio_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
        }
        
        $post_type = sanitize_text_field($_POST['post_type'] ?? 'post');
        $limit = absint($_POST['limit'] ?? 50);
        
        // Get posts without featured images
        $posts = get_posts([
            'post_type' => $post_type,
            'post_status' => 'publish',
            'posts_per_page' => $limit,
            'meta_query' => [
                [
                    'key' => '_thumbnail_id',
                    'compare' => 'NOT EXISTS',
                ],
            ],
        ]);
        
        $post_ids = wp_list_pluck($posts, 'ID');
        
        if (!empty($post_ids)) {
            $queue_id = $this->background_processor->add_to_queue(
                'bulk_process_posts',
                0,
                'bulk',
                BackgroundProcessor::PRIORITY_NORMAL,
                ['post_ids' => $post_ids]
            );
            
            wp_send_json_success([
                'message' => sprintf(__('Added %d posts to processing queue', 'ultra-featured-image-optimizer'), count($post_ids)),
                'queue_id' => $queue_id,
                'post_count' => count($post_ids),
            ]);
        } else {
            wp_send_json_error(['message' => __('No posts found to process', 'ultra-featured-image-optimizer')]);
        }
    }
    
    public function ajax_clear_cache() {
        check_ajax_referer('ufio_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
        }
        
        $this->cache->clear_all();
        
        wp_send_json_success(['message' => __('Cache cleared successfully', 'ultra-featured-image-optimizer')]);
    }
}
