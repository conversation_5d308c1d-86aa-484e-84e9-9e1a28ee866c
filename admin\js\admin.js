/**
 * Ultra Featured Image Optimizer - Admin JavaScript
 * Modern, efficient admin interface interactions
 */

(function($) {
    'use strict';

    // Global UFIO Admin object
    window.UFIOAdmin = {
        init: function() {
            this.bindEvents();
            this.initComponents();
            this.startPeriodicUpdates();
        },

        bindEvents: function() {
            // API Test
            $(document).on('click', '.ufio-test-api', this.testAPI);
            
            // Bulk Processing
            $(document).on('click', '.ufio-bulk-process', this.bulkProcess);
            
            // Clear Cache
            $(document).on('click', '.ufio-clear-cache', this.clearCache);
            
            // Process Single Post
            $(document).on('click', '.ufio-process-single', this.processSingle);
            
            // Refresh Stats
            $(document).on('click', '.ufio-refresh-stats', this.refreshStats);
            
            // Settings Form
            $(document).on('submit', '#ufio-settings-form', this.saveSettings);
            
            // Tab Navigation
            $(document).on('click', '.ufio-tab-nav a', this.switchTab);
        },

        initComponents: function() {
            // Initialize progress bars
            this.initProgressBars();
            
            // Initialize tooltips
            this.initTooltips();
            
            // Initialize charts if available
            this.initCharts();
            
            // Auto-refresh queue status
            this.refreshQueueStatus();
        },

        startPeriodicUpdates: function() {
            // Refresh stats every 30 seconds
            setInterval(() => {
                this.refreshStats(true);
            }, 30000);
            
            // Refresh queue status every 10 seconds
            setInterval(() => {
                this.refreshQueueStatus();
            }, 10000);
        },

        testAPI: function(e) {
            e.preventDefault();
            
            const $button = $(this);
            const $result = $('.ufio-api-test-result');
            
            $button.prop('disabled', true).html('<span class="ufio-spinner"></span> ' + ufioAdmin.strings.processing);
            
            $.ajax({
                url: ufioAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'ufio_test_api',
                    nonce: ufioAdmin.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $result.html('<div class="ufio-notice success"><span class="ufio-notice-icon">✓</span>' + response.data.message + '</div>');
                    } else {
                        $result.html('<div class="ufio-notice error"><span class="ufio-notice-icon">✗</span>' + response.data.message + '</div>');
                    }
                },
                error: function() {
                    $result.html('<div class="ufio-notice error"><span class="ufio-notice-icon">✗</span>Connection failed</div>');
                },
                complete: function() {
                    $button.prop('disabled', false).html('Test API Connection');
                }
            });
        },

        bulkProcess: function(e) {
            e.preventDefault();
            
            if (!confirm(ufioAdmin.strings.confirm_bulk)) {
                return;
            }
            
            const $button = $(this);
            const $progress = $('.ufio-bulk-progress');
            const postType = $button.data('post-type') || 'post';
            const limit = $button.data('limit') || 50;
            
            $button.prop('disabled', true).html('<span class="ufio-spinner"></span> ' + ufioAdmin.strings.processing);
            $progress.show();
            
            $.ajax({
                url: ufioAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'ufio_bulk_process',
                    nonce: ufioAdmin.nonce,
                    post_type: postType,
                    limit: limit
                },
                success: function(response) {
                    if (response.success) {
                        UFIOAdmin.showNotice('success', response.data.message);
                        UFIOAdmin.startProgressTracking(response.data.queue_id);
                    } else {
                        UFIOAdmin.showNotice('error', response.data.message);
                        $progress.hide();
                    }
                },
                error: function() {
                    UFIOAdmin.showNotice('error', 'Request failed');
                    $progress.hide();
                },
                complete: function() {
                    $button.prop('disabled', false).html('Start Bulk Processing');
                }
            });
        },

        clearCache: function(e) {
            e.preventDefault();
            
            if (!confirm(ufioAdmin.strings.confirm_clear_cache)) {
                return;
            }
            
            const $button = $(this);
            
            $button.prop('disabled', true).html('<span class="ufio-spinner"></span> Clearing...');
            
            $.ajax({
                url: ufioAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'ufio_clear_cache',
                    nonce: ufioAdmin.nonce
                },
                success: function(response) {
                    if (response.success) {
                        UFIOAdmin.showNotice('success', response.data.message);
                        UFIOAdmin.refreshStats();
                    } else {
                        UFIOAdmin.showNotice('error', response.data.message);
                    }
                },
                error: function() {
                    UFIOAdmin.showNotice('error', 'Request failed');
                },
                complete: function() {
                    $button.prop('disabled', false).html('Clear Cache');
                }
            });
        },

        processSingle: function(e) {
            e.preventDefault();
            
            const $button = $(this);
            const postId = $button.data('post-id');
            const actionType = $button.data('action-type') || 'process_featured_image';
            
            $button.prop('disabled', true).html('<span class="ufio-spinner"></span> Processing...');
            
            $.ajax({
                url: ufioAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'ufio_process_single',
                    nonce: ufioAdmin.nonce,
                    item_id: postId,
                    action_type: actionType
                },
                success: function(response) {
                    if (response.success) {
                        UFIOAdmin.showNotice('success', 'Added to processing queue');
                    } else {
                        UFIOAdmin.showNotice('error', response.data.message);
                    }
                },
                error: function() {
                    UFIOAdmin.showNotice('error', 'Request failed');
                },
                complete: function() {
                    $button.prop('disabled', false).html('Process Images');
                }
            });
        },

        refreshStats: function(silent = false) {
            if (!silent) {
                $('.ufio-stats-loading').show();
            }
            
            $.ajax({
                url: ufioAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'ufio_get_stats',
                    nonce: ufioAdmin.nonce
                },
                success: function(response) {
                    if (response.success) {
                        UFIOAdmin.updateStatsDisplay(response.data);
                    }
                },
                complete: function() {
                    if (!silent) {
                        $('.ufio-stats-loading').hide();
                    }
                }
            });
        },

        refreshQueueStatus: function() {
            $.ajax({
                url: ufioAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'ufio_get_queue_status',
                    nonce: ufioAdmin.nonce
                },
                success: function(response) {
                    if (response.success) {
                        UFIOAdmin.updateQueueDisplay(response.data.status);
                    }
                }
            });
        },

        updateStatsDisplay: function(stats) {
            // Update stat cards
            $('.ufio-stat-number[data-stat="total_posts"]').text(stats.total_posts || 0);
            $('.ufio-stat-number[data-stat="posts_with_featured"]').text(stats.posts_with_featured || 0);
            $('.ufio-stat-number[data-stat="total_images"]').text(stats.total_images || 0);
            $('.ufio-stat-number[data-stat="optimized_images"]').text(stats.optimized_images || 0);
            
            // Update progress bars
            const featuredCoverage = stats.featured_coverage || 0;
            const optimizationCoverage = stats.optimization_coverage || 0;
            
            $('.ufio-progress-bar[data-progress="featured"]').css('width', featuredCoverage + '%');
            $('.ufio-progress-text[data-progress="featured"]').text(featuredCoverage + '%');
            
            $('.ufio-progress-bar[data-progress="optimization"]').css('width', optimizationCoverage + '%');
            $('.ufio-progress-text[data-progress="optimization"]').text(optimizationCoverage + '%');
            
            // Update cache size
            if (stats.cache_size) {
                $('.ufio-cache-size').text(UFIOAdmin.formatBytes(stats.cache_size));
            }
        },

        updateQueueDisplay: function(queueStatus) {
            $('.ufio-queue-number[data-queue="pending"]').text(queueStatus.pending || 0);
            $('.ufio-queue-number[data-queue="processing"]').text(queueStatus.processing || 0);
            $('.ufio-queue-number[data-queue="completed"]').text(queueStatus.completed || 0);
            $('.ufio-queue-number[data-queue="failed"]').text(queueStatus.failed || 0);
        },

        startProgressTracking: function(queueId) {
            const $progress = $('.ufio-bulk-progress');
            const $progressBar = $progress.find('.ufio-progress-bar');
            const $progressText = $progress.find('.ufio-progress-text');
            
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 10;
                if (progress > 95) progress = 95;
                
                $progressBar.css('width', progress + '%');
                $progressText.text(Math.round(progress) + '%');
                
                // Check if processing is complete
                UFIOAdmin.refreshQueueStatus();
                
                if (progress >= 95) {
                    clearInterval(interval);
                    setTimeout(() => {
                        $progressBar.css('width', '100%');
                        $progressText.text('100%');
                        setTimeout(() => {
                            $progress.hide();
                            UFIOAdmin.refreshStats();
                        }, 1000);
                    }, 2000);
                }
            }, 1000);
        },

        initProgressBars: function() {
            $('.ufio-progress-bar').each(function() {
                const $bar = $(this);
                const width = $bar.data('width') || 0;
                
                setTimeout(() => {
                    $bar.css('width', width + '%');
                }, 100);
            });
        },

        initTooltips: function() {
            $('[data-tooltip]').each(function() {
                const $element = $(this);
                const tooltip = $element.data('tooltip');
                
                $element.attr('title', tooltip);
            });
        },

        initCharts: function() {
            // Initialize charts if Chart.js is available
            if (typeof Chart !== 'undefined') {
                this.initPerformanceChart();
                this.initProcessingChart();
            }
        },

        initPerformanceChart: function() {
            const $canvas = $('#ufio-performance-chart');
            if ($canvas.length === 0) return;
            
            const ctx = $canvas[0].getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [], // Will be populated with data
                    datasets: [{
                        label: 'Processing Time (ms)',
                        data: [],
                        borderColor: 'rgb(34, 113, 177)',
                        backgroundColor: 'rgba(34, 113, 177, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        },

        initProcessingChart: function() {
            const $canvas = $('#ufio-processing-chart');
            if ($canvas.length === 0) return;
            
            const ctx = $canvas[0].getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Completed', 'Failed', 'Pending'],
                    datasets: [{
                        data: [0, 0, 0], // Will be populated with data
                        backgroundColor: [
                            'rgb(0, 163, 42)',
                            'rgb(214, 54, 56)',
                            'rgb(219, 166, 23)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        },

        switchTab: function(e) {
            e.preventDefault();
            
            const $link = $(this);
            const target = $link.attr('href');
            
            // Update active tab
            $('.ufio-tab-nav a').removeClass('nav-tab-active');
            $link.addClass('nav-tab-active');
            
            // Show target content
            $('.ufio-tab-content').hide();
            $(target).show();
        },

        saveSettings: function(e) {
            const $form = $(this);
            const $submitButton = $form.find('input[type="submit"]');
            
            $submitButton.prop('disabled', true).val('Saving...');
            
            // Form will submit normally, this just provides visual feedback
            setTimeout(() => {
                $submitButton.prop('disabled', false).val('Save Settings');
            }, 2000);
        },

        showNotice: function(type, message) {
            const $notice = $('<div class="ufio-notice ' + type + '">' +
                '<span class="ufio-notice-icon">' + (type === 'success' ? '✓' : '✗') + '</span>' +
                message +
                '</div>');
            
            $('.ufio-notices').append($notice);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                $notice.fadeOut(() => {
                    $notice.remove();
                });
            }, 5000);
        },

        formatBytes: function(bytes, decimals = 2) {
            if (bytes === 0) return '0 Bytes';
            
            const k = 1024;
            const dm = decimals < 0 ? 0 : decimals;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
        },

        debounce: function(func, wait, immediate) {
            let timeout;
            return function executedFunction() {
                const context = this;
                const args = arguments;
                const later = function() {
                    timeout = null;
                    if (!immediate) func.apply(context, args);
                };
                const callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(context, args);
            };
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        UFIOAdmin.init();
    });

})(jQuery);
