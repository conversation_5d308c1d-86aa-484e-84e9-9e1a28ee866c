<?php
/**
 * Cache management class
 * 
 * @package UltraFeaturedImageOptimizer
 * @subpackage Cache
 */

namespace UltraFeaturedImageOptimizer;

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Multi-layer cache management class
 */
class Cache {
    
    /**
     * Cache types
     */
    const TYPE_OBJECT = 'object';
    const TYPE_TRANSIENT = 'transient';
    const TYPE_DATABASE = 'database';
    const TYPE_FILE = 'file';
    
    /**
     * Cache groups
     */
    const GROUP_AI = 'ufio_ai';
    const GROUP_IMAGE = 'ufio_image';
    const GROUP_SEO = 'ufio_seo';
    const GROUP_STATS = 'ufio_stats';
    
    /**
     * Default cache duration (24 hours)
     */
    const DEFAULT_DURATION = DAY_IN_SECONDS;
    
    /**
     * Database instance
     * @var Database
     */
    private $database;
    
    /**
     * Memory cache for current request
     * @var array
     */
    private $memory_cache = [];
    
    /**
     * Cache statistics
     * @var array
     */
    private $stats = [
        'hits' => 0,
        'misses' => 0,
        'sets' => 0,
        'deletes' => 0,
    ];
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->database = new Database();
        
        // Initialize object cache group
        wp_cache_add_global_groups([
            self::GROUP_AI,
            self::GROUP_IMAGE,
            self::GROUP_SEO,
            self::GROUP_STATS,
        ]);
    }
    
    /**
     * Get cached data with fallback strategy
     * 
     * @param string $key Cache key
     * @param string $group Cache group
     * @param int $duration Cache duration in seconds
     * @return mixed|false Cached data or false if not found
     */
    public function get($key, $group = self::GROUP_IMAGE, $duration = self::DEFAULT_DURATION) {
        $cache_key = $this->generate_key($key, $group);
        
        // 1. Check memory cache first (fastest)
        if (isset($this->memory_cache[$cache_key])) {
            $this->stats['hits']++;
            return $this->memory_cache[$cache_key];
        }
        
        // 2. Check WordPress object cache
        $data = wp_cache_get($key, $group);
        if ($data !== false) {
            $this->memory_cache[$cache_key] = $data;
            $this->stats['hits']++;
            return $data;
        }
        
        // 3. Check transient cache
        $transient_key = $this->get_transient_key($key, $group);
        $data = get_transient($transient_key);
        if ($data !== false) {
            // Store in object cache for faster access
            wp_cache_set($key, $data, $group, $duration);
            $this->memory_cache[$cache_key] = $data;
            $this->stats['hits']++;
            return $data;
        }
        
        // 4. Check database cache (for persistent data)
        if ($this->should_use_database_cache($group)) {
            $data = $this->get_from_database($cache_key);
            if ($data !== false) {
                // Store in higher-level caches
                wp_cache_set($key, $data, $group, $duration);
                set_transient($transient_key, $data, $duration);
                $this->memory_cache[$cache_key] = $data;
                $this->stats['hits']++;
                return $data;
            }
        }
        
        // 5. Check file cache (for large data)
        if ($this->should_use_file_cache($group)) {
            $data = $this->get_from_file($cache_key);
            if ($data !== false) {
                // Store in higher-level caches
                wp_cache_set($key, $data, $group, min($duration, HOUR_IN_SECONDS));
                $this->memory_cache[$cache_key] = $data;
                $this->stats['hits']++;
                return $data;
            }
        }
        
        $this->stats['misses']++;
        return false;
    }
    
    /**
     * Set cached data across all cache layers
     * 
     * @param string $key Cache key
     * @param mixed $data Data to cache
     * @param string $group Cache group
     * @param int $duration Cache duration in seconds
     * @return bool Success
     */
    public function set($key, $data, $group = self::GROUP_IMAGE, $duration = self::DEFAULT_DURATION) {
        $cache_key = $this->generate_key($key, $group);
        
        // 1. Store in memory cache
        $this->memory_cache[$cache_key] = $data;
        
        // 2. Store in WordPress object cache
        wp_cache_set($key, $data, $group, $duration);
        
        // 3. Store in transient cache
        $transient_key = $this->get_transient_key($key, $group);
        set_transient($transient_key, $data, $duration);
        
        // 4. Store in database cache (for persistent data)
        if ($this->should_use_database_cache($group)) {
            $this->set_in_database($cache_key, $data, $group, $duration);
        }
        
        // 5. Store in file cache (for large data)
        if ($this->should_use_file_cache($group) && $this->is_large_data($data)) {
            $this->set_in_file($cache_key, $data, $duration);
        }
        
        $this->stats['sets']++;
        return true;
    }
    
    /**
     * Delete cached data from all cache layers
     * 
     * @param string $key Cache key
     * @param string $group Cache group
     * @return bool Success
     */
    public function delete($key, $group = self::GROUP_IMAGE) {
        $cache_key = $this->generate_key($key, $group);
        
        // Remove from memory cache
        unset($this->memory_cache[$cache_key]);
        
        // Remove from object cache
        wp_cache_delete($key, $group);
        
        // Remove from transient cache
        $transient_key = $this->get_transient_key($key, $group);
        delete_transient($transient_key);
        
        // Remove from database cache
        if ($this->should_use_database_cache($group)) {
            $this->delete_from_database($cache_key);
        }
        
        // Remove from file cache
        if ($this->should_use_file_cache($group)) {
            $this->delete_from_file($cache_key);
        }
        
        $this->stats['deletes']++;
        return true;
    }
    
    /**
     * Clear all cache for a specific group
     * 
     * @param string $group Cache group
     * @return bool Success
     */
    public function clear_group($group) {
        global $wpdb;
        
        // Clear memory cache for group
        foreach ($this->memory_cache as $key => $value) {
            if (strpos($key, $group . '_') === 0) {
                unset($this->memory_cache[$key]);
            }
        }
        
        // Clear transients for group
        $wpdb->query($wpdb->prepare(
            "DELETE FROM {$wpdb->options} 
             WHERE option_name LIKE %s OR option_name LIKE %s",
            $wpdb->esc_like('_transient_ufio_' . $group) . '%',
            $wpdb->esc_like('_transient_timeout_ufio_' . $group) . '%'
        ));
        
        // Clear database cache for group
        if ($this->should_use_database_cache($group)) {
            $wpdb->query($wpdb->prepare(
                "DELETE FROM {$wpdb->prefix}ufio_ai_cache WHERE cache_type = %s",
                $group
            ));
        }
        
        // Clear file cache for group
        if ($this->should_use_file_cache($group)) {
            $this->clear_file_cache_group($group);
        }
        
        return true;
    }
    
    /**
     * Clear all plugin caches
     * 
     * @return bool Success
     */
    public function clear_all() {
        // Clear memory cache
        $this->memory_cache = [];
        
        // Clear object cache groups
        $groups = [self::GROUP_AI, self::GROUP_IMAGE, self::GROUP_SEO, self::GROUP_STATS];
        foreach ($groups as $group) {
            wp_cache_flush_group($group);
        }
        
        // Clear all plugin transients
        global $wpdb;
        $wpdb->query(
            "DELETE FROM {$wpdb->options} 
             WHERE option_name LIKE '_transient_ufio_%' 
             OR option_name LIKE '_transient_timeout_ufio_%'"
        );
        
        // Clear database cache
        $wpdb->query("DELETE FROM {$wpdb->prefix}ufio_ai_cache");
        
        // Clear file cache
        $this->clear_file_cache_all();
        
        return true;
    }
    
    /**
     * Get cache statistics
     * 
     * @return array Cache statistics
     */
    public function get_stats() {
        $total_requests = $this->stats['hits'] + $this->stats['misses'];
        $hit_ratio = $total_requests > 0 ? ($this->stats['hits'] / $total_requests) * 100 : 0;
        
        return array_merge($this->stats, [
            'hit_ratio' => round($hit_ratio, 2),
            'memory_usage' => $this->get_memory_cache_size(),
            'database_entries' => $this->get_database_cache_count(),
            'file_cache_size' => $this->get_file_cache_size(),
        ]);
    }
    
    /**
     * Generate cache key
     * 
     * @param string $key Original key
     * @param string $group Cache group
     * @return string Generated key
     */
    private function generate_key($key, $group) {
        return $group . '_' . md5($key);
    }
    
    /**
     * Get transient key
     * 
     * @param string $key Original key
     * @param string $group Cache group
     * @return string Transient key
     */
    private function get_transient_key($key, $group) {
        return 'ufio_' . $group . '_' . md5($key);
    }
    
    /**
     * Check if group should use database cache
     * 
     * @param string $group Cache group
     * @return bool
     */
    private function should_use_database_cache($group) {
        return in_array($group, [self::GROUP_AI, self::GROUP_SEO]);
    }
    
    /**
     * Check if group should use file cache
     * 
     * @param string $group Cache group
     * @return bool
     */
    private function should_use_file_cache($group) {
        return in_array($group, [self::GROUP_IMAGE, self::GROUP_AI]);
    }
    
    /**
     * Check if data is large enough for file cache
     * 
     * @param mixed $data Data to check
     * @return bool
     */
    private function is_large_data($data) {
        return strlen(serialize($data)) > 10240; // 10KB threshold
    }
    
    /**
     * Get data from database cache
     * 
     * @param string $cache_key Cache key
     * @return mixed|false Cached data or false
     */
    private function get_from_database($cache_key) {
        global $wpdb;
        
        $result = $wpdb->get_row($wpdb->prepare(
            "SELECT cache_data FROM {$wpdb->prefix}ufio_ai_cache 
             WHERE cache_key = %s AND expires_at > %s",
            $cache_key,
            current_time('mysql')
        ));
        
        if ($result) {
            return maybe_unserialize($result->cache_data);
        }
        
        return false;
    }
    
    /**
     * Set data in database cache
     * 
     * @param string $cache_key Cache key
     * @param mixed $data Data to cache
     * @param string $group Cache group
     * @param int $duration Cache duration
     * @return bool Success
     */
    private function set_in_database($cache_key, $data, $group, $duration) {
        global $wpdb;
        
        $expires_at = date('Y-m-d H:i:s', time() + $duration);
        
        return $wpdb->replace(
            $wpdb->prefix . 'ufio_ai_cache',
            [
                'cache_key' => $cache_key,
                'cache_type' => $group,
                'cache_data' => maybe_serialize($data),
                'expires_at' => $expires_at,
            ],
            ['%s', '%s', '%s', '%s']
        ) !== false;
    }
    
    /**
     * Delete data from database cache
     * 
     * @param string $cache_key Cache key
     * @return bool Success
     */
    private function delete_from_database($cache_key) {
        global $wpdb;
        
        return $wpdb->delete(
            $wpdb->prefix . 'ufio_ai_cache',
            ['cache_key' => $cache_key],
            ['%s']
        ) !== false;
    }
    
    /**
     * Get data from file cache
     * 
     * @param string $cache_key Cache key
     * @return mixed|false Cached data or false
     */
    private function get_from_file($cache_key) {
        $file_path = $this->get_cache_file_path($cache_key);
        
        if (!file_exists($file_path)) {
            return false;
        }
        
        $file_data = file_get_contents($file_path);
        if ($file_data === false) {
            return false;
        }
        
        $cache_data = json_decode($file_data, true);
        if (!$cache_data || !isset($cache_data['expires_at'], $cache_data['data'])) {
            return false;
        }
        
        // Check if expired
        if (time() > $cache_data['expires_at']) {
            unlink($file_path);
            return false;
        }
        
        return maybe_unserialize($cache_data['data']);
    }
    
    /**
     * Set data in file cache
     * 
     * @param string $cache_key Cache key
     * @param mixed $data Data to cache
     * @param int $duration Cache duration
     * @return bool Success
     */
    private function set_in_file($cache_key, $data, $duration) {
        $file_path = $this->get_cache_file_path($cache_key);
        $cache_dir = dirname($file_path);
        
        // Create directory if it doesn't exist
        if (!file_exists($cache_dir)) {
            wp_mkdir_p($cache_dir);
        }
        
        $cache_data = [
            'expires_at' => time() + $duration,
            'data' => maybe_serialize($data),
        ];
        
        return file_put_contents($file_path, json_encode($cache_data)) !== false;
    }
    
    /**
     * Delete data from file cache
     * 
     * @param string $cache_key Cache key
     * @return bool Success
     */
    private function delete_from_file($cache_key) {
        $file_path = $this->get_cache_file_path($cache_key);
        
        if (file_exists($file_path)) {
            return unlink($file_path);
        }
        
        return true;
    }
    
    /**
     * Get cache file path
     * 
     * @param string $cache_key Cache key
     * @return string File path
     */
    private function get_cache_file_path($cache_key) {
        $subdir = substr($cache_key, 0, 2);
        return UFIO_CACHE_DIR . $subdir . '/' . $cache_key . '.cache';
    }
    
    /**
     * Clear file cache for specific group
     * 
     * @param string $group Cache group
     */
    private function clear_file_cache_group($group) {
        $cache_dir = UFIO_CACHE_DIR;
        if (!is_dir($cache_dir)) {
            return;
        }
        
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($cache_dir, \FilesystemIterator::SKIP_DOTS)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile() && strpos($file->getFilename(), $group . '_') === 0) {
                unlink($file->getRealPath());
            }
        }
    }
    
    /**
     * Clear all file cache
     */
    private function clear_file_cache_all() {
        $cache_dir = UFIO_CACHE_DIR;
        if (!is_dir($cache_dir)) {
            return;
        }
        
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($cache_dir, \FilesystemIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::CHILD_FIRST
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'cache') {
                unlink($file->getRealPath());
            }
        }
    }
    
    /**
     * Get memory cache size
     * 
     * @return int Size in bytes
     */
    private function get_memory_cache_size() {
        return strlen(serialize($this->memory_cache));
    }
    
    /**
     * Get database cache count
     * 
     * @return int Number of entries
     */
    private function get_database_cache_count() {
        global $wpdb;
        return (int) $wpdb->get_var(
            "SELECT COUNT(*) FROM {$wpdb->prefix}ufio_ai_cache WHERE expires_at > NOW()"
        );
    }
    
    /**
     * Get file cache size
     * 
     * @return int Size in bytes
     */
    private function get_file_cache_size() {
        $cache_dir = UFIO_CACHE_DIR;
        if (!is_dir($cache_dir)) {
            return 0;
        }
        
        $size = 0;
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($cache_dir, \FilesystemIterator::SKIP_DOTS)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'cache') {
                $size += $file->getSize();
            }
        }
        
        return $size;
    }
}
