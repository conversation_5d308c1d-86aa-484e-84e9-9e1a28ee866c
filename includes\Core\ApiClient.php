<?php
/**
 * API Client Class
 * 
 * Handles communication with AI APIs with advanced error handling and optimization
 * 
 * @package AiSeoOptimizerUltra
 * @since 9.0.0
 */

namespace AiSeoOptimizerUltra\Core;

use Exception;
use WP_Error;

/**
 * API Client Class
 * 
 * Manages AI API communications with multiple providers
 */
final class ApiClient {
    private static $instance = null;
    private $rate_limiter = [];
    private $retry_delays = [1, 2, 4, 8]; // Exponential backoff
    
    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Private constructor
     */
    private function __construct() {
        // Initialize rate limiter
        $this->init_rate_limiter();
    }
    
    /**
     * Initialize rate limiter
     */
    private function init_rate_limiter() {
        $max_requests = Options::get('ai_seo_ultra_max_requests_per_hour', 100);
        $this->rate_limiter = [
            'max_requests' => $max_requests,
            'window' => HOUR_IN_SECONDS,
            'requests' => []
        ];
    }
    
    /**
     * Test API connection
     */
    public function test_connection() {
        try {
            $api_key = Options::get('ai_seo_ultra_api_key');
            $provider = Options::get('ai_seo_ultra_api_provider', 'openai');
            
            if (empty($api_key)) {
                return new WP_Error('no_api_key', 'API key is required');
            }
            
            // Simple test request
            $test_prompt = "Test connection. Respond with 'OK' only.";
            $response = $this->make_request($test_prompt, [
                'max_tokens' => 10,
                'temperature' => 0
            ]);
            
            if (is_wp_error($response)) {
                return $response;
            }
            
            return true;
            
        } catch (Exception $e) {
            Logger::error('API connection test failed: ' . $e->getMessage());
            return new WP_Error('connection_failed', $e->getMessage());
        }
    }
    
    /**
     * Generate SEO content
     */
    public function generate_seo_content($post_data, $options = []) {
        try {
            \AiSeoUltraMonitor::check_memory_limit('api_request');
            
            // Check rate limit
            if (!$this->check_rate_limit()) {
                return new WP_Error('rate_limit_exceeded', 'API rate limit exceeded');
            }
            
            // Build prompt
            $prompt = $this->build_seo_prompt($post_data, $options);
            
            // Check cache first
            $cache_key = $this->generate_cache_key($prompt, $options);
            $cached_response = Cache::get($cache_key, 'api');
            
            if ($cached_response !== false) {
                Logger::debug('API response served from cache');
                return $cached_response;
            }
            
            // Make API request
            $response = $this->make_request($prompt, $options);
            
            if (is_wp_error($response)) {
                // Cache error for shorter time
                Cache::set($cache_key, $response, 'api', HOUR_IN_SECONDS);
                return $response;
            }
            
            // Parse response
            $parsed_response = $this->parse_seo_response($response);
            
            // Cache successful response
            Cache::set($cache_key, $parsed_response, 'api');
            
            return $parsed_response;
            
        } catch (Exception $e) {
            Logger::error('API request failed: ' . $e->getMessage());
            return new WP_Error('api_error', $e->getMessage());
        }
    }
    
    /**
     * Make API request with retry logic
     */
    private function make_request($prompt, $options = []) {
        $provider = Options::get('ai_seo_ultra_api_provider', 'openai');
        $api_key = Options::get('ai_seo_ultra_api_key');
        
        if (empty($api_key)) {
            return new WP_Error('no_api_key', 'API key is required');
        }
        
        $attempts = 0;
        $max_attempts = 3;
        
        while ($attempts < $max_attempts) {
            try {
                $response = $this->send_request($provider, $prompt, $options, $api_key);
                
                if (!is_wp_error($response)) {
                    // Record successful request
                    $this->record_request();
                    return $response;
                }
                
                // Check if we should retry
                if ($this->should_retry($response, $attempts)) {
                    $attempts++;
                    $delay = $this->retry_delays[$attempts - 1] ?? 8;
                    Logger::warning("API request failed, retrying in {$delay}s. Attempt {$attempts}/{$max_attempts}");
                    sleep($delay);
                    continue;
                }
                
                return $response;
                
            } catch (Exception $e) {
                $attempts++;
                if ($attempts >= $max_attempts) {
                    return new WP_Error('api_exception', $e->getMessage());
                }
                
                $delay = $this->retry_delays[$attempts - 1] ?? 8;
                Logger::warning("API exception, retrying in {$delay}s: " . $e->getMessage());
                sleep($delay);
            }
        }
        
        return new WP_Error('max_retries_exceeded', 'Maximum retry attempts exceeded');
    }
    
    /**
     * Send request to specific provider
     */
    private function send_request($provider, $prompt, $options, $api_key) {
        switch ($provider) {
            case 'openai':
                return $this->send_openai_request($prompt, $options, $api_key);
            case 'anthropic':
                return $this->send_anthropic_request($prompt, $options, $api_key);
            case 'google':
                return $this->send_google_request($prompt, $options, $api_key);
            default:
                return new WP_Error('unsupported_provider', 'Unsupported API provider: ' . $provider);
        }
    }
    
    /**
     * Send OpenAI request
     */
    private function send_openai_request($prompt, $options, $api_key) {
        $model = Options::get('ai_seo_ultra_model', 'gpt-3.5-turbo');
        
        $body = [
            'model' => $model,
            'messages' => [
                ['role' => 'user', 'content' => $prompt]
            ],
            'max_tokens' => $options['max_tokens'] ?? 1000,
            'temperature' => $options['temperature'] ?? 0.7,
            'top_p' => $options['top_p'] ?? 1,
            'frequency_penalty' => 0,
            'presence_penalty' => 0
        ];
        
        $response = wp_remote_post('https://api.openai.com/v1/chat/completions', [
            'headers' => [
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type' => 'application/json',
                'User-Agent' => 'AI-SEO-Ultra/' . AI_SEO_ULTRA_VERSION
            ],
            'body' => wp_json_encode($body),
            'timeout' => 60,
            'sslverify' => true
        ]);
        
        return $this->process_response($response, 'openai');
    }
    
    /**
     * Send Anthropic request
     */
    private function send_anthropic_request($prompt, $options, $api_key) {
        $model = Options::get('ai_seo_ultra_model', 'claude-3-sonnet-20240229');
        
        $body = [
            'model' => $model,
            'max_tokens' => $options['max_tokens'] ?? 1000,
            'messages' => [
                ['role' => 'user', 'content' => $prompt]
            ]
        ];
        
        $response = wp_remote_post('https://api.anthropic.com/v1/messages', [
            'headers' => [
                'x-api-key' => $api_key,
                'Content-Type' => 'application/json',
                'anthropic-version' => '2023-06-01',
                'User-Agent' => 'AI-SEO-Ultra/' . AI_SEO_ULTRA_VERSION
            ],
            'body' => wp_json_encode($body),
            'timeout' => 60,
            'sslverify' => true
        ]);
        
        return $this->process_response($response, 'anthropic');
    }
    
    /**
     * Send Google request
     */
    private function send_google_request($prompt, $options, $api_key) {
        $model = Options::get('ai_seo_ultra_model', 'gemini-pro');
        
        $body = [
            'contents' => [
                ['parts' => [['text' => $prompt]]]
            ],
            'generationConfig' => [
                'maxOutputTokens' => $options['max_tokens'] ?? 1000,
                'temperature' => $options['temperature'] ?? 0.7
            ]
        ];
        
        $response = wp_remote_post("https://generativelanguage.googleapis.com/v1beta/models/{$model}:generateContent?key={$api_key}", [
            'headers' => [
                'Content-Type' => 'application/json',
                'User-Agent' => 'AI-SEO-Ultra/' . AI_SEO_ULTRA_VERSION
            ],
            'body' => wp_json_encode($body),
            'timeout' => 60,
            'sslverify' => true
        ]);
        
        return $this->process_response($response, 'google');
    }
    
    /**
     * Process API response
     */
    private function process_response($response, $provider) {
        if (is_wp_error($response)) {
            return $response;
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        
        if ($status_code !== 200) {
            $error_message = "API request failed with status {$status_code}";
            
            // Try to extract error message from response
            $decoded_body = json_decode($body, true);
            if ($decoded_body && isset($decoded_body['error']['message'])) {
                $error_message .= ': ' . $decoded_body['error']['message'];
            }
            
            return new WP_Error('api_error', $error_message, ['status_code' => $status_code]);
        }
        
        $decoded_body = json_decode($body, true);
        if (!$decoded_body) {
            return new WP_Error('invalid_response', 'Invalid JSON response from API');
        }
        
        // Extract content based on provider
        return $this->extract_content($decoded_body, $provider);
    }
    
    /**
     * Extract content from API response
     */
    private function extract_content($response, $provider) {
        switch ($provider) {
            case 'openai':
                return $response['choices'][0]['message']['content'] ?? '';
            case 'anthropic':
                return $response['content'][0]['text'] ?? '';
            case 'google':
                return $response['candidates'][0]['content']['parts'][0]['text'] ?? '';
            default:
                return '';
        }
    }
    
    /**
     * Build SEO optimization prompt
     */
    private function build_seo_prompt($post_data, $options) {
        $target_audience = Options::get('ai_seo_ultra_target_audience', '');
        $tone = Options::get('ai_seo_ultra_desired_tone', 'professional');
        $custom_instructions = Options::get('ai_seo_ultra_custom_instructions', '');
        
        $prompt = "You are an expert SEO content optimizer. Analyze the following content and provide optimized SEO elements.\n\n";
        
        // Add post data
        $prompt .= "CONTENT TO OPTIMIZE:\n";
        $prompt .= "Title: " . ($post_data['title'] ?? '') . "\n";
        $prompt .= "Current Meta Description: " . ($post_data['meta_description'] ?? '') . "\n";
        $prompt .= "Primary Keyword: " . ($post_data['primary_keyword'] ?? '') . "\n";
        $prompt .= "Secondary Keywords: " . implode(', ', $post_data['secondary_keywords'] ?? []) . "\n";
        $prompt .= "Content Preview: " . substr($post_data['content'] ?? '', 0, 500) . "...\n\n";
        
        // Add optimization requirements
        $prompt .= "OPTIMIZATION REQUIREMENTS:\n";
        if (!empty($target_audience)) {
            $prompt .= "- Target Audience: {$target_audience}\n";
        }
        $prompt .= "- Tone: {$tone}\n";
        $prompt .= "- Include primary keyword naturally\n";
        $prompt .= "- Optimize for search engines and user engagement\n";
        $prompt .= "- Title should be 50-60 characters\n";
        $prompt .= "- Meta description should be 150-160 characters\n\n";
        
        if (!empty($custom_instructions)) {
            $prompt .= "ADDITIONAL INSTRUCTIONS:\n{$custom_instructions}\n\n";
        }
        
        $prompt .= "Please provide the response in the following JSON format:\n";
        $prompt .= "{\n";
        $prompt .= '  "title": "Optimized title here",';
        $prompt .= '  "meta_description": "Optimized meta description here",';
        $prompt .= '  "h1": "Optimized H1 heading here",';
        $prompt .= '  "focus_keywords": ["keyword1", "keyword2"],';
        $prompt .= '  "internal_links": [{"anchor": "anchor text", "url": "suggested-url"}]';
        $prompt .= "}\n";
        
        return $prompt;
    }
    
    /**
     * Parse SEO response
     */
    private function parse_seo_response($response) {
        // Try to extract JSON from response
        $json_start = strpos($response, '{');
        $json_end = strrpos($response, '}');
        
        if ($json_start !== false && $json_end !== false) {
            $json_string = substr($response, $json_start, $json_end - $json_start + 1);
            $parsed = json_decode($json_string, true);
            
            if ($parsed) {
                return $parsed;
            }
        }
        
        // Fallback: return raw response
        return ['raw_response' => $response];
    }
    
    /**
     * Check rate limit
     */
    private function check_rate_limit() {
        $current_time = time();
        $window_start = $current_time - $this->rate_limiter['window'];
        
        // Clean old requests
        $this->rate_limiter['requests'] = array_filter(
            $this->rate_limiter['requests'],
            function($timestamp) use ($window_start) {
                return $timestamp > $window_start;
            }
        );
        
        // Check if we're under the limit
        return count($this->rate_limiter['requests']) < $this->rate_limiter['max_requests'];
    }
    
    /**
     * Record API request
     */
    private function record_request() {
        $this->rate_limiter['requests'][] = time();
    }
    
    /**
     * Check if request should be retried
     */
    private function should_retry($error, $attempt) {
        if (!is_wp_error($error)) {
            return false;
        }
        
        $error_code = $error->get_error_code();
        $retryable_errors = ['api_error', 'timeout', 'connection_failed'];
        
        return in_array($error_code, $retryable_errors) && $attempt < 3;
    }
    
    /**
     * Generate cache key
     */
    private function generate_cache_key($prompt, $options) {
        $key_data = [
            'prompt_hash' => md5($prompt),
            'options' => $options,
            'provider' => Options::get('ai_seo_ultra_api_provider'),
            'model' => Options::get('ai_seo_ultra_model')
        ];
        
        return 'api_' . md5(wp_json_encode($key_data));
    }
    
    /**
     * Prevent cloning and unserialization
     */
    private function __clone() {}
    public function __wakeup() {
        throw new Exception('Cannot unserialize singleton');
    }
}
