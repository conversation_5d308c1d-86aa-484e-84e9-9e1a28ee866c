<?php
/**
 * Database Class
 * 
 * Handles database operations, table creation, and optimization
 * 
 * @package AiSeoOptimizerUltra
 * @since 9.0.0
 */

namespace AiSeoOptimizerUltra\Core;

use Exception;

/**
 * Database Class
 * 
 * Manages database operations with optimization
 */
final class Database {
    private static $instance = null;
    private $table_prefix;
    private $tables = [];
    
    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Private constructor
     */
    private function __construct() {
        global $wpdb;
        $this->table_prefix = $wpdb->prefix . 'ai_seo_ultra_';
        $this->define_tables();
    }
    
    /**
     * Define database tables
     */
    private function define_tables() {
        $this->tables = [
            'seo_data' => [
                'name' => $this->table_prefix . 'seo_data',
                'schema' => $this->get_seo_data_schema()
            ],
            'api_cache' => [
                'name' => $this->table_prefix . 'api_cache',
                'schema' => $this->get_api_cache_schema()
            ],
            'processing_queue' => [
                'name' => $this->table_prefix . 'processing_queue',
                'schema' => $this->get_processing_queue_schema()
            ],
            'logs' => [
                'name' => $this->table_prefix . 'logs',
                'schema' => $this->get_logs_schema()
            ]
        ];
    }
    
    /**
     * Create all plugin tables
     */
    public static function create_tables() {
        $instance = self::get_instance();
        return $instance->create_all_tables();
    }
    
    /**
     * Drop all plugin tables
     */
    public static function drop_tables() {
        $instance = self::get_instance();
        return $instance->drop_all_tables();
    }
    
    /**
     * Optimize database tables
     */
    public static function optimize_tables() {
        $instance = self::get_instance();
        return $instance->optimize_all_tables();
    }
    
    /**
     * Get table name
     */
    public static function get_table_name($table_key) {
        $instance = self::get_instance();
        return $instance->tables[$table_key]['name'] ?? null;
    }
    
    /**
     * Create all tables
     */
    private function create_all_tables() {
        global $wpdb;
        
        try {
            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            
            foreach ($this->tables as $table_key => $table_info) {
                $sql = $table_info['schema'];
                
                Logger::debug("Creating table: {$table_info['name']}");
                
                $result = dbDelta($sql);
                
                if ($wpdb->last_error) {
                    throw new Exception("Database error creating table {$table_info['name']}: " . $wpdb->last_error);
                }
                
                Logger::info("Table created/updated: {$table_info['name']}");
            }
            
            // Create indexes
            $this->create_indexes();
            
            Logger::info("All database tables created successfully");
            return true;
            
        } catch (Exception $e) {
            Logger::error("Error creating database tables: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Drop all tables
     */
    private function drop_all_tables() {
        global $wpdb;
        
        try {
            foreach ($this->tables as $table_key => $table_info) {
                $sql = "DROP TABLE IF EXISTS {$table_info['name']}";
                $wpdb->query($sql);
                
                if ($wpdb->last_error) {
                    throw new Exception("Error dropping table {$table_info['name']}: " . $wpdb->last_error);
                }
                
                Logger::info("Table dropped: {$table_info['name']}");
            }
            
            return true;
            
        } catch (Exception $e) {
            Logger::error("Error dropping database tables: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Optimize all tables
     */
    private function optimize_all_tables() {
        global $wpdb;
        
        try {
            foreach ($this->tables as $table_key => $table_info) {
                $sql = "OPTIMIZE TABLE {$table_info['name']}";
                $wpdb->query($sql);
                
                if ($wpdb->last_error) {
                    Logger::warning("Error optimizing table {$table_info['name']}: " . $wpdb->last_error);
                } else {
                    Logger::debug("Table optimized: {$table_info['name']}");
                }
            }
            
            Logger::info("Database optimization completed");
            return true;
            
        } catch (Exception $e) {
            Logger::error("Error optimizing database tables: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create database indexes
     */
    private function create_indexes() {
        global $wpdb;
        
        $indexes = [
            // SEO Data indexes
            "CREATE INDEX idx_post_id ON {$this->tables['seo_data']['name']} (post_id)",
            "CREATE INDEX idx_updated_at ON {$this->tables['seo_data']['name']} (updated_at)",
            
            // API Cache indexes
            "CREATE INDEX idx_cache_key ON {$this->tables['api_cache']['name']} (cache_key)",
            "CREATE INDEX idx_expires_at ON {$this->tables['api_cache']['name']} (expires_at)",
            
            // Processing Queue indexes
            "CREATE INDEX idx_status ON {$this->tables['processing_queue']['name']} (status)",
            "CREATE INDEX idx_priority ON {$this->tables['processing_queue']['name']} (priority)",
            "CREATE INDEX idx_scheduled_at ON {$this->tables['processing_queue']['name']} (scheduled_at)",
            
            // Logs indexes
            "CREATE INDEX idx_level ON {$this->tables['logs']['name']} (level)",
            "CREATE INDEX idx_created_at ON {$this->tables['logs']['name']} (created_at)"
        ];
        
        foreach ($indexes as $index_sql) {
            $wpdb->query($index_sql);
            // Ignore errors for existing indexes
        }
    }
    
    /**
     * SEO Data table schema
     */
    private function get_seo_data_schema() {
        $charset_collate = $this->get_charset_collate();
        
        return "CREATE TABLE {$this->tables['seo_data']['name']} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            post_id bigint(20) unsigned NOT NULL,
            title_score int(3) DEFAULT 0,
            description_score int(3) DEFAULT 0,
            overall_score int(3) DEFAULT 0,
            suggested_title text,
            suggested_description text,
            suggested_h1 text,
            keywords text,
            internal_links text,
            schema_data longtext,
            last_processed datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY post_id (post_id)
        ) $charset_collate;";
    }
    
    /**
     * API Cache table schema
     */
    private function get_api_cache_schema() {
        $charset_collate = $this->get_charset_collate();
        
        return "CREATE TABLE {$this->tables['api_cache']['name']} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            cache_key varchar(255) NOT NULL,
            cache_value longtext NOT NULL,
            expires_at datetime NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY cache_key (cache_key)
        ) $charset_collate;";
    }
    
    /**
     * Processing Queue table schema
     */
    private function get_processing_queue_schema() {
        $charset_collate = $this->get_charset_collate();
        
        return "CREATE TABLE {$this->tables['processing_queue']['name']} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            post_id bigint(20) unsigned NOT NULL,
            action varchar(100) NOT NULL,
            priority int(3) DEFAULT 5,
            status varchar(20) DEFAULT 'pending',
            attempts int(3) DEFAULT 0,
            max_attempts int(3) DEFAULT 3,
            scheduled_at datetime DEFAULT CURRENT_TIMESTAMP,
            started_at datetime NULL,
            completed_at datetime NULL,
            error_message text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY post_id (post_id)
        ) $charset_collate;";
    }
    
    /**
     * Logs table schema
     */
    private function get_logs_schema() {
        $charset_collate = $this->get_charset_collate();
        
        return "CREATE TABLE {$this->tables['logs']['name']} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            level varchar(20) NOT NULL,
            message text NOT NULL,
            context longtext,
            user_id bigint(20) unsigned DEFAULT NULL,
            ip_address varchar(45) DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";
    }
    
    /**
     * Get charset collate
     */
    private function get_charset_collate() {
        global $wpdb;
        return $wpdb->get_charset_collate();
    }
    
    /**
     * Insert SEO data
     */
    public static function insert_seo_data($post_id, $data) {
        global $wpdb;
        $instance = self::get_instance();
        
        try {
            $table_name = $instance->tables['seo_data']['name'];
            
            $insert_data = [
                'post_id' => absint($post_id),
                'title_score' => intval($data['title_score'] ?? 0),
                'description_score' => intval($data['description_score'] ?? 0),
                'overall_score' => intval($data['overall_score'] ?? 0),
                'suggested_title' => sanitize_text_field($data['suggested_title'] ?? ''),
                'suggested_description' => sanitize_textarea_field($data['suggested_description'] ?? ''),
                'suggested_h1' => sanitize_text_field($data['suggested_h1'] ?? ''),
                'keywords' => sanitize_text_field($data['keywords'] ?? ''),
                'internal_links' => wp_json_encode($data['internal_links'] ?? []),
                'schema_data' => wp_json_encode($data['schema_data'] ?? [])
            ];
            
            $result = $wpdb->replace($table_name, $insert_data);
            
            if ($result === false) {
                throw new Exception("Database error: " . $wpdb->last_error);
            }
            
            return $result;
            
        } catch (Exception $e) {
            Logger::error("Error inserting SEO data for post {$post_id}: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get SEO data
     */
    public static function get_seo_data($post_id) {
        global $wpdb;
        $instance = self::get_instance();
        
        try {
            $table_name = $instance->tables['seo_data']['name'];
            
            $result = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$table_name} WHERE post_id = %d",
                absint($post_id)
            ), ARRAY_A);
            
            if ($result) {
                // Decode JSON fields
                $result['internal_links'] = json_decode($result['internal_links'], true) ?: [];
                $result['schema_data'] = json_decode($result['schema_data'], true) ?: [];
            }
            
            return $result;
            
        } catch (Exception $e) {
            Logger::error("Error getting SEO data for post {$post_id}: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Clean up old cache entries
     */
    public static function cleanup_expired_cache() {
        global $wpdb;
        $instance = self::get_instance();
        
        try {
            $table_name = $instance->tables['api_cache']['name'];
            
            $deleted = $wpdb->query(
                "DELETE FROM {$table_name} WHERE expires_at < NOW()"
            );
            
            if ($deleted > 0) {
                Logger::info("Cleaned up {$deleted} expired cache entries");
            }
            
            return $deleted;
            
        } catch (Exception $e) {
            Logger::error("Error cleaning up expired cache: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Clean up old log entries
     */
    public static function cleanup_old_logs($days = 30) {
        global $wpdb;
        $instance = self::get_instance();
        
        try {
            $table_name = $instance->tables['logs']['name'];
            
            $deleted = $wpdb->query($wpdb->prepare(
                "DELETE FROM {$table_name} WHERE created_at < DATE_SUB(NOW(), INTERVAL %d DAY)",
                intval($days)
            ));
            
            if ($deleted > 0) {
                Logger::info("Cleaned up {$deleted} old log entries");
            }
            
            return $deleted;
            
        } catch (Exception $e) {
            Logger::error("Error cleaning up old logs: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get database statistics
     */
    public static function get_statistics() {
        global $wpdb;
        $instance = self::get_instance();
        
        $stats = [];
        
        try {
            foreach ($instance->tables as $table_key => $table_info) {
                $count = $wpdb->get_var("SELECT COUNT(*) FROM {$table_info['name']}");
                $stats[$table_key] = [
                    'name' => $table_info['name'],
                    'count' => intval($count)
                ];
            }
            
            return $stats;
            
        } catch (Exception $e) {
            Logger::error("Error getting database statistics: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Prevent cloning and unserialization
     */
    private function __clone() {}
    public function __wakeup() {
        throw new Exception('Cannot unserialize singleton');
    }
}
