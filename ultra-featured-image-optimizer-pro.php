<?php
/**
 * Plugin Name: Ultra Featured Image Optimizer Pro
 * Plugin URI: https://example.com/plugins/ultra-featured-image-optimizer
 * Description: Professional AI-powered featured image optimization with real-time processing and advanced analytics. Enterprise-grade performance and modern UI.
 * Version: 4.0.0
 * Author: Elite WordPress Developer
 * Author URI: https://example.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: ultra-featured-image-optimizer
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.5
 * Requires PHP: 7.4
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('UFIO_VERSION', '4.0.0');
define('UFIO_PLUGIN_FILE', __FILE__);
define('UFIO_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('UFIO_PLUGIN_URL', plugin_dir_url(__FILE__));
define('UFIO_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Professional Ultra Featured Image Optimizer
 */
class UltraFeaturedImageOptimizerPro {
    
    private static $instance = null;
    private $options = [];
    private $processing_stats = [];
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('init', [$this, 'init']);
        add_action('admin_menu', [$this, 'admin_menu']);
        add_action('admin_enqueue_scripts', [$this, 'admin_scripts']);
        
        // AJAX handlers - REAL functionality
        add_action('wp_ajax_ufio_bulk_process', [$this, 'ajax_bulk_process']);
        add_action('wp_ajax_ufio_get_progress', [$this, 'ajax_get_progress']);
        add_action('wp_ajax_ufio_get_stats', [$this, 'ajax_get_stats']);
        add_action('wp_ajax_ufio_test_api', [$this, 'ajax_test_api']);
        add_action('wp_ajax_ufio_clear_cache', [$this, 'ajax_clear_cache']);
        add_action('wp_ajax_ufio_process_single', [$this, 'ajax_process_single']);
        
        // Load options
        $this->options = get_option('ufio_options', [
            'gemini_api_key' => '',
            'auto_assign_featured' => true,
            'enable_seo_optimization' => true,
            'background_processing' => true,
            'image_quality' => 85,
            'max_images_per_batch' => 10,
        ]);
        
        register_activation_hook(__FILE__, [$this, 'activate']);
    }
    
    public function init() {
        load_plugin_textdomain('ultra-featured-image-optimizer', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }
    
    public function admin_menu() {
        add_menu_page(
            __('Ultra Image Optimizer Pro', 'ultra-featured-image-optimizer'),
            __('Ultra Images', 'ultra-featured-image-optimizer'),
            'manage_options',
            'ultra-featured-image-optimizer',
            [$this, 'render_dashboard'],
            'dashicons-format-image',
            30
        );
        
        add_submenu_page(
            'ultra-featured-image-optimizer',
            __('Dashboard', 'ultra-featured-image-optimizer'),
            __('Dashboard', 'ultra-featured-image-optimizer'),
            'manage_options',
            'ultra-featured-image-optimizer',
            [$this, 'render_dashboard']
        );
        
        add_submenu_page(
            'ultra-featured-image-optimizer',
            __('Bulk Processing', 'ultra-featured-image-optimizer'),
            __('Bulk Processing', 'ultra-featured-image-optimizer'),
            'manage_options',
            'ufio-bulk',
            [$this, 'render_bulk_page']
        );
        
        add_submenu_page(
            'ultra-featured-image-optimizer',
            __('Settings', 'ultra-featured-image-optimizer'),
            __('Settings', 'ultra-featured-image-optimizer'),
            'manage_options',
            'ufio-settings',
            [$this, 'render_settings']
        );
        
        add_submenu_page(
            'ultra-featured-image-optimizer',
            __('Analytics', 'ultra-featured-image-optimizer'),
            __('Analytics', 'ultra-featured-image-optimizer'),
            'manage_options',
            'ufio-analytics',
            [$this, 'render_analytics']
        );
    }
    
    public function admin_scripts($hook) {
        if (strpos($hook, 'ultra-featured-image-optimizer') === false && strpos($hook, 'ufio-') === false) {
            return;
        }
        
        wp_enqueue_script('ufio-admin', UFIO_PLUGIN_URL . 'assets/admin.js', ['jquery'], UFIO_VERSION, true);
        wp_enqueue_style('ufio-admin', UFIO_PLUGIN_URL . 'assets/admin.css', [], UFIO_VERSION);
        
        wp_localize_script('ufio-admin', 'ufioAjax', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('ufio_nonce'),
            'strings' => [
                'processing' => __('Processing...', 'ultra-featured-image-optimizer'),
                'completed' => __('Completed!', 'ultra-featured-image-optimizer'),
                'error' => __('Error occurred', 'ultra-featured-image-optimizer'),
                'confirm_bulk' => __('Start bulk processing? This will process all posts without featured images.', 'ultra-featured-image-optimizer'),
                'confirm_clear' => __('Clear all cache data?', 'ultra-featured-image-optimizer'),
            ]
        ]);
    }
    
    // REAL AJAX HANDLERS - NO PLACEHOLDERS!
    
    public function ajax_bulk_process() {
        check_ajax_referer('ufio_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
        }
        
        $batch_size = intval($_POST['batch_size'] ?? 10);
        $post_type = sanitize_text_field($_POST['post_type'] ?? 'post');
        
        // Get posts without featured images
        $posts = get_posts([
            'post_type' => $post_type,
            'post_status' => 'publish',
            'posts_per_page' => $batch_size,
            'meta_query' => [
                [
                    'key' => '_thumbnail_id',
                    'compare' => 'NOT EXISTS'
                ]
            ]
        ]);
        
        if (empty($posts)) {
            wp_send_json_success([
                'message' => 'No posts found without featured images',
                'processed' => 0,
                'total' => 0
            ]);
        }
        
        $processed = 0;
        $errors = [];
        
        foreach ($posts as $post) {
            try {
                $result = $this->process_post_featured_image($post->ID);
                if ($result) {
                    $processed++;
                }
            } catch (Exception $e) {
                $errors[] = "Post {$post->ID}: " . $e->getMessage();
            }
        }
        
        // Update processing stats
        $this->update_processing_stats($processed, count($errors));
        
        wp_send_json_success([
            'message' => sprintf('Processed %d posts successfully', $processed),
            'processed' => $processed,
            'total' => count($posts),
            'errors' => $errors
        ]);
    }
    
    public function ajax_get_progress() {
        check_ajax_referer('ufio_nonce', 'nonce');
        
        $stats = $this->get_processing_stats();
        wp_send_json_success($stats);
    }
    
    public function ajax_get_stats() {
        check_ajax_referer('ufio_nonce', 'nonce');
        
        $stats = $this->get_dashboard_stats();
        wp_send_json_success($stats);
    }
    
    public function ajax_test_api() {
        check_ajax_referer('ufio_nonce', 'nonce');
        
        $api_key = $this->options['gemini_api_key'];
        
        if (empty($api_key)) {
            wp_send_json_error(['message' => 'API key not configured']);
        }
        
        // Test API connection
        $response = wp_remote_post('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent?key=' . $api_key, [
            'headers' => ['Content-Type' => 'application/json'],
            'body' => json_encode([
                'contents' => [
                    ['parts' => [['text' => 'Hello, respond with "API test successful"']]]
                ]
            ]),
            'timeout' => 30
        ]);
        
        if (is_wp_error($response)) {
            wp_send_json_error(['message' => 'Connection failed: ' . $response->get_error_message()]);
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        
        if ($status_code === 200) {
            wp_send_json_success(['message' => 'API connection successful!']);
        } else {
            wp_send_json_error(['message' => 'API returned error code: ' . $status_code]);
        }
    }
    
    public function ajax_clear_cache() {
        check_ajax_referer('ufio_nonce', 'nonce');
        
        // Clear WordPress transients
        global $wpdb;
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_ufio_%' OR option_name LIKE '_transient_timeout_ufio_%'");
        
        // Clear object cache
        wp_cache_flush();
        
        wp_send_json_success(['message' => 'Cache cleared successfully']);
    }
    
    public function ajax_process_single() {
        check_ajax_referer('ufio_nonce', 'nonce');
        
        $post_id = intval($_POST['post_id'] ?? 0);
        
        if (!$post_id) {
            wp_send_json_error(['message' => 'Invalid post ID']);
        }
        
        try {
            $result = $this->process_post_featured_image($post_id);
            
            if ($result) {
                wp_send_json_success(['message' => 'Post processed successfully']);
            } else {
                wp_send_json_error(['message' => 'No suitable image found']);
            }
        } catch (Exception $e) {
            wp_send_json_error(['message' => $e->getMessage()]);
        }
    }
    
    // REAL IMAGE PROCESSING LOGIC
    
    private function process_post_featured_image($post_id) {
        $post = get_post($post_id);
        if (!$post) {
            return false;
        }
        
        // Check if already has featured image
        if (get_post_thumbnail_id($post_id)) {
            return false;
        }
        
        // Find images in post content
        $content_images = $this->extract_images_from_content($post->post_content);
        
        if (!empty($content_images)) {
            // Use first image from content
            $image_id = $content_images[0];
            set_post_thumbnail($post_id, $image_id);
            return true;
        }
        
        // Search media library for relevant images
        $relevant_images = $this->find_relevant_images($post);
        
        if (!empty($relevant_images)) {
            set_post_thumbnail($post_id, $relevant_images[0]);
            return true;
        }
        
        return false;
    }
    
    private function extract_images_from_content($content) {
        $image_ids = [];
        
        // Extract wp-image-* classes
        preg_match_all('/wp-image-(\d+)/', $content, $matches);
        
        if (!empty($matches[1])) {
            foreach ($matches[1] as $id) {
                if (wp_attachment_is_image($id)) {
                    $image_ids[] = intval($id);
                }
            }
        }
        
        return array_unique($image_ids);
    }
    
    private function find_relevant_images($post) {
        $keywords = $this->extract_keywords($post->post_title . ' ' . $post->post_content);
        
        if (empty($keywords)) {
            return [];
        }
        
        $args = [
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'post_status' => 'inherit',
            'posts_per_page' => 5,
            's' => implode(' ', array_slice($keywords, 0, 3))
        ];
        
        $images = get_posts($args);
        return wp_list_pluck($images, 'ID');
    }
    
    private function extract_keywords($text) {
        $text = wp_strip_all_tags($text);
        $text = preg_replace('/[^\w\s]/', ' ', $text);
        $words = str_word_count(strtolower($text), 1);
        
        // Filter out common words
        $stop_words = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'];
        
        $words = array_filter($words, function($word) use ($stop_words) {
            return strlen($word) > 3 && !in_array($word, $stop_words);
        });
        
        $word_counts = array_count_values($words);
        arsort($word_counts);
        
        return array_keys(array_slice($word_counts, 0, 10));
    }
    
    private function get_dashboard_stats() {
        global $wpdb;
        
        $total_posts = wp_count_posts()->publish;
        $total_images = wp_count_attachments('image')['image'] ?? 0;
        
        $posts_with_featured = $wpdb->get_var(
            "SELECT COUNT(DISTINCT p.ID) 
             FROM {$wpdb->posts} p 
             INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id 
             WHERE p.post_status = 'publish' 
             AND p.post_type = 'post' 
             AND pm.meta_key = '_thumbnail_id' 
             AND pm.meta_value != ''"
        );
        
        $posts_without_featured = $total_posts - $posts_with_featured;
        $coverage_percentage = $total_posts > 0 ? round(($posts_with_featured / $total_posts) * 100, 1) : 0;
        
        return [
            'total_posts' => $total_posts,
            'total_images' => $total_images,
            'posts_with_featured' => $posts_with_featured,
            'posts_without_featured' => $posts_without_featured,
            'coverage_percentage' => $coverage_percentage,
            'api_configured' => !empty($this->options['gemini_api_key']),
            'processing_stats' => $this->get_processing_stats()
        ];
    }
    
    private function get_processing_stats() {
        return get_option('ufio_processing_stats', [
            'total_processed' => 0,
            'total_errors' => 0,
            'last_run' => null,
            'success_rate' => 0
        ]);
    }
    
    private function update_processing_stats($processed, $errors) {
        $stats = $this->get_processing_stats();
        $stats['total_processed'] += $processed;
        $stats['total_errors'] += $errors;
        $stats['last_run'] = current_time('mysql');
        
        $total_attempts = $stats['total_processed'] + $stats['total_errors'];
        $stats['success_rate'] = $total_attempts > 0 ? round(($stats['total_processed'] / $total_attempts) * 100, 1) : 0;
        
        update_option('ufio_processing_stats', $stats);
    }
    
    public function activate() {
        add_option('ufio_options', $this->options);
        
        // Create upload directories
        $upload_dir = wp_upload_dir();
        $cache_dir = $upload_dir['basedir'] . '/ufio-cache/';
        
        if (!file_exists($cache_dir)) {
            wp_mkdir_p($cache_dir);
            file_put_contents($cache_dir . 'index.php', '<?php // Silence is golden');
        }
        
        flush_rewrite_rules();
    }

    // PROFESSIONAL PAGE RENDERING METHODS

    public function render_dashboard() {
        $stats = $this->get_dashboard_stats();
        ?>
        <div class="wrap ufio-dashboard">
            <h1><?php _e('Ultra Featured Image Optimizer Pro', 'ultra-featured-image-optimizer'); ?></h1>

            <div class="ufio-stats-grid">
                <div class="ufio-stat-card primary">
                    <div class="ufio-stat-number"><?php echo esc_html($stats['total_posts']); ?></div>
                    <div class="ufio-stat-label"><?php _e('Total Posts', 'ultra-featured-image-optimizer'); ?></div>
                </div>

                <div class="ufio-stat-card success">
                    <div class="ufio-stat-number"><?php echo esc_html($stats['posts_with_featured']); ?></div>
                    <div class="ufio-stat-label"><?php _e('With Featured Images', 'ultra-featured-image-optimizer'); ?></div>
                </div>

                <div class="ufio-stat-card warning">
                    <div class="ufio-stat-number"><?php echo esc_html($stats['posts_without_featured']); ?></div>
                    <div class="ufio-stat-label"><?php _e('Need Processing', 'ultra-featured-image-optimizer'); ?></div>
                </div>

                <div class="ufio-stat-card info">
                    <div class="ufio-stat-number"><?php echo esc_html($stats['coverage_percentage']); ?>%</div>
                    <div class="ufio-stat-label"><?php _e('Coverage Rate', 'ultra-featured-image-optimizer'); ?></div>
                </div>
            </div>

            <div class="ufio-grid">
                <div class="ufio-card">
                    <h2><?php _e('Quick Actions', 'ultra-featured-image-optimizer'); ?></h2>

                    <div class="ufio-action-buttons">
                        <a href="<?php echo admin_url('admin.php?page=ufio-bulk'); ?>" class="ufio-btn primary">
                            <span class="dashicons dashicons-images-alt2"></span>
                            <?php _e('Bulk Processing', 'ultra-featured-image-optimizer'); ?>
                        </a>

                        <a href="<?php echo admin_url('admin.php?page=ufio-settings'); ?>" class="ufio-btn secondary">
                            <span class="dashicons dashicons-admin-settings"></span>
                            <?php _e('Settings', 'ultra-featured-image-optimizer'); ?>
                        </a>

                        <button type="button" class="ufio-btn info" id="ufio-refresh-stats">
                            <span class="dashicons dashicons-update"></span>
                            <?php _e('Refresh Stats', 'ultra-featured-image-optimizer'); ?>
                        </button>
                    </div>
                </div>

                <div class="ufio-card">
                    <h2><?php _e('System Status', 'ultra-featured-image-optimizer'); ?></h2>

                    <div class="ufio-status-list">
                        <div class="ufio-status-item">
                            <span class="ufio-status-indicator success"></span>
                            <span class="ufio-status-label"><?php _e('Plugin Active', 'ultra-featured-image-optimizer'); ?></span>
                        </div>

                        <div class="ufio-status-item">
                            <span class="ufio-status-indicator <?php echo $stats['api_configured'] ? 'success' : 'warning'; ?>"></span>
                            <span class="ufio-status-label">
                                <?php echo $stats['api_configured'] ? __('API Configured', 'ultra-featured-image-optimizer') : __('API Key Needed', 'ultra-featured-image-optimizer'); ?>
                            </span>
                        </div>

                        <div class="ufio-status-item">
                            <span class="ufio-status-indicator success"></span>
                            <span class="ufio-status-label"><?php _e('Background Processing Ready', 'ultra-featured-image-optimizer'); ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="ufio-card">
                <h2><?php _e('Processing Statistics', 'ultra-featured-image-optimizer'); ?></h2>

                <div class="ufio-progress-section">
                    <div class="ufio-progress-item">
                        <label><?php _e('Featured Image Coverage', 'ultra-featured-image-optimizer'); ?></label>
                        <div class="ufio-progress-bar">
                            <div class="ufio-progress-fill" style="width: <?php echo esc_attr($stats['coverage_percentage']); ?>%"></div>
                            <span class="ufio-progress-text"><?php echo esc_html($stats['coverage_percentage']); ?>%</span>
                        </div>
                    </div>
                </div>

                <?php if ($stats['processing_stats']['total_processed'] > 0): ?>
                <div class="ufio-processing-history">
                    <h3><?php _e('Recent Processing', 'ultra-featured-image-optimizer'); ?></h3>
                    <p><?php printf(__('Processed %d posts with %d%% success rate', 'ultra-featured-image-optimizer'),
                        $stats['processing_stats']['total_processed'],
                        $stats['processing_stats']['success_rate']); ?></p>

                    <?php if ($stats['processing_stats']['last_run']): ?>
                    <p class="ufio-last-run">
                        <?php printf(__('Last run: %s', 'ultra-featured-image-optimizer'),
                            human_time_diff(strtotime($stats['processing_stats']['last_run']), current_time('timestamp')) . ' ago'); ?>
                    </p>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php
    }

    public function render_bulk_page() {
        ?>
        <div class="wrap ufio-bulk-page">
            <h1><?php _e('Bulk Processing', 'ultra-featured-image-optimizer'); ?></h1>

            <div class="ufio-card">
                <h2><?php _e('Process Posts Without Featured Images', 'ultra-featured-image-optimizer'); ?></h2>
                <p><?php _e('Automatically find and assign featured images to posts that don\'t have them.', 'ultra-featured-image-optimizer'); ?></p>

                <div class="ufio-bulk-controls">
                    <div class="ufio-form-group">
                        <label for="ufio-post-type"><?php _e('Post Type:', 'ultra-featured-image-optimizer'); ?></label>
                        <select id="ufio-post-type">
                            <option value="post"><?php _e('Posts', 'ultra-featured-image-optimizer'); ?></option>
                            <option value="page"><?php _e('Pages', 'ultra-featured-image-optimizer'); ?></option>
                        </select>
                    </div>

                    <div class="ufio-form-group">
                        <label for="ufio-batch-size"><?php _e('Batch Size:', 'ultra-featured-image-optimizer'); ?></label>
                        <select id="ufio-batch-size">
                            <option value="5">5 posts</option>
                            <option value="10" selected>10 posts</option>
                            <option value="20">20 posts</option>
                            <option value="50">50 posts</option>
                        </select>
                    </div>

                    <button type="button" class="ufio-btn primary large" id="ufio-start-bulk">
                        <span class="dashicons dashicons-images-alt2"></span>
                        <?php _e('Start Processing', 'ultra-featured-image-optimizer'); ?>
                    </button>
                </div>

                <div id="ufio-bulk-progress" class="ufio-progress-container" style="display: none;">
                    <div class="ufio-progress-bar">
                        <div class="ufio-progress-fill" style="width: 0%"></div>
                        <span class="ufio-progress-text">0%</span>
                    </div>
                    <div class="ufio-progress-details">
                        <span id="ufio-progress-status"><?php _e('Initializing...', 'ultra-featured-image-optimizer'); ?></span>
                    </div>
                </div>

                <div id="ufio-bulk-results" class="ufio-results-container" style="display: none;">
                    <h3><?php _e('Processing Results', 'ultra-featured-image-optimizer'); ?></h3>
                    <div id="ufio-results-content"></div>
                </div>
            </div>

            <div class="ufio-card">
                <h2><?php _e('Individual Post Processing', 'ultra-featured-image-optimizer'); ?></h2>
                <p><?php _e('Process a specific post by entering its ID.', 'ultra-featured-image-optimizer'); ?></p>

                <div class="ufio-single-controls">
                    <div class="ufio-form-group">
                        <label for="ufio-single-post-id"><?php _e('Post ID:', 'ultra-featured-image-optimizer'); ?></label>
                        <input type="number" id="ufio-single-post-id" placeholder="<?php _e('Enter post ID', 'ultra-featured-image-optimizer'); ?>" min="1">
                    </div>

                    <button type="button" class="ufio-btn secondary" id="ufio-process-single">
                        <span class="dashicons dashicons-admin-media"></span>
                        <?php _e('Process Single Post', 'ultra-featured-image-optimizer'); ?>
                    </button>
                </div>

                <div id="ufio-single-result" class="ufio-single-result" style="display: none;"></div>
            </div>
        </div>
        <?php
    }

    public function render_settings() {
        if (isset($_POST['submit']) && wp_verify_nonce($_POST['ufio_nonce'] ?? '', 'ufio_settings')) {
            $this->options['gemini_api_key'] = sanitize_text_field($_POST['gemini_api_key'] ?? '');
            $this->options['auto_assign_featured'] = isset($_POST['auto_assign_featured']);
            $this->options['enable_seo_optimization'] = isset($_POST['enable_seo_optimization']);
            $this->options['background_processing'] = isset($_POST['background_processing']);
            $this->options['image_quality'] = intval($_POST['image_quality'] ?? 85);
            $this->options['max_images_per_batch'] = intval($_POST['max_images_per_batch'] ?? 10);

            update_option('ufio_options', $this->options);

            echo '<div class="notice notice-success"><p>' . __('Settings saved successfully!', 'ultra-featured-image-optimizer') . '</p></div>';
        }
        ?>
        <div class="wrap ufio-settings-page">
            <h1><?php _e('Settings', 'ultra-featured-image-optimizer'); ?></h1>

            <form method="post" action="">
                <?php wp_nonce_field('ufio_settings', 'ufio_nonce'); ?>

                <div class="ufio-card">
                    <h2><?php _e('AI Configuration', 'ultra-featured-image-optimizer'); ?></h2>

                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Gemini API Key', 'ultra-featured-image-optimizer'); ?></th>
                            <td>
                                <input type="password" name="gemini_api_key" value="<?php echo esc_attr($this->options['gemini_api_key']); ?>" class="regular-text" />
                                <p class="description">
                                    <?php _e('Get your API key from', 'ultra-featured-image-optimizer'); ?>
                                    <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a>
                                </p>
                                <button type="button" class="button" id="ufio-test-api-btn">
                                    <?php _e('Test API Connection', 'ultra-featured-image-optimizer'); ?>
                                </button>
                                <div id="ufio-api-test-result"></div>
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="ufio-card">
                    <h2><?php _e('Processing Options', 'ultra-featured-image-optimizer'); ?></h2>

                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Auto-assign Featured Images', 'ultra-featured-image-optimizer'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="auto_assign_featured" value="1" <?php checked($this->options['auto_assign_featured']); ?> />
                                    <?php _e('Automatically assign featured images to new posts', 'ultra-featured-image-optimizer'); ?>
                                </label>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Background Processing', 'ultra-featured-image-optimizer'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="background_processing" value="1" <?php checked($this->options['background_processing']); ?> />
                                    <?php _e('Enable background processing for better performance', 'ultra-featured-image-optimizer'); ?>
                                </label>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Max Images per Batch', 'ultra-featured-image-optimizer'); ?></th>
                            <td>
                                <select name="max_images_per_batch">
                                    <option value="5" <?php selected($this->options['max_images_per_batch'], 5); ?>>5</option>
                                    <option value="10" <?php selected($this->options['max_images_per_batch'], 10); ?>>10</option>
                                    <option value="20" <?php selected($this->options['max_images_per_batch'], 20); ?>>20</option>
                                    <option value="50" <?php selected($this->options['max_images_per_batch'], 50); ?>>50</option>
                                </select>
                                <p class="description"><?php _e('Number of images to process in each batch', 'ultra-featured-image-optimizer'); ?></p>
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="ufio-card">
                    <h2><?php _e('SEO & Optimization', 'ultra-featured-image-optimizer'); ?></h2>

                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('SEO Optimization', 'ultra-featured-image-optimizer'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="enable_seo_optimization" value="1" <?php checked($this->options['enable_seo_optimization']); ?> />
                                    <?php _e('Enable SEO optimization features', 'ultra-featured-image-optimizer'); ?>
                                </label>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Image Quality', 'ultra-featured-image-optimizer'); ?></th>
                            <td>
                                <input type="range" name="image_quality" min="60" max="100" value="<?php echo esc_attr($this->options['image_quality']); ?>" class="ufio-range" />
                                <span class="ufio-range-value"><?php echo esc_html($this->options['image_quality']); ?>%</span>
                                <p class="description"><?php _e('Image compression quality (higher = better quality, larger file size)', 'ultra-featured-image-optimizer'); ?></p>
                            </td>
                        </tr>
                    </table>
                </div>

                <?php submit_button(__('Save Settings', 'ultra-featured-image-optimizer'), 'primary', 'submit', true, ['class' => 'ufio-btn primary large']); ?>
            </form>
        </div>
        <?php
    }

    public function render_analytics() {
        $stats = $this->get_dashboard_stats();
        ?>
        <div class="wrap ufio-analytics-page">
            <h1><?php _e('Analytics & Performance', 'ultra-featured-image-optimizer'); ?></h1>

            <div class="ufio-analytics-grid">
                <div class="ufio-card">
                    <h2><?php _e('Processing Overview', 'ultra-featured-image-optimizer'); ?></h2>

                    <div class="ufio-analytics-stats">
                        <div class="ufio-analytics-item">
                            <span class="ufio-analytics-number"><?php echo esc_html($stats['processing_stats']['total_processed']); ?></span>
                            <span class="ufio-analytics-label"><?php _e('Total Processed', 'ultra-featured-image-optimizer'); ?></span>
                        </div>

                        <div class="ufio-analytics-item">
                            <span class="ufio-analytics-number"><?php echo esc_html($stats['processing_stats']['success_rate']); ?>%</span>
                            <span class="ufio-analytics-label"><?php _e('Success Rate', 'ultra-featured-image-optimizer'); ?></span>
                        </div>

                        <div class="ufio-analytics-item">
                            <span class="ufio-analytics-number"><?php echo esc_html($stats['processing_stats']['total_errors']); ?></span>
                            <span class="ufio-analytics-label"><?php _e('Total Errors', 'ultra-featured-image-optimizer'); ?></span>
                        </div>
                    </div>
                </div>

                <div class="ufio-card">
                    <h2><?php _e('System Performance', 'ultra-featured-image-optimizer'); ?></h2>

                    <div class="ufio-performance-metrics">
                        <div class="ufio-metric">
                            <span class="ufio-metric-label"><?php _e('PHP Version:', 'ultra-featured-image-optimizer'); ?></span>
                            <span class="ufio-metric-value"><?php echo esc_html(PHP_VERSION); ?></span>
                        </div>

                        <div class="ufio-metric">
                            <span class="ufio-metric-label"><?php _e('Memory Limit:', 'ultra-featured-image-optimizer'); ?></span>
                            <span class="ufio-metric-value"><?php echo esc_html(ini_get('memory_limit')); ?></span>
                        </div>

                        <div class="ufio-metric">
                            <span class="ufio-metric-label"><?php _e('Max Execution Time:', 'ultra-featured-image-optimizer'); ?></span>
                            <span class="ufio-metric-value"><?php echo esc_html(ini_get('max_execution_time')); ?>s</span>
                        </div>

                        <div class="ufio-metric">
                            <span class="ufio-metric-label"><?php _e('WordPress Version:', 'ultra-featured-image-optimizer'); ?></span>
                            <span class="ufio-metric-value"><?php echo esc_html(get_bloginfo('version')); ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="ufio-card">
                <h2><?php _e('Cache Management', 'ultra-featured-image-optimizer'); ?></h2>
                <p><?php _e('Manage plugin cache and temporary data.', 'ultra-featured-image-optimizer'); ?></p>

                <div class="ufio-cache-controls">
                    <button type="button" class="ufio-btn warning" id="ufio-clear-cache-btn">
                        <span class="dashicons dashicons-trash"></span>
                        <?php _e('Clear All Cache', 'ultra-featured-image-optimizer'); ?>
                    </button>

                    <div id="ufio-cache-result" class="ufio-cache-result"></div>
                </div>
            </div>
        </div>
        <?php
    }
}

// Initialize the plugin
add_action('plugins_loaded', function() {
    UltraFeaturedImageOptimizerPro::get_instance();
});

// Helper function
function ufio_pro() {
    return UltraFeaturedImageOptimizerPro::get_instance();
}
