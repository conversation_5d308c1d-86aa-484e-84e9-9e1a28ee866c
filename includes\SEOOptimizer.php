<?php
/**
 * SEO optimization for images and content
 * 
 * @package UltraFeaturedImageOptimizer
 * @subpackage SEOOptimizer
 */

namespace UltraFeaturedImageOptimizer;

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Advanced SEO optimization for images and content
 */
class SEOOptimizer {
    
    /**
     * Dependencies
     */
    private $cache;
    private $logger;
    private $database;
    
    /**
     * SEO configuration
     * @var array
     */
    private $config;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->cache = new Cache();
        $this->logger = new Logger();
        $this->database = new Database();
        
        $this->config = [
            'enable_structured_data' => ufio()->get_option('enable_structured_data', true),
            'enable_meta_tags' => ufio()->get_option('enable_meta_tags', true),
            'enable_lazy_loading' => ufio()->get_option('enable_lazy_loading', true),
            'enable_webp' => ufio()->get_option('enable_webp', true),
            'compression_quality' => ufio()->get_option('compression_quality', 85),
        ];
    }
    
    /**
     * Initialize SEO optimizer
     */
    public function init() {
        // Frontend hooks
        add_action('wp_head', [$this, 'output_structured_data'], 5);
        add_action('wp_head', [$this, 'output_meta_tags'], 10);
        
        // Image optimization hooks
        add_filter('wp_get_attachment_image_attributes', [$this, 'optimize_image_attributes'], 10, 3);
        add_filter('the_content', [$this, 'optimize_content_images'], 20);
        
        // Sitemap hooks
        add_action('init', [$this, 'register_image_sitemap']);
        
        // Schema markup hooks
        add_filter('wp_get_attachment_metadata', [$this, 'enhance_attachment_metadata'], 10, 2);
        
        // Performance hooks
        if ($this->config['enable_lazy_loading']) {
            add_filter('wp_get_attachment_image_attributes', [$this, 'add_lazy_loading'], 15, 3);
        }
        
        // WebP support
        if ($this->config['enable_webp']) {
            add_filter('wp_generate_attachment_metadata', [$this, 'generate_webp_versions'], 10, 2);
        }
    }
    
    /**
     * Output structured data for images
     */
    public function output_structured_data() {
        if (!$this->config['enable_structured_data']) {
            return;
        }
        
        global $post;
        
        if (!is_singular() || !$post) {
            return;
        }
        
        $structured_data = $this->generate_post_structured_data($post);
        
        if (!empty($structured_data)) {
            echo '<script type="application/ld+json">' . json_encode($structured_data, JSON_UNESCAPED_SLASHES) . '</script>' . "\n";
        }
    }
    
    /**
     * Output meta tags for images
     */
    public function output_meta_tags() {
        if (!$this->config['enable_meta_tags']) {
            return;
        }
        
        global $post;
        
        if (!is_singular() || !$post) {
            return;
        }
        
        $meta_tags = $this->generate_image_meta_tags($post);
        
        foreach ($meta_tags as $property => $content) {
            if (!empty($content)) {
                printf('<meta property="%s" content="%s" />' . "\n", esc_attr($property), esc_attr($content));
            }
        }
    }
    
    /**
     * Optimize image attributes
     * 
     * @param array $attr Image attributes
     * @param \WP_Post $attachment Attachment object
     * @param string $size Image size
     * @return array Optimized attributes
     */
    public function optimize_image_attributes($attr, $attachment, $size) {
        // Enhance alt text if empty
        if (empty($attr['alt'])) {
            $optimized_alt = $this->generate_optimized_alt_text($attachment);
            if ($optimized_alt) {
                $attr['alt'] = $optimized_alt;
            }
        }
        
        // Add structured data attributes
        $attr['itemscope'] = '';
        $attr['itemtype'] = 'https://schema.org/ImageObject';
        
        // Add loading optimization
        if (!isset($attr['loading'])) {
            $attr['loading'] = 'lazy';
        }
        
        // Add decoding optimization
        if (!isset($attr['decoding'])) {
            $attr['decoding'] = 'async';
        }
        
        return $attr;
    }
    
    /**
     * Optimize content images
     * 
     * @param string $content Post content
     * @return string Optimized content
     */
    public function optimize_content_images($content) {
        if (!is_singular()) {
            return $content;
        }
        
        // Find all images in content
        preg_match_all('/<img[^>]+>/i', $content, $matches);
        
        if (empty($matches[0])) {
            return $content;
        }
        
        foreach ($matches[0] as $img_tag) {
            $optimized_tag = $this->optimize_img_tag($img_tag);
            $content = str_replace($img_tag, $optimized_tag, $content);
        }
        
        return $content;
    }
    
    /**
     * Generate structured data for post
     * 
     * @param \WP_Post $post Post object
     * @return array Structured data
     */
    private function generate_post_structured_data($post) {
        $cache_key = "structured_data_{$post->ID}";
        $cached_data = $this->cache->get($cache_key, Cache::GROUP_SEO);
        
        if ($cached_data !== false) {
            return $cached_data;
        }
        
        $structured_data = [
            '@context' => 'https://schema.org',
            '@type' => 'Article',
            'headline' => $post->post_title,
            'description' => $this->get_post_description($post),
            'url' => get_permalink($post->ID),
            'datePublished' => get_the_date('c', $post->ID),
            'dateModified' => get_the_modified_date('c', $post->ID),
            'author' => [
                '@type' => 'Person',
                'name' => get_the_author_meta('display_name', $post->post_author),
                'url' => get_author_posts_url($post->post_author),
            ],
            'publisher' => [
                '@type' => 'Organization',
                'name' => get_bloginfo('name'),
                'url' => home_url(),
            ],
        ];
        
        // Add featured image
        $featured_image_id = get_post_thumbnail_id($post->ID);
        if ($featured_image_id) {
            $image_data = $this->get_image_structured_data($featured_image_id);
            if ($image_data) {
                $structured_data['image'] = $image_data;
            }
        }
        
        // Add content images
        $content_images = $this->extract_content_images($post->post_content);
        if (!empty($content_images)) {
            $structured_data['associatedMedia'] = [];
            
            foreach ($content_images as $image_id) {
                $image_data = $this->get_image_structured_data($image_id);
                if ($image_data) {
                    $structured_data['associatedMedia'][] = $image_data;
                }
            }
        }
        
        // Add breadcrumbs
        $breadcrumbs = $this->generate_breadcrumbs($post);
        if (!empty($breadcrumbs)) {
            $structured_data['breadcrumb'] = $breadcrumbs;
        }
        
        // Cache the data
        $this->cache->set($cache_key, $structured_data, Cache::GROUP_SEO, HOUR_IN_SECONDS);
        
        return $structured_data;
    }
    
    /**
     * Get image structured data
     * 
     * @param int $attachment_id Attachment ID
     * @return array|null Image structured data
     */
    private function get_image_structured_data($attachment_id) {
        $attachment = get_post($attachment_id);
        if (!$attachment) {
            return null;
        }
        
        $metadata = wp_get_attachment_metadata($attachment_id);
        $image_url = wp_get_attachment_url($attachment_id);
        
        if (!$metadata || !$image_url) {
            return null;
        }
        
        $alt_text = get_post_meta($attachment_id, '_wp_attachment_image_alt', true);
        
        return [
            '@type' => 'ImageObject',
            'url' => $image_url,
            'width' => $metadata['width'] ?? null,
            'height' => $metadata['height'] ?? null,
            'caption' => $attachment->post_excerpt ?: null,
            'description' => $attachment->post_content ?: $alt_text ?: null,
            'name' => $attachment->post_title ?: null,
            'contentUrl' => $image_url,
            'thumbnailUrl' => wp_get_attachment_image_url($attachment_id, 'thumbnail'),
        ];
    }
    
    /**
     * Generate image meta tags
     * 
     * @param \WP_Post $post Post object
     * @return array Meta tags
     */
    private function generate_image_meta_tags($post) {
        $meta_tags = [];
        
        // Featured image meta tags
        $featured_image_id = get_post_thumbnail_id($post->ID);
        if ($featured_image_id) {
            $image_url = wp_get_attachment_image_url($featured_image_id, 'large');
            $metadata = wp_get_attachment_metadata($featured_image_id);
            $alt_text = get_post_meta($featured_image_id, '_wp_attachment_image_alt', true);
            
            if ($image_url) {
                // Open Graph
                $meta_tags['og:image'] = $image_url;
                $meta_tags['og:image:secure_url'] = $image_url;
                $meta_tags['og:image:width'] = $metadata['width'] ?? '';
                $meta_tags['og:image:height'] = $metadata['height'] ?? '';
                $meta_tags['og:image:alt'] = $alt_text ?: $post->post_title;
                
                // Twitter Card
                $meta_tags['twitter:card'] = 'summary_large_image';
                $meta_tags['twitter:image'] = $image_url;
                $meta_tags['twitter:image:alt'] = $alt_text ?: $post->post_title;
            }
        }
        
        return $meta_tags;
    }
    
    /**
     * Generate optimized alt text
     * 
     * @param \WP_Post $attachment Attachment object
     * @return string|null Optimized alt text
     */
    private function generate_optimized_alt_text($attachment) {
        // Check if we have AI-generated alt text
        $db_metadata = $this->database->get_image_metadata($attachment->ID);
        if ($db_metadata && !empty($db_metadata->ai_description)) {
            return $db_metadata->ai_description;
        }
        
        // Fallback to attachment title or caption
        if (!empty($attachment->post_excerpt)) {
            return $attachment->post_excerpt;
        }
        
        if (!empty($attachment->post_title)) {
            return $attachment->post_title;
        }
        
        return null;
    }
    
    /**
     * Optimize individual img tag
     * 
     * @param string $img_tag Image tag HTML
     * @return string Optimized image tag
     */
    private function optimize_img_tag($img_tag) {
        // Add lazy loading if not present
        if (strpos($img_tag, 'loading=') === false) {
            $img_tag = str_replace('<img ', '<img loading="lazy" ', $img_tag);
        }
        
        // Add decoding optimization
        if (strpos($img_tag, 'decoding=') === false) {
            $img_tag = str_replace('<img ', '<img decoding="async" ', $img_tag);
        }
        
        // Add structured data attributes
        if (strpos($img_tag, 'itemscope') === false) {
            $img_tag = str_replace('<img ', '<img itemscope itemtype="https://schema.org/ImageObject" ', $img_tag);
        }
        
        return $img_tag;
    }
    
    /**
     * Extract content images
     * 
     * @param string $content Post content
     * @return array Image attachment IDs
     */
    private function extract_content_images($content) {
        $image_ids = [];
        
        preg_match_all('/wp-image-(\d+)/', $content, $matches);
        
        if (!empty($matches[1])) {
            $image_ids = array_map('intval', $matches[1]);
            $image_ids = array_unique($image_ids);
        }
        
        return $image_ids;
    }
    
    /**
     * Generate breadcrumbs
     * 
     * @param \WP_Post $post Post object
     * @return array|null Breadcrumb structured data
     */
    private function generate_breadcrumbs($post) {
        $breadcrumbs = [
            '@type' => 'BreadcrumbList',
            'itemListElement' => [],
        ];
        
        $position = 1;
        
        // Home
        $breadcrumbs['itemListElement'][] = [
            '@type' => 'ListItem',
            'position' => $position++,
            'name' => get_bloginfo('name'),
            'item' => home_url(),
        ];
        
        // Categories (for posts)
        if ($post->post_type === 'post') {
            $categories = get_the_category($post->ID);
            if (!empty($categories)) {
                $category = $categories[0];
                $breadcrumbs['itemListElement'][] = [
                    '@type' => 'ListItem',
                    'position' => $position++,
                    'name' => $category->name,
                    'item' => get_category_link($category->term_id),
                ];
            }
        }
        
        // Current post
        $breadcrumbs['itemListElement'][] = [
            '@type' => 'ListItem',
            'position' => $position,
            'name' => $post->post_title,
            'item' => get_permalink($post->ID),
        ];
        
        return $breadcrumbs;
    }
    
    /**
     * Get post description
     * 
     * @param \WP_Post $post Post object
     * @return string Post description
     */
    private function get_post_description($post) {
        if (!empty($post->post_excerpt)) {
            return wp_strip_all_tags($post->post_excerpt);
        }
        
        $content = wp_strip_all_tags($post->post_content);
        return wp_trim_words($content, 30);
    }
    
    /**
     * Add lazy loading attributes
     * 
     * @param array $attr Image attributes
     * @param \WP_Post $attachment Attachment object
     * @param string $size Image size
     * @return array Modified attributes
     */
    public function add_lazy_loading($attr, $attachment, $size) {
        if (!isset($attr['loading'])) {
            $attr['loading'] = 'lazy';
        }
        
        return $attr;
    }
    
    /**
     * Generate WebP versions of images
     * 
     * @param array $metadata Attachment metadata
     * @param int $attachment_id Attachment ID
     * @return array Modified metadata
     */
    public function generate_webp_versions($metadata, $attachment_id) {
        if (!function_exists('imagewebp')) {
            return $metadata;
        }
        
        $file_path = get_attached_file($attachment_id);
        if (!$file_path || !file_exists($file_path)) {
            return $metadata;
        }
        
        $this->create_webp_version($file_path);
        
        // Create WebP versions for all sizes
        if (!empty($metadata['sizes'])) {
            $upload_dir = wp_upload_dir();
            $base_dir = dirname($file_path);
            
            foreach ($metadata['sizes'] as $size => $size_data) {
                $size_file = $base_dir . '/' . $size_data['file'];
                if (file_exists($size_file)) {
                    $this->create_webp_version($size_file);
                }
            }
        }
        
        return $metadata;
    }
    
    /**
     * Create WebP version of image
     * 
     * @param string $file_path Original image file path
     * @return bool Success
     */
    private function create_webp_version($file_path) {
        $webp_path = preg_replace('/\.(jpe?g|png)$/i', '.webp', $file_path);
        
        if (file_exists($webp_path)) {
            return true; // Already exists
        }
        
        $image_info = getimagesize($file_path);
        if (!$image_info) {
            return false;
        }
        
        $mime_type = $image_info['mime'];
        $image = null;
        
        switch ($mime_type) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg($file_path);
                break;
            case 'image/png':
                $image = imagecreatefrompng($file_path);
                break;
            default:
                return false;
        }
        
        if (!$image) {
            return false;
        }
        
        $quality = $this->config['compression_quality'];
        $success = imagewebp($image, $webp_path, $quality);
        
        imagedestroy($image);
        
        if ($success) {
            $this->logger->debug('WebP version created', [
                'original' => $file_path,
                'webp' => $webp_path,
                'quality' => $quality,
            ]);
        }
        
        return $success;
    }
    
    /**
     * Register image sitemap
     */
    public function register_image_sitemap() {
        add_action('wp_sitemaps_init', function($sitemaps) {
            $sitemaps->add_provider('images', new ImageSitemapProvider());
        });
    }
    
    /**
     * Enhance attachment metadata
     * 
     * @param array $metadata Attachment metadata
     * @param int $attachment_id Attachment ID
     * @return array Enhanced metadata
     */
    public function enhance_attachment_metadata($metadata, $attachment_id) {
        if (!wp_attachment_is_image($attachment_id)) {
            return $metadata;
        }
        
        // Add SEO metadata
        $db_metadata = $this->database->get_image_metadata($attachment_id);
        if ($db_metadata) {
            $metadata['seo_score'] = $db_metadata->seo_score;
            $metadata['ai_confidence'] = $db_metadata->ai_confidence;
            $metadata['keywords'] = $db_metadata->keywords;
        }
        
        return $metadata;
    }
    
    /**
     * Calculate SEO score for image
     * 
     * @param int $attachment_id Attachment ID
     * @return float SEO score (0-1)
     */
    public function calculate_seo_score($attachment_id) {
        $score = 0.0;
        $factors = 0;
        
        // Alt text
        $alt_text = get_post_meta($attachment_id, '_wp_attachment_image_alt', true);
        if (!empty($alt_text)) {
            $score += 0.3;
        }
        $factors++;
        
        // Title
        $attachment = get_post($attachment_id);
        if ($attachment && !empty($attachment->post_title)) {
            $score += 0.2;
        }
        $factors++;
        
        // Caption
        if ($attachment && !empty($attachment->post_excerpt)) {
            $score += 0.2;
        }
        $factors++;
        
        // File size optimization
        $metadata = wp_get_attachment_metadata($attachment_id);
        if ($metadata && isset($metadata['filesize'])) {
            $file_size = $metadata['filesize'];
            if ($file_size < 500000) { // Less than 500KB
                $score += 0.15;
            } elseif ($file_size < 1000000) { // Less than 1MB
                $score += 0.1;
            }
        }
        $factors++;
        
        // WebP availability
        $file_path = get_attached_file($attachment_id);
        if ($file_path) {
            $webp_path = preg_replace('/\.(jpe?g|png)$/i', '.webp', $file_path);
            if (file_exists($webp_path)) {
                $score += 0.15;
            }
        }
        $factors++;
        
        return $factors > 0 ? $score / $factors : 0.0;
    }
    
    /**
     * Get SEO recommendations for image
     * 
     * @param int $attachment_id Attachment ID
     * @return array SEO recommendations
     */
    public function get_seo_recommendations($attachment_id) {
        $recommendations = [];
        
        // Check alt text
        $alt_text = get_post_meta($attachment_id, '_wp_attachment_image_alt', true);
        if (empty($alt_text)) {
            $recommendations[] = [
                'type' => 'warning',
                'message' => 'Missing alt text for accessibility and SEO',
                'action' => 'Add descriptive alt text',
            ];
        }
        
        // Check file size
        $metadata = wp_get_attachment_metadata($attachment_id);
        if ($metadata && isset($metadata['filesize'])) {
            $file_size = $metadata['filesize'];
            if ($file_size > 1000000) { // Greater than 1MB
                $recommendations[] = [
                    'type' => 'warning',
                    'message' => 'Large file size may impact page load speed',
                    'action' => 'Consider compressing the image',
                ];
            }
        }
        
        // Check WebP availability
        $file_path = get_attached_file($attachment_id);
        if ($file_path) {
            $webp_path = preg_replace('/\.(jpe?g|png)$/i', '.webp', $file_path);
            if (!file_exists($webp_path)) {
                $recommendations[] = [
                    'type' => 'info',
                    'message' => 'WebP version not available',
                    'action' => 'Generate WebP version for better performance',
                ];
            }
        }
        
        return $recommendations;
    }
}

/**
 * Image Sitemap Provider
 */
class ImageSitemapProvider extends \WP_Sitemaps_Provider {
    
    public function get_name() {
        return 'images';
    }
    
    public function get_object_type() {
        return 'image';
    }
    
    public function get_url_list($page_num, $object_subtype = '') {
        $url_list = [];
        
        $args = [
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'post_status' => 'inherit',
            'posts_per_page' => wp_sitemaps_get_max_urls($this->object_type),
            'paged' => $page_num,
            'no_found_rows' => true,
        ];
        
        $query = new \WP_Query($args);
        
        foreach ($query->posts as $attachment) {
            $image_url = wp_get_attachment_url($attachment->ID);
            if ($image_url) {
                $url_list[] = [
                    'loc' => $image_url,
                    'lastmod' => mysql2date('c', $attachment->post_modified_gmt),
                ];
            }
        }
        
        return $url_list;
    }
    
    public function get_max_num_pages($object_subtype = '') {
        $args = [
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'post_status' => 'inherit',
            'no_found_rows' => false,
            'update_post_meta_cache' => false,
            'update_post_term_cache' => false,
        ];
        
        $query = new \WP_Query($args);
        $total_images = $query->found_posts;
        
        return (int) ceil($total_images / wp_sitemaps_get_max_urls($this->object_type));
    }
}
