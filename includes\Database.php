<?php
/**
 * Database management class
 * 
 * @package UltraFeaturedImageOptimizer
 * @subpackage Database
 */

namespace UltraFeaturedImageOptimizer;

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Database class for managing plugin tables and queries
 */
class Database {
    
    /**
     * Table names
     */
    private $tables = [
        'image_metadata' => 'ufio_image_metadata',
        'processing_queue' => 'ufio_processing_queue',
        'ai_cache' => 'ufio_ai_cache',
        'performance_logs' => 'ufio_performance_logs',
    ];
    
    /**
     * WordPress database instance
     * @var wpdb
     */
    private $wpdb;
    
    /**
     * Constructor
     */
    public function __construct() {
        global $wpdb;
        $this->wpdb = $wpdb;
        
        // Add table names to wpdb
        foreach ($this->tables as $key => $table) {
            $this->wpdb->{$key} = $this->wpdb->prefix . $table;
        }
    }
    
    /**
     * Create database tables
     */
    public function create_tables() {
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        
        $charset_collate = $this->wpdb->get_charset_collate();
        
        // Image metadata table
        $sql_image_metadata = "CREATE TABLE {$this->wpdb->image_metadata} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            attachment_id bigint(20) unsigned NOT NULL,
            post_id bigint(20) unsigned DEFAULT NULL,
            image_url varchar(500) NOT NULL,
            alt_text text DEFAULT NULL,
            caption text DEFAULT NULL,
            description text DEFAULT NULL,
            keywords text DEFAULT NULL,
            ai_tags text DEFAULT NULL,
            ai_description text DEFAULT NULL,
            ai_confidence decimal(3,2) DEFAULT NULL,
            seo_score decimal(3,2) DEFAULT NULL,
            file_size bigint(20) unsigned DEFAULT NULL,
            dimensions varchar(20) DEFAULT NULL,
            format varchar(10) DEFAULT NULL,
            compression_ratio decimal(3,2) DEFAULT NULL,
            webp_available tinyint(1) DEFAULT 0,
            cdn_url varchar(500) DEFAULT NULL,
            last_optimized datetime DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY attachment_id (attachment_id),
            KEY post_id (post_id),
            KEY last_optimized (last_optimized),
            KEY ai_confidence (ai_confidence),
            KEY seo_score (seo_score),
            FULLTEXT KEY search_text (alt_text, caption, description, keywords, ai_tags, ai_description)
        ) $charset_collate;";
        
        // Processing queue table
        $sql_processing_queue = "CREATE TABLE {$this->wpdb->processing_queue} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            item_type varchar(20) NOT NULL DEFAULT 'post',
            item_id bigint(20) unsigned NOT NULL,
            action varchar(50) NOT NULL,
            priority tinyint(3) unsigned DEFAULT 5,
            status varchar(20) DEFAULT 'pending',
            attempts tinyint(3) unsigned DEFAULT 0,
            max_attempts tinyint(3) unsigned DEFAULT 3,
            error_message text DEFAULT NULL,
            scheduled_at datetime DEFAULT CURRENT_TIMESTAMP,
            started_at datetime DEFAULT NULL,
            completed_at datetime DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY item_lookup (item_type, item_id),
            KEY status_priority (status, priority),
            KEY scheduled_at (scheduled_at),
            KEY attempts (attempts)
        ) $charset_collate;";
        
        // AI cache table
        $sql_ai_cache = "CREATE TABLE {$this->wpdb->ai_cache} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            cache_key varchar(255) NOT NULL,
            cache_type varchar(50) NOT NULL,
            cache_data longtext NOT NULL,
            expires_at datetime NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY cache_key (cache_key),
            KEY cache_type (cache_type),
            KEY expires_at (expires_at)
        ) $charset_collate;";
        
        // Performance logs table
        $sql_performance_logs = "CREATE TABLE {$this->wpdb->performance_logs} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            action varchar(100) NOT NULL,
            execution_time decimal(8,4) NOT NULL,
            memory_usage bigint(20) unsigned NOT NULL,
            query_count int(10) unsigned DEFAULT 0,
            cache_hits int(10) unsigned DEFAULT 0,
            cache_misses int(10) unsigned DEFAULT 0,
            error_count int(10) unsigned DEFAULT 0,
            metadata text DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY action (action),
            KEY execution_time (execution_time),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        // Execute table creation
        dbDelta($sql_image_metadata);
        dbDelta($sql_processing_queue);
        dbDelta($sql_ai_cache);
        dbDelta($sql_performance_logs);
        
        // Create indexes for better performance
        $this->create_additional_indexes();
    }
    
    /**
     * Create additional indexes for performance
     */
    private function create_additional_indexes() {
        // Add composite indexes for common queries
        $indexes = [
            "CREATE INDEX idx_image_post_confidence ON {$this->wpdb->image_metadata} (post_id, ai_confidence DESC)",
            "CREATE INDEX idx_queue_status_priority_scheduled ON {$this->wpdb->processing_queue} (status, priority DESC, scheduled_at ASC)",
            "CREATE INDEX idx_cache_type_expires ON {$this->wpdb->ai_cache} (cache_type, expires_at)",
            "CREATE INDEX idx_perf_action_time ON {$this->wpdb->performance_logs} (action, created_at DESC)",
        ];
        
        foreach ($indexes as $index_sql) {
            $this->wpdb->query($index_sql);
        }
    }
    
    /**
     * Get image metadata by attachment ID
     * 
     * @param int $attachment_id Attachment ID
     * @return object|null Image metadata
     */
    public function get_image_metadata($attachment_id) {
        $sql = $this->wpdb->prepare(
            "SELECT * FROM {$this->wpdb->image_metadata} WHERE attachment_id = %d",
            $attachment_id
        );
        
        return $this->wpdb->get_row($sql);
    }
    
    /**
     * Insert or update image metadata
     * 
     * @param array $data Image metadata
     * @return int|false Insert ID or false on failure
     */
    public function save_image_metadata($data) {
        $defaults = [
            'attachment_id' => 0,
            'post_id' => null,
            'image_url' => '',
            'alt_text' => null,
            'caption' => null,
            'description' => null,
            'keywords' => null,
            'ai_tags' => null,
            'ai_description' => null,
            'ai_confidence' => null,
            'seo_score' => null,
            'file_size' => null,
            'dimensions' => null,
            'format' => null,
            'compression_ratio' => null,
            'webp_available' => 0,
            'cdn_url' => null,
            'last_optimized' => current_time('mysql'),
        ];
        
        $data = wp_parse_args($data, $defaults);
        
        // Check if record exists
        $existing = $this->get_image_metadata($data['attachment_id']);
        
        if ($existing) {
            // Update existing record
            $result = $this->wpdb->update(
                $this->wpdb->image_metadata,
                $data,
                ['attachment_id' => $data['attachment_id']],
                $this->get_data_format($data),
                ['%d']
            );
            return $result !== false ? $existing->id : false;
        } else {
            // Insert new record
            $result = $this->wpdb->insert(
                $this->wpdb->image_metadata,
                $data,
                $this->get_data_format($data)
            );
            return $result !== false ? $this->wpdb->insert_id : false;
        }
    }
    
    /**
     * Add item to processing queue
     * 
     * @param array $data Queue item data
     * @return int|false Insert ID or false on failure
     */
    public function add_to_queue($data) {
        $defaults = [
            'item_type' => 'post',
            'item_id' => 0,
            'action' => 'process_images',
            'priority' => 5,
            'status' => 'pending',
            'attempts' => 0,
            'max_attempts' => 3,
            'scheduled_at' => current_time('mysql'),
        ];
        
        $data = wp_parse_args($data, $defaults);
        
        // Check if item already exists in queue
        $existing = $this->wpdb->get_var($this->wpdb->prepare(
            "SELECT id FROM {$this->wpdb->processing_queue} 
             WHERE item_type = %s AND item_id = %d AND action = %s AND status = 'pending'",
            $data['item_type'],
            $data['item_id'],
            $data['action']
        ));
        
        if ($existing) {
            return $existing;
        }
        
        $result = $this->wpdb->insert(
            $this->wpdb->processing_queue,
            $data,
            ['%s', '%d', '%s', '%d', '%s', '%d', '%d', '%s']
        );
        
        return $result !== false ? $this->wpdb->insert_id : false;
    }
    
    /**
     * Get next items from processing queue
     * 
     * @param int $limit Number of items to retrieve
     * @return array Queue items
     */
    public function get_queue_items($limit = 10) {
        $sql = $this->wpdb->prepare(
            "SELECT * FROM {$this->wpdb->processing_queue} 
             WHERE status = 'pending' AND attempts < max_attempts 
             AND scheduled_at <= %s
             ORDER BY priority DESC, scheduled_at ASC 
             LIMIT %d",
            current_time('mysql'),
            $limit
        );
        
        return $this->wpdb->get_results($sql);
    }
    
    /**
     * Update queue item status
     * 
     * @param int $id Queue item ID
     * @param string $status New status
     * @param string $error_message Error message (optional)
     * @return bool Success
     */
    public function update_queue_item($id, $status, $error_message = null) {
        $data = [
            'status' => $status,
            'updated_at' => current_time('mysql'),
        ];
        
        if ($status === 'processing') {
            $data['started_at'] = current_time('mysql');
            $this->wpdb->query($this->wpdb->prepare(
                "UPDATE {$this->wpdb->processing_queue} SET attempts = attempts + 1 WHERE id = %d",
                $id
            ));
        } elseif ($status === 'completed') {
            $data['completed_at'] = current_time('mysql');
        } elseif ($status === 'failed' && $error_message) {
            $data['error_message'] = $error_message;
        }
        
        $result = $this->wpdb->update(
            $this->wpdb->processing_queue,
            $data,
            ['id' => $id],
            array_fill(0, count($data), '%s'),
            ['%d']
        );
        
        return $result !== false;
    }
    
    /**
     * Get data format array for wpdb operations
     * 
     * @param array $data Data array
     * @return array Format array
     */
    private function get_data_format($data) {
        $formats = [];
        foreach ($data as $key => $value) {
            if (in_array($key, ['attachment_id', 'post_id', 'file_size', 'webp_available'])) {
                $formats[] = '%d';
            } elseif (in_array($key, ['ai_confidence', 'seo_score', 'compression_ratio'])) {
                $formats[] = '%f';
            } else {
                $formats[] = '%s';
            }
        }
        return $formats;
    }
    
    /**
     * Clean up old records
     */
    public function cleanup() {
        // Remove completed queue items older than 7 days
        $this->wpdb->query($this->wpdb->prepare(
            "DELETE FROM {$this->wpdb->processing_queue} 
             WHERE status = 'completed' AND completed_at < %s",
            date('Y-m-d H:i:s', strtotime('-7 days'))
        ));
        
        // Remove expired cache entries
        $this->wpdb->query($this->wpdb->prepare(
            "DELETE FROM {$this->wpdb->ai_cache} WHERE expires_at < %s",
            current_time('mysql')
        ));
        
        // Remove old performance logs (keep last 30 days)
        $this->wpdb->query($this->wpdb->prepare(
            "DELETE FROM {$this->wpdb->performance_logs} WHERE created_at < %s",
            date('Y-m-d H:i:s', strtotime('-30 days'))
        ));
    }
    
    /**
     * Get database statistics
     * 
     * @return array Statistics
     */
    public function get_stats() {
        $stats = [];
        
        // Image metadata stats
        $stats['total_images'] = $this->wpdb->get_var(
            "SELECT COUNT(*) FROM {$this->wpdb->image_metadata}"
        );
        
        $stats['optimized_images'] = $this->wpdb->get_var(
            "SELECT COUNT(*) FROM {$this->wpdb->image_metadata} WHERE last_optimized IS NOT NULL"
        );
        
        // Queue stats
        $stats['pending_queue'] = $this->wpdb->get_var(
            "SELECT COUNT(*) FROM {$this->wpdb->processing_queue} WHERE status = 'pending'"
        );
        
        $stats['processing_queue'] = $this->wpdb->get_var(
            "SELECT COUNT(*) FROM {$this->wpdb->processing_queue} WHERE status = 'processing'"
        );
        
        // Cache stats
        $stats['cache_entries'] = $this->wpdb->get_var(
            "SELECT COUNT(*) FROM {$this->wpdb->ai_cache} WHERE expires_at > NOW()"
        );
        
        return $stats;
    }
}
