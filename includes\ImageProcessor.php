<?php
/**
 * Image processing engine
 * 
 * @package UltraFeaturedImageOptimizer
 * @subpackage ImageProcessor
 */

namespace UltraFeaturedImageOptimizer;

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Advanced image processing with AI integration and optimization
 */
class ImageProcessor {
    
    /**
     * Supported image formats
     */
    const SUPPORTED_FORMATS = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    
    /**
     * Image size thresholds
     */
    const MIN_WIDTH = 300;
    const MIN_HEIGHT = 200;
    const MAX_FILE_SIZE = 5242880; // 5MB
    
    /**
     * Dependencies
     */
    private $database;
    private $cache;
    private $logger;
    private $ai_handler;
    
    /**
     * Processing statistics
     * @var array
     */
    private $stats = [
        'processed' => 0,
        'optimized' => 0,
        'errors' => 0,
        'skipped' => 0,
    ];
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->database = new Database();
        $this->cache = new Cache();
        $this->logger = new Logger();
        $this->ai_handler = new AIHandler();
    }
    
    /**
     * Process post images (featured + content)
     * 
     * @param int $post_id Post ID
     * @param array $options Processing options
     * @return array Processing results
     */
    public function process_post_images($post_id, $options = []) {
        $tracking_id = $this->logger->start_performance_tracking('process_post_images');
        
        try {
            $post = get_post($post_id);
            if (!$post) {
                throw new \Exception("Post not found: {$post_id}");
            }
            
            $results = [
                'featured_image' => null,
                'content_images' => [],
                'seo_data' => [],
                'errors' => [],
            ];
            
            // Process featured image
            if (ufio()->get_option('auto_assign_featured', true)) {
                $results['featured_image'] = $this->process_featured_image($post, $options);
            }
            
            // Process content images
            if (ufio()->get_option('auto_insert_content', true)) {
                $results['content_images'] = $this->process_content_images($post, $options);
            }
            
            // Generate SEO data
            if (ufio()->get_option('enable_seo_optimization', true)) {
                $results['seo_data'] = $this->generate_seo_data($post, $results);
            }
            
            $this->stats['processed']++;
            
            return $results;
            
        } catch (\Exception $e) {
            $this->logger->error('Failed to process post images', [
                'post_id' => $post_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            $this->stats['errors']++;
            
            return [
                'featured_image' => null,
                'content_images' => [],
                'seo_data' => [],
                'errors' => [$e->getMessage()],
            ];
            
        } finally {
            $this->logger->end_performance_tracking($tracking_id, [
                'post_id' => $post_id,
                'stats' => $this->stats,
            ]);
        }
    }
    
    /**
     * Process featured image for post
     * 
     * @param \WP_Post $post Post object
     * @param array $options Processing options
     * @return array|null Featured image data
     */
    public function process_featured_image($post, $options = []) {
        // Check if post already has featured image
        $existing_featured = get_post_thumbnail_id($post->ID);
        if ($existing_featured && !($options['force_replace'] ?? false)) {
            $this->logger->debug('Post already has featured image', ['post_id' => $post->ID]);
            return $this->get_image_data($existing_featured);
        }
        
        // Find best image for featured
        $best_image = $this->find_best_featured_image($post, $options);
        
        if (!$best_image) {
            $this->logger->info('No suitable featured image found', ['post_id' => $post->ID]);
            return null;
        }
        
        // Set as featured image
        $result = set_post_thumbnail($post->ID, $best_image['attachment_id']);
        
        if ($result) {
            $this->logger->info('Featured image assigned', [
                'post_id' => $post->ID,
                'attachment_id' => $best_image['attachment_id'],
                'confidence' => $best_image['confidence'] ?? 0,
            ]);
            
            // Update metadata
            $this->database->save_image_metadata([
                'attachment_id' => $best_image['attachment_id'],
                'post_id' => $post->ID,
                'ai_confidence' => $best_image['confidence'] ?? 0,
                'seo_score' => $best_image['seo_score'] ?? 0,
            ]);
            
            return $best_image;
        }
        
        return null;
    }
    
    /**
     * Process content images for post
     * 
     * @param \WP_Post $post Post object
     * @param array $options Processing options
     * @return array Content images data
     */
    public function process_content_images($post, $options = []) {
        $content_images = [];
        $max_images = ufio()->get_option('max_images_per_post', 5);
        
        // Parse content for headings
        $headings = $this->extract_headings($post->post_content);
        
        if (empty($headings)) {
            $this->logger->debug('No headings found in content', ['post_id' => $post->ID]);
            return $content_images;
        }
        
        $images_added = 0;
        
        foreach ($headings as $heading) {
            if ($images_added >= $max_images) {
                break;
            }
            
            // Find relevant image for heading
            $relevant_image = $this->find_relevant_image($heading, $post, $options);
            
            if ($relevant_image) {
                $content_images[] = [
                    'heading' => $heading,
                    'image' => $relevant_image,
                    'position' => $this->calculate_insertion_position($heading, $post->post_content),
                ];
                
                $images_added++;
            }
        }
        
        // Insert images into content if enabled
        if (!empty($content_images) && ($options['insert_into_content'] ?? true)) {
            $this->insert_images_into_content($post->ID, $content_images);
        }
        
        return $content_images;
    }
    
    /**
     * Find best featured image for post
     * 
     * @param \WP_Post $post Post object
     * @param array $options Processing options
     * @return array|null Best image data
     */
    private function find_best_featured_image($post, $options = []) {
        $cache_key = "featured_image_{$post->ID}_" . md5(serialize($options));
        $cached_result = $this->cache->get($cache_key, Cache::GROUP_IMAGE);
        
        if ($cached_result !== false) {
            return $cached_result;
        }
        
        $candidates = [];
        
        // 1. Check media library for relevant images
        $media_candidates = $this->find_media_library_candidates($post);
        $candidates = array_merge($candidates, $media_candidates);
        
        // 2. Check existing content images
        $content_candidates = $this->find_content_image_candidates($post);
        $candidates = array_merge($candidates, $content_candidates);
        
        // 3. Use AI to score candidates
        if (ufio()->get_option('use_ai_processing', true) && !empty($candidates)) {
            $candidates = $this->ai_handler->score_image_relevance($candidates, $post);
        }
        
        // 4. Apply filters and sorting
        $candidates = $this->filter_image_candidates($candidates);
        $candidates = $this->sort_candidates_by_score($candidates);
        
        $best_image = !empty($candidates) ? $candidates[0] : null;
        
        // Cache result
        $this->cache->set($cache_key, $best_image, Cache::GROUP_IMAGE, HOUR_IN_SECONDS);
        
        return $best_image;
    }
    
    /**
     * Find media library candidates
     * 
     * @param \WP_Post $post Post object
     * @return array Image candidates
     */
    private function find_media_library_candidates($post) {
        $args = [
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'post_status' => 'inherit',
            'posts_per_page' => 50,
            'meta_query' => [
                [
                    'key' => '_wp_attachment_image_alt',
                    'compare' => 'EXISTS',
                ],
            ],
        ];
        
        // Search by post keywords
        $keywords = $this->extract_keywords($post);
        if (!empty($keywords)) {
            $args['s'] = implode(' ', array_slice($keywords, 0, 3));
        }
        
        $attachments = get_posts($args);
        $candidates = [];
        
        foreach ($attachments as $attachment) {
            $image_data = $this->get_image_data($attachment->ID);
            if ($image_data && $this->is_suitable_for_featured($image_data)) {
                $candidates[] = array_merge($image_data, [
                    'source' => 'media_library',
                    'relevance_score' => $this->calculate_text_relevance($attachment, $post),
                ]);
            }
        }
        
        return $candidates;
    }
    
    /**
     * Find content image candidates
     * 
     * @param \WP_Post $post Post object
     * @return array Image candidates
     */
    private function find_content_image_candidates($post) {
        $candidates = [];
        
        // Extract images from content
        preg_match_all('/<img[^>]+>/i', $post->post_content, $img_tags);
        
        foreach ($img_tags[0] as $img_tag) {
            $attachment_id = $this->extract_attachment_id_from_img($img_tag);
            
            if ($attachment_id) {
                $image_data = $this->get_image_data($attachment_id);
                if ($image_data && $this->is_suitable_for_featured($image_data)) {
                    $candidates[] = array_merge($image_data, [
                        'source' => 'content',
                        'relevance_score' => 0.8, // Higher score for content images
                    ]);
                }
            }
        }
        
        return $candidates;
    }
    
    /**
     * Get comprehensive image data
     * 
     * @param int $attachment_id Attachment ID
     * @return array|null Image data
     */
    private function get_image_data($attachment_id) {
        $cache_key = "image_data_{$attachment_id}";
        $cached_data = $this->cache->get($cache_key, Cache::GROUP_IMAGE);
        
        if ($cached_data !== false) {
            return $cached_data;
        }
        
        $attachment = get_post($attachment_id);
        if (!$attachment || $attachment->post_type !== 'attachment') {
            return null;
        }
        
        $metadata = wp_get_attachment_metadata($attachment_id);
        $image_url = wp_get_attachment_url($attachment_id);
        
        if (!$metadata || !$image_url) {
            return null;
        }
        
        $image_data = [
            'attachment_id' => $attachment_id,
            'url' => $image_url,
            'title' => $attachment->post_title,
            'alt_text' => get_post_meta($attachment_id, '_wp_attachment_image_alt', true),
            'caption' => $attachment->post_excerpt,
            'description' => $attachment->post_content,
            'width' => $metadata['width'] ?? 0,
            'height' => $metadata['height'] ?? 0,
            'file_size' => $metadata['filesize'] ?? 0,
            'mime_type' => $attachment->post_mime_type,
            'format' => pathinfo($image_url, PATHINFO_EXTENSION),
            'upload_date' => $attachment->post_date,
        ];
        
        // Get additional metadata from database
        $db_metadata = $this->database->get_image_metadata($attachment_id);
        if ($db_metadata) {
            $image_data = array_merge($image_data, [
                'ai_tags' => $db_metadata->ai_tags,
                'ai_description' => $db_metadata->ai_description,
                'ai_confidence' => $db_metadata->ai_confidence,
                'seo_score' => $db_metadata->seo_score,
                'keywords' => $db_metadata->keywords,
            ]);
        }
        
        // Cache the data
        $this->cache->set($cache_key, $image_data, Cache::GROUP_IMAGE, HOUR_IN_SECONDS);
        
        return $image_data;
    }
    
    /**
     * Check if image is suitable for featured image
     * 
     * @param array $image_data Image data
     * @return bool
     */
    private function is_suitable_for_featured($image_data) {
        // Check dimensions
        if ($image_data['width'] < self::MIN_WIDTH || $image_data['height'] < self::MIN_HEIGHT) {
            return false;
        }
        
        // Check file size
        if ($image_data['file_size'] > self::MAX_FILE_SIZE) {
            return false;
        }
        
        // Check format
        if (!in_array(strtolower($image_data['format']), self::SUPPORTED_FORMATS)) {
            return false;
        }
        
        // Check aspect ratio (avoid extremely wide or tall images)
        $aspect_ratio = $image_data['width'] / $image_data['height'];
        if ($aspect_ratio < 0.5 || $aspect_ratio > 3.0) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Extract keywords from post
     * 
     * @param \WP_Post $post Post object
     * @return array Keywords
     */
    private function extract_keywords($post) {
        $text = $post->post_title . ' ' . $post->post_content;
        $text = wp_strip_all_tags($text);
        $text = preg_replace('/[^\w\s]/', ' ', $text);
        
        $words = str_word_count(strtolower($text), 1);
        $words = array_filter($words, function($word) {
            return strlen($word) > 3;
        });
        
        $word_counts = array_count_values($words);
        arsort($word_counts);
        
        return array_keys(array_slice($word_counts, 0, 10));
    }
    
    /**
     * Extract headings from content
     * 
     * @param string $content Post content
     * @return array Headings
     */
    private function extract_headings($content) {
        $headings = [];
        
        preg_match_all('/<h([2-6])[^>]*>(.*?)<\/h[2-6]>/i', $content, $matches, PREG_SET_ORDER);
        
        foreach ($matches as $match) {
            $level = (int) $match[1];
            $text = wp_strip_all_tags($match[2]);
            $text = trim($text);
            
            if (strlen($text) > 5) {
                $headings[] = [
                    'level' => $level,
                    'text' => $text,
                    'full_match' => $match[0],
                ];
            }
        }
        
        return $headings;
    }
    
    /**
     * Find relevant image for heading
     * 
     * @param array $heading Heading data
     * @param \WP_Post $post Post object
     * @param array $options Processing options
     * @return array|null Relevant image
     */
    private function find_relevant_image($heading, $post, $options = []) {
        $cache_key = "relevant_image_" . md5($heading['text'] . $post->ID);
        $cached_result = $this->cache->get($cache_key, Cache::GROUP_IMAGE);
        
        if ($cached_result !== false) {
            return $cached_result;
        }
        
        // Search for images related to heading text
        $search_terms = $this->extract_search_terms($heading['text']);
        
        $args = [
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'post_status' => 'inherit',
            'posts_per_page' => 10,
            's' => implode(' ', $search_terms),
        ];
        
        $attachments = get_posts($args);
        $candidates = [];
        
        foreach ($attachments as $attachment) {
            $image_data = $this->get_image_data($attachment->ID);
            if ($image_data) {
                $relevance_score = $this->calculate_heading_relevance($heading, $image_data);
                $candidates[] = array_merge($image_data, [
                    'relevance_score' => $relevance_score,
                ]);
            }
        }
        
        // Sort by relevance and get best match
        usort($candidates, function($a, $b) {
            return $b['relevance_score'] <=> $a['relevance_score'];
        });
        
        $best_image = !empty($candidates) ? $candidates[0] : null;
        
        // Cache result
        $this->cache->set($cache_key, $best_image, Cache::GROUP_IMAGE, HOUR_IN_SECONDS);
        
        return $best_image;
    }
    
    /**
     * Calculate text relevance between attachment and post
     * 
     * @param \WP_Post $attachment Attachment object
     * @param \WP_Post $post Post object
     * @return float Relevance score (0-1)
     */
    private function calculate_text_relevance($attachment, $post) {
        $attachment_text = $attachment->post_title . ' ' . 
                          $attachment->post_excerpt . ' ' . 
                          $attachment->post_content . ' ' .
                          get_post_meta($attachment->ID, '_wp_attachment_image_alt', true);
        
        $post_text = $post->post_title . ' ' . $post->post_content;
        
        return $this->calculate_text_similarity($attachment_text, $post_text);
    }
    
    /**
     * Calculate text similarity
     * 
     * @param string $text1 First text
     * @param string $text2 Second text
     * @return float Similarity score (0-1)
     */
    private function calculate_text_similarity($text1, $text2) {
        $text1 = strtolower(wp_strip_all_tags($text1));
        $text2 = strtolower(wp_strip_all_tags($text2));
        
        $words1 = str_word_count($text1, 1);
        $words2 = str_word_count($text2, 1);
        
        $common_words = array_intersect($words1, $words2);
        $total_words = array_unique(array_merge($words1, $words2));
        
        return count($total_words) > 0 ? count($common_words) / count($total_words) : 0;
    }
    
    /**
     * Filter image candidates
     * 
     * @param array $candidates Image candidates
     * @return array Filtered candidates
     */
    private function filter_image_candidates($candidates) {
        return array_filter($candidates, function($candidate) {
            return $this->is_suitable_for_featured($candidate);
        });
    }
    
    /**
     * Sort candidates by score
     * 
     * @param array $candidates Image candidates
     * @return array Sorted candidates
     */
    private function sort_candidates_by_score($candidates) {
        usort($candidates, function($a, $b) {
            $score_a = ($a['ai_confidence'] ?? 0) * 0.6 + ($a['relevance_score'] ?? 0) * 0.4;
            $score_b = ($b['ai_confidence'] ?? 0) * 0.6 + ($b['relevance_score'] ?? 0) * 0.4;
            
            return $score_b <=> $score_a;
        });
        
        return $candidates;
    }
    
    /**
     * Extract search terms from text
     * 
     * @param string $text Input text
     * @return array Search terms
     */
    private function extract_search_terms($text) {
        $text = wp_strip_all_tags($text);
        $words = str_word_count(strtolower($text), 1);
        
        return array_filter($words, function($word) {
            return strlen($word) > 3;
        });
    }
    
    /**
     * Calculate heading relevance
     * 
     * @param array $heading Heading data
     * @param array $image_data Image data
     * @return float Relevance score
     */
    private function calculate_heading_relevance($heading, $image_data) {
        $heading_text = $heading['text'];
        $image_text = ($image_data['title'] ?? '') . ' ' . 
                     ($image_data['alt_text'] ?? '') . ' ' . 
                     ($image_data['caption'] ?? '') . ' ' .
                     ($image_data['ai_tags'] ?? '');
        
        return $this->calculate_text_similarity($heading_text, $image_text);
    }
    
    /**
     * Calculate insertion position for image
     * 
     * @param array $heading Heading data
     * @param string $content Post content
     * @return int Position in content
     */
    private function calculate_insertion_position($heading, $content) {
        $pos = strpos($content, $heading['full_match']);
        return $pos !== false ? $pos + strlen($heading['full_match']) : 0;
    }
    
    /**
     * Insert images into post content
     * 
     * @param int $post_id Post ID
     * @param array $content_images Content images data
     * @return bool Success
     */
    private function insert_images_into_content($post_id, $content_images) {
        $post = get_post($post_id);
        if (!$post) {
            return false;
        }
        
        $content = $post->post_content;
        $offset = 0;
        
        // Sort by position (reverse order for proper insertion)
        usort($content_images, function($a, $b) {
            return $b['position'] <=> $a['position'];
        });
        
        foreach ($content_images as $content_image) {
            $image_html = $this->generate_image_html($content_image['image']);
            $position = $content_image['position'] + $offset;
            
            $content = substr_replace($content, "\n\n" . $image_html . "\n\n", $position, 0);
            $offset += strlen($image_html) + 4;
        }
        
        // Update post content
        wp_update_post([
            'ID' => $post_id,
            'post_content' => $content,
        ]);
        
        return true;
    }
    
    /**
     * Generate HTML for image
     * 
     * @param array $image_data Image data
     * @return string Image HTML
     */
    private function generate_image_html($image_data) {
        $size = ufio()->get_option('image_size', 'large');
        $image_url = wp_get_attachment_image_url($image_data['attachment_id'], $size);
        $alt_text = $image_data['alt_text'] ?? $image_data['title'] ?? '';
        $caption = $image_data['caption'] ?? '';
        
        $html = sprintf(
            '<figure class="wp-block-image size-%s"><img src="%s" alt="%s" class="wp-image-%d"/>',
            esc_attr($size),
            esc_url($image_url),
            esc_attr($alt_text),
            $image_data['attachment_id']
        );
        
        if ($caption) {
            $html .= sprintf('<figcaption>%s</figcaption>', esc_html($caption));
        }
        
        $html .= '</figure>';
        
        return $html;
    }
    
    /**
     * Extract attachment ID from img tag
     * 
     * @param string $img_tag Image tag HTML
     * @return int|null Attachment ID
     */
    private function extract_attachment_id_from_img($img_tag) {
        if (preg_match('/wp-image-(\d+)/', $img_tag, $matches)) {
            return (int) $matches[1];
        }
        
        if (preg_match('/data-id="(\d+)"/', $img_tag, $matches)) {
            return (int) $matches[1];
        }
        
        return null;
    }
    
    /**
     * Generate SEO data for processed images
     * 
     * @param \WP_Post $post Post object
     * @param array $results Processing results
     * @return array SEO data
     */
    private function generate_seo_data($post, $results) {
        $seo_data = [];
        
        // Featured image SEO
        if ($results['featured_image']) {
            $seo_data['featured_image'] = [
                'structured_data' => $this->generate_image_structured_data($results['featured_image'], $post),
                'meta_tags' => $this->generate_image_meta_tags($results['featured_image'], $post),
            ];
        }
        
        // Content images SEO
        foreach ($results['content_images'] as $content_image) {
            $seo_data['content_images'][] = [
                'structured_data' => $this->generate_image_structured_data($content_image['image'], $post),
                'alt_optimization' => $this->optimize_alt_text($content_image['image'], $content_image['heading']),
            ];
        }
        
        return $seo_data;
    }
    
    /**
     * Generate structured data for image
     * 
     * @param array $image_data Image data
     * @param \WP_Post $post Post object
     * @return array Structured data
     */
    private function generate_image_structured_data($image_data, $post) {
        return [
            '@type' => 'ImageObject',
            'url' => $image_data['url'],
            'width' => $image_data['width'],
            'height' => $image_data['height'],
            'caption' => $image_data['caption'] ?? $image_data['alt_text'] ?? '',
            'description' => $image_data['description'] ?? $image_data['ai_description'] ?? '',
            'name' => $image_data['title'] ?? '',
            'contentUrl' => $image_data['url'],
            'license' => get_permalink($post->ID),
            'acquireLicensePage' => get_permalink($post->ID),
        ];
    }
    
    /**
     * Generate meta tags for image
     * 
     * @param array $image_data Image data
     * @param \WP_Post $post Post object
     * @return array Meta tags
     */
    private function generate_image_meta_tags($image_data, $post) {
        return [
            'og:image' => $image_data['url'],
            'og:image:width' => $image_data['width'],
            'og:image:height' => $image_data['height'],
            'og:image:alt' => $image_data['alt_text'] ?? $image_data['title'] ?? '',
            'twitter:image' => $image_data['url'],
            'twitter:image:alt' => $image_data['alt_text'] ?? $image_data['title'] ?? '',
        ];
    }
    
    /**
     * Optimize alt text for image
     * 
     * @param array $image_data Image data
     * @param array $heading Heading context
     * @return string Optimized alt text
     */
    private function optimize_alt_text($image_data, $heading) {
        $current_alt = $image_data['alt_text'] ?? '';
        
        if (empty($current_alt) && !empty($heading['text'])) {
            // Generate alt text based on heading
            return sprintf('Image related to %s', $heading['text']);
        }
        
        return $current_alt;
    }
    
    /**
     * Get processing statistics
     * 
     * @return array Statistics
     */
    public function get_stats() {
        return $this->stats;
    }
    
    /**
     * Reset processing statistics
     */
    public function reset_stats() {
        $this->stats = [
            'processed' => 0,
            'optimized' => 0,
            'errors' => 0,
            'skipped' => 0,
        ];
    }
}
