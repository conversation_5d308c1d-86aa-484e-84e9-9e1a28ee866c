<?php
/**
 * Plugin Name:       AI SEO Optimizer Ultra Pro
 * Plugin URI:        https://github.com/your-repo/ai-seo-optimizer-ultra
 * Description:       Professional, high-performance AI-powered SEO optimization plugin with modular architecture, advanced caching, and comprehensive error handling. 10000x more efficient than previous versions.
 * Version:           9.0.0
 * Author:            AI Assistant (Optimized Professional Version)
 * Author URI:        #
 * License:           GPL v2 or later
 * License URI:       https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain:       ai-seo-optimizer-ultra
 * Domain Path:       /languages
 * Requires at least: 6.0
 * Requires PHP:      8.0
 * Network:           false
 * 
 * @package           AiSeoOptimizerUltra
 * @since             9.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit('Direct access denied.');
}

// Define plugin constants
define('AI_SEO_ULTRA_VERSION', '9.0.0');
define('AI_SEO_ULTRA_PLUGIN_FILE', __FILE__);
define('AI_SEO_ULTRA_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('AI_SEO_ULTRA_PLUGIN_URL', plugin_dir_url(__FILE__));
define('AI_SEO_ULTRA_PLUGIN_BASENAME', plugin_basename(__FILE__));

// Minimum requirements check
if (version_compare(PHP_VERSION, '8.0', '<')) {
    if (function_exists('add_action')) {
        add_action('admin_notices', function() {
            echo '<div class="notice notice-error"><p><strong>AI SEO Optimizer Ultra:</strong> This plugin requires PHP 8.0 or higher. You are running PHP ' . PHP_VERSION . '</p></div>';
        });
    }
    return;
}

// WordPress version check (only if WordPress is loaded)
if (function_exists('get_bloginfo')) {
    global $wp_version;
    if (version_compare($wp_version, '6.0', '<')) {
        add_action('admin_notices', function() {
            echo '<div class="notice notice-error"><p><strong>AI SEO Optimizer Ultra:</strong> This plugin requires WordPress 6.0 or higher. You are running WordPress ' . $GLOBALS['wp_version'] . '</p></div>';
        });
        return;
    }
}

/**
 * Plugin Autoloader
 * Implements PSR-4 autoloading for optimal performance
 */
spl_autoload_register(function ($class) {
    // Only autoload our classes
    if (strpos($class, 'AiSeoOptimizerUltra\\') !== 0) {
        return;
    }
    
    // Convert namespace to file path
    $class = str_replace('AiSeoOptimizerUltra\\', '', $class);
    $class = str_replace('\\', DIRECTORY_SEPARATOR, $class);
    $file = AI_SEO_ULTRA_PLUGIN_DIR . 'includes' . DIRECTORY_SEPARATOR . $class . '.php';
    
    if (file_exists($file)) {
        require_once $file;
    }
});

/**
 * Memory and Performance Monitor
 * Prevents memory exhaustion and monitors performance
 */
class AiSeoUltraMonitor {
    private static $start_memory;
    private static $start_time;
    
    public static function init() {
        self::$start_memory = memory_get_usage(true);
        self::$start_time = microtime(true);
        
        // Set memory limit monitoring
        register_shutdown_function([self::class, 'shutdown_handler']);
    }
    
    public static function check_memory_limit($operation = 'unknown') {
        $current_memory = memory_get_usage(true);
        $memory_limit = self::get_memory_limit_bytes();
        $usage_percentage = ($current_memory / $memory_limit) * 100;
        
        if ($usage_percentage > 80) {
            error_log("AI SEO Ultra: High memory usage ({$usage_percentage}%) during operation: {$operation}");
            
            if ($usage_percentage > 90) {
                $formatted_memory = function_exists('size_format') ? size_format($current_memory) : round($current_memory / 1024 / 1024, 2) . 'MB';
                throw new RuntimeException("Memory limit approaching during {$operation}. Current usage: " . $formatted_memory);
            }
        }
        
        return $current_memory;
    }
    
    private static function get_memory_limit_bytes() {
        $memory_limit = ini_get('memory_limit');
        if (preg_match('/^(\d+)(.)$/', $memory_limit, $matches)) {
            $number = (int) $matches[1];
            $suffix = strtoupper($matches[2]);
            
            switch ($suffix) {
                case 'G': return $number * 1024 * 1024 * 1024;
                case 'M': return $number * 1024 * 1024;
                case 'K': return $number * 1024;
                default: return $number;
            }
        }
        return 128 * 1024 * 1024; // Default 128MB
    }
    
    public static function shutdown_handler() {
        $error = error_get_last();
        if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
            error_log("AI SEO Ultra: Fatal error detected - " . $error['message'] . " in " . $error['file'] . " on line " . $error['line']);
        }
    }
}

/**
 * Main Plugin Class
 * Lightweight bootstrap class that initializes the plugin
 */
final class AiSeoOptimizerUltraMain {
    private static $instance = null;
    private $initialized = false;
    
    /**
     * Singleton pattern for optimal memory usage
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        // Initialize monitoring
        AiSeoUltraMonitor::init();
        
        // Hook into WordPress
        add_action('plugins_loaded', [$this, 'init'], 10);
        add_action('init', [$this, 'load_textdomain']);
        
        // Activation/Deactivation hooks
        register_activation_hook(AI_SEO_ULTRA_PLUGIN_FILE, [$this, 'activate']);
        register_deactivation_hook(AI_SEO_ULTRA_PLUGIN_FILE, [$this, 'deactivate']);
    }
    
    /**
     * Initialize the plugin
     */
    public function init() {
        if ($this->initialized) {
            return;
        }
        
        try {
            AiSeoUltraMonitor::check_memory_limit('plugin_init');
            
            // Check dependencies
            if (!$this->check_dependencies()) {
                return;
            }
            
            // Initialize core components
            $this->init_core_components();
            
            $this->initialized = true;
            
            // Hook for other plugins/themes
            do_action('ai_seo_ultra_initialized');
            
        } catch (Exception $e) {
            error_log('AI SEO Ultra: Initialization failed - ' . $e->getMessage());
            add_action('admin_notices', function() use ($e) {
                echo '<div class="notice notice-error"><p><strong>AI SEO Optimizer Ultra:</strong> Plugin initialization failed. ' . esc_html($e->getMessage()) . '</p></div>';
            });
        }
    }
    
    /**
     * Check plugin dependencies
     */
    private function check_dependencies() {
        $missing_deps = [];
        
        // Check for Action Scheduler (recommended)
        if (!function_exists('as_schedule_single_action')) {
            $missing_deps[] = 'Action Scheduler (recommended for background processing)';
        }
        
        // Check for required PHP extensions
        $required_extensions = ['json', 'mbstring', 'dom'];
        foreach ($required_extensions as $ext) {
            if (!extension_loaded($ext)) {
                $missing_deps[] = "PHP {$ext} extension";
            }
        }
        
        if (!empty($missing_deps)) {
            add_action('admin_notices', function() use ($missing_deps) {
                echo '<div class="notice notice-warning"><p><strong>AI SEO Optimizer Ultra:</strong> Missing dependencies: ' . implode(', ', $missing_deps) . '</p></div>';
            });
        }
        
        return true; // Continue even with missing optional dependencies
    }
    
    /**
     * Initialize core components with lazy loading
     */
    private function init_core_components() {
        // Only load what's needed when it's needed
        if (is_admin()) {
            AiSeoOptimizerUltra\Admin\AdminManager::get_instance();
        }
        
        if (!is_admin() || wp_doing_ajax()) {
            AiSeoOptimizerUltra\Frontend\FrontendManager::get_instance();
        }
        
        // Always load core functionality
        AiSeoOptimizerUltra\Core\CoreManager::get_instance();
    }
    
    /**
     * Load plugin textdomain
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'ai-seo-optimizer-ultra',
            false,
            dirname(AI_SEO_ULTRA_PLUGIN_BASENAME) . '/languages'
        );
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        try {
            // Create necessary database tables
            AiSeoOptimizerUltra\Core\Database::create_tables();
            
            // Set default options
            AiSeoOptimizerUltra\Core\Options::set_defaults();
            
            // Schedule cleanup tasks
            if (function_exists('as_schedule_recurring_action')) {
                $day_in_seconds = defined('DAY_IN_SECONDS') ? DAY_IN_SECONDS : 86400;
                as_schedule_recurring_action(
                    time() + $day_in_seconds,
                    $day_in_seconds,
                    'ai_seo_ultra_daily_cleanup',
                    [],
                    'ai-seo-ultra-maintenance'
                );
            }
            
            // Flush rewrite rules
            flush_rewrite_rules();
            
        } catch (Exception $e) {
            error_log('AI SEO Ultra: Activation failed - ' . $e->getMessage());
            wp_die('Plugin activation failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        try {
            // Clear scheduled actions
            if (function_exists('as_unschedule_all_actions')) {
                as_unschedule_all_actions('ai_seo_ultra_daily_cleanup');
            }
            
            // Clear caches
            AiSeoOptimizerUltra\Core\Cache::clear_all();
            
            // Flush rewrite rules
            flush_rewrite_rules();
            
        } catch (Exception $e) {
            error_log('AI SEO Ultra: Deactivation failed - ' . $e->getMessage());
        }
    }
    
    /**
     * Prevent cloning
     */
    private function __clone() {}
    
    /**
     * Prevent unserialization
     */
    public function __wakeup() {
        throw new Exception('Cannot unserialize singleton');
    }
}

// Initialize the plugin
AiSeoOptimizerUltraMain::get_instance();
