<?php
/**
 * Main admin page template
 * 
 * @package UltraFeaturedImageOptimizer
 */

if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="ufio-admin-wrap">
    <!-- Header -->
    <div class="ufio-header">
        <h1><?php _e('Ultra Featured Image Optimizer', 'ultra-featured-image-optimizer'); ?></h1>
        <p class="description">
            <?php _e('AI-powered image optimization with advanced SEO features and performance optimization.', 'ultra-featured-image-optimizer'); ?>
        </p>
    </div>

    <!-- Notices -->
    <div class="ufio-notices"></div>

    <!-- Statistics Overview -->
    <div class="ufio-stats-grid">
        <div class="ufio-stat-card">
            <div class="ufio-stat-number" data-stat="total_posts"><?php echo esc_html($stats['total_posts']); ?></div>
            <div class="ufio-stat-label"><?php _e('Total Posts', 'ultra-featured-image-optimizer'); ?></div>
        </div>
        
        <div class="ufio-stat-card success">
            <div class="ufio-stat-number" data-stat="posts_with_featured"><?php echo esc_html($stats['posts_with_featured']); ?></div>
            <div class="ufio-stat-label"><?php _e('With Featured Images', 'ultra-featured-image-optimizer'); ?></div>
        </div>
        
        <div class="ufio-stat-card">
            <div class="ufio-stat-number" data-stat="total_images"><?php echo esc_html($stats['total_images']); ?></div>
            <div class="ufio-stat-label"><?php _e('Total Images', 'ultra-featured-image-optimizer'); ?></div>
        </div>
        
        <div class="ufio-stat-card success">
            <div class="ufio-stat-number" data-stat="optimized_images"><?php echo esc_html($stats['optimized_images']); ?></div>
            <div class="ufio-stat-label"><?php _e('Optimized Images', 'ultra-featured-image-optimizer'); ?></div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="ufio-grid">
        <!-- Featured Image Coverage -->
        <div class="ufio-card">
            <h2><?php _e('Featured Image Coverage', 'ultra-featured-image-optimizer'); ?></h2>
            <div class="ufio-progress">
                <div class="ufio-progress-bar" data-progress="featured" style="width: <?php echo esc_attr($stats['featured_coverage']); ?>%"></div>
                <div class="ufio-progress-text" data-progress="featured"><?php echo esc_html($stats['featured_coverage']); ?>%</div>
            </div>
            <p><?php printf(__('%d of %d posts have featured images', 'ultra-featured-image-optimizer'), $stats['posts_with_featured'], $stats['total_posts']); ?></p>
            
            <div class="ufio-button-group">
                <button class="ufio-button primary ufio-bulk-process" data-post-type="post" data-limit="50">
                    <?php _e('Process Posts', 'ultra-featured-image-optimizer'); ?>
                </button>
                <button class="ufio-button secondary ufio-bulk-process" data-post-type="page" data-limit="25">
                    <?php _e('Process Pages', 'ultra-featured-image-optimizer'); ?>
                </button>
            </div>
            
            <!-- Bulk Progress -->
            <div class="ufio-bulk-progress" style="display: none; margin-top: 15px;">
                <div class="ufio-progress">
                    <div class="ufio-progress-bar" style="width: 0%"></div>
                    <div class="ufio-progress-text">0%</div>
                </div>
            </div>
        </div>

        <!-- Image Optimization Status -->
        <div class="ufio-card">
            <h2><?php _e('Image Optimization Status', 'ultra-featured-image-optimizer'); ?></h2>
            <div class="ufio-progress">
                <div class="ufio-progress-bar" data-progress="optimization" style="width: <?php echo esc_attr($stats['optimization_coverage']); ?>%"></div>
                <div class="ufio-progress-text" data-progress="optimization"><?php echo esc_html($stats['optimization_coverage']); ?>%</div>
            </div>
            <p><?php printf(__('%d of %d images are optimized', 'ultra-featured-image-optimizer'), $stats['optimized_images'], $stats['total_images']); ?></p>
            
            <div class="ufio-button-group">
                <a href="<?php echo admin_url('admin.php?page=ufio-tools'); ?>" class="ufio-button primary">
                    <?php _e('Optimization Tools', 'ultra-featured-image-optimizer'); ?>
                </a>
                <button class="ufio-button secondary ufio-refresh-stats">
                    <?php _e('Refresh Stats', 'ultra-featured-image-optimizer'); ?>
                </button>
            </div>
        </div>

        <!-- Processing Queue -->
        <div class="ufio-card">
            <h2><?php _e('Processing Queue', 'ultra-featured-image-optimizer'); ?></h2>
            <div class="ufio-queue-status">
                <div class="ufio-queue-item">
                    <div class="ufio-queue-number" data-queue="pending"><?php echo esc_html($stats['queue_pending']); ?></div>
                    <div class="ufio-queue-label"><?php _e('Pending', 'ultra-featured-image-optimizer'); ?></div>
                </div>
                <div class="ufio-queue-item">
                    <div class="ufio-queue-number" data-queue="processing">0</div>
                    <div class="ufio-queue-label"><?php _e('Processing', 'ultra-featured-image-optimizer'); ?></div>
                </div>
                <div class="ufio-queue-item">
                    <div class="ufio-queue-number" data-queue="completed">0</div>
                    <div class="ufio-queue-label"><?php _e('Completed', 'ultra-featured-image-optimizer'); ?></div>
                </div>
                <div class="ufio-queue-item">
                    <div class="ufio-queue-number" data-queue="failed">0</div>
                    <div class="ufio-queue-label"><?php _e('Failed', 'ultra-featured-image-optimizer'); ?></div>
                </div>
            </div>
            
            <div class="ufio-button-group">
                <a href="<?php echo admin_url('admin.php?page=ufio-analytics'); ?>" class="ufio-button secondary">
                    <?php _e('View Analytics', 'ultra-featured-image-optimizer'); ?>
                </a>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="ufio-card">
            <h2><?php _e('Quick Actions', 'ultra-featured-image-optimizer'); ?></h2>
            
            <div class="ufio-form-group">
                <label><?php _e('API Configuration', 'ultra-featured-image-optimizer'); ?></label>
                <div class="ufio-button-group">
                    <button class="ufio-button secondary ufio-test-api">
                        <?php _e('Test API Connection', 'ultra-featured-image-optimizer'); ?>
                    </button>
                    <a href="<?php echo admin_url('admin.php?page=ufio-settings'); ?>" class="ufio-button secondary">
                        <?php _e('Settings', 'ultra-featured-image-optimizer'); ?>
                    </a>
                </div>
                <div class="ufio-api-test-result"></div>
            </div>
            
            <div class="ufio-form-group">
                <label><?php _e('Cache Management', 'ultra-featured-image-optimizer'); ?></label>
                <p class="description">
                    <?php _e('Current cache size:', 'ultra-featured-image-optimizer'); ?> 
                    <span class="ufio-cache-size"><?php echo size_format($stats['cache_size']); ?></span>
                </p>
                <button class="ufio-button warning ufio-clear-cache">
                    <?php _e('Clear All Cache', 'ultra-featured-image-optimizer'); ?>
                </button>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="ufio-card">
            <h2><?php _e('Recent Activity', 'ultra-featured-image-optimizer'); ?></h2>
            
            <?php
            // Get recent processing activity
            global $wpdb;
            $recent_activity = $wpdb->get_results(
                "SELECT * FROM {$wpdb->prefix}ufio_processing_queue 
                 WHERE status IN ('completed', 'failed') 
                 ORDER BY completed_at DESC 
                 LIMIT 5",
                ARRAY_A
            );
            ?>
            
            <?php if (!empty($recent_activity)): ?>
                <div class="ufio-activity-list">
                    <?php foreach ($recent_activity as $activity): ?>
                        <div class="ufio-activity-item">
                            <div class="ufio-activity-status <?php echo esc_attr($activity['status']); ?>">
                                <?php echo $activity['status'] === 'completed' ? '✓' : '✗'; ?>
                            </div>
                            <div class="ufio-activity-details">
                                <div class="ufio-activity-action">
                                    <?php echo esc_html(ucfirst(str_replace('_', ' ', $activity['action']))); ?>
                                </div>
                                <div class="ufio-activity-time">
                                    <?php echo human_time_diff(strtotime($activity['completed_at']), current_time('timestamp')); ?> ago
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <p class="description"><?php _e('No recent activity found.', 'ultra-featured-image-optimizer'); ?></p>
            <?php endif; ?>
            
            <div class="ufio-button-group">
                <a href="<?php echo admin_url('admin.php?page=ufio-analytics'); ?>" class="ufio-button secondary">
                    <?php _e('View Full History', 'ultra-featured-image-optimizer'); ?>
                </a>
            </div>
        </div>

        <!-- System Status -->
        <div class="ufio-card">
            <h2><?php _e('System Status', 'ultra-featured-image-optimizer'); ?></h2>
            
            <div class="ufio-system-checks">
                <?php
                $checks = [
                    'php_version' => [
                        'label' => __('PHP Version', 'ultra-featured-image-optimizer'),
                        'value' => PHP_VERSION,
                        'status' => version_compare(PHP_VERSION, '7.4', '>=') ? 'success' : 'warning',
                        'requirement' => '7.4+',
                    ],
                    'memory_limit' => [
                        'label' => __('Memory Limit', 'ultra-featured-image-optimizer'),
                        'value' => ini_get('memory_limit'),
                        'status' => 'success',
                        'requirement' => '128M+',
                    ],
                    'max_execution_time' => [
                        'label' => __('Max Execution Time', 'ultra-featured-image-optimizer'),
                        'value' => ini_get('max_execution_time') . 's',
                        'status' => ini_get('max_execution_time') >= 30 ? 'success' : 'warning',
                        'requirement' => '30s+',
                    ],
                    'curl_support' => [
                        'label' => __('cURL Support', 'ultra-featured-image-optimizer'),
                        'value' => function_exists('curl_init') ? __('Available', 'ultra-featured-image-optimizer') : __('Not Available', 'ultra-featured-image-optimizer'),
                        'status' => function_exists('curl_init') ? 'success' : 'error',
                        'requirement' => __('Required', 'ultra-featured-image-optimizer'),
                    ],
                    'gd_support' => [
                        'label' => __('GD Library', 'ultra-featured-image-optimizer'),
                        'value' => extension_loaded('gd') ? __('Available', 'ultra-featured-image-optimizer') : __('Not Available', 'ultra-featured-image-optimizer'),
                        'status' => extension_loaded('gd') ? 'success' : 'warning',
                        'requirement' => __('Recommended', 'ultra-featured-image-optimizer'),
                    ],
                ];
                ?>
                
                <?php foreach ($checks as $check): ?>
                    <div class="ufio-system-check">
                        <div class="ufio-check-status <?php echo esc_attr($check['status']); ?>">
                            <?php echo $check['status'] === 'success' ? '✓' : ($check['status'] === 'warning' ? '⚠' : '✗'); ?>
                        </div>
                        <div class="ufio-check-details">
                            <div class="ufio-check-label"><?php echo esc_html($check['label']); ?></div>
                            <div class="ufio-check-value">
                                <?php echo esc_html($check['value']); ?>
                                <span class="ufio-check-requirement">(<?php echo esc_html($check['requirement']); ?>)</span>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>

<style>
.ufio-activity-list {
    margin: 15px 0;
}

.ufio-activity-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 0;
    border-bottom: 1px solid var(--ufio-gray-200);
}

.ufio-activity-item:last-child {
    border-bottom: none;
}

.ufio-activity-status {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #fff;
    flex-shrink: 0;
}

.ufio-activity-status.completed {
    background: var(--ufio-success);
}

.ufio-activity-status.failed {
    background: var(--ufio-error);
}

.ufio-activity-details {
    flex: 1;
}

.ufio-activity-action {
    font-weight: 500;
    color: var(--ufio-gray-900);
}

.ufio-activity-time {
    font-size: 12px;
    color: var(--ufio-gray-700);
}

.ufio-system-checks {
    margin: 15px 0;
}

.ufio-system-check {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 0;
    border-bottom: 1px solid var(--ufio-gray-200);
}

.ufio-system-check:last-child {
    border-bottom: none;
}

.ufio-check-status {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #fff;
    flex-shrink: 0;
}

.ufio-check-status.success {
    background: var(--ufio-success);
}

.ufio-check-status.warning {
    background: var(--ufio-warning);
}

.ufio-check-status.error {
    background: var(--ufio-error);
}

.ufio-check-details {
    flex: 1;
}

.ufio-check-label {
    font-weight: 500;
    color: var(--ufio-gray-900);
}

.ufio-check-value {
    font-size: 12px;
    color: var(--ufio-gray-700);
}

.ufio-check-requirement {
    opacity: 0.7;
}

.ufio-stats-loading {
    display: none;
    text-align: center;
    padding: 20px;
}
</style>
