/**
 * Schema Markup JavaScript Functionality
 *
 * This file contains the JavaScript code for handling schema markup functionality
 * in both the Manual Process and Review AI Suggestions sections.
 */

jQuery(document).ready(function($) {
    // Constants
    const MANUAL_PROCESS_NONCE = ai_seo_params.manual_process_nonce;
    const REVIEW_SUGGESTIONS_NONCE = ai_seo_params.review_suggestions_nonce;

    /**
     * Manual Process - Generate Schema Markup for selected posts
     */
    $('#ai-seo-generate-schema-batch').on('click', function(e) {
        e.preventDefault();

        const $button = $(this);
        const $spinner = $button.next('.spinner');
        const $result = $('#ai-seo-schema-batch-result');

        // Get selected post IDs
        const selectedPostIds = [];
        $('.ai-seo-manual-select:checked').each(function() {
            selectedPostIds.push($(this).val());
        });

        if (selectedPostIds.length === 0) {
            $result.html('<div class="ai-seo-ajax-result notice-warning"><span class="dashicons"></span>No posts selected.</div>');
            return;
        }

        // Disable button and show spinner
        $button.prop('disabled', true);
        $spinner.css('visibility', 'visible');
        $result.html('');

        // Make AJAX request
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'ai_seo_generate_schema_batch',
                post_ids: selectedPostIds,
                nonce: MANUAL_PROCESS_NONCE
            },
            success: function(response) {
                if (response.success) {
                    $result.html('<div class="ai-seo-ajax-result notice-success"><span class="dashicons"></span>' + response.data.message + '</div>');

                    // Update UI to show schema markup is enabled for selected posts
                    selectedPostIds.forEach(function(postId) {
                        $('.ai-seo-schema-markup-cb[data-post-id="' + postId + '"]').prop('checked', true);
                    });
                } else {
                    $result.html('<div class="ai-seo-ajax-result notice-error"><span class="dashicons"></span>' + response.data.message + '</div>');
                }
            },
            error: function() {
                $result.html('<div class="ai-seo-ajax-result notice-error"><span class="dashicons"></span>Error generating schema markup. Please try again.</div>');
            },
            complete: function() {
                $button.prop('disabled', false);
                $spinner.css('visibility', 'hidden');
            }
        });
    });

    /**
     * Review AI Suggestions - Apply Schema Markup for a specific post
     */
    $('.ai-seo-apply-schema-btn').on('click', function(e) {
        e.preventDefault();

        const $button = $(this);
        const $spinner = $button.next('.spinner');
        const $result = $button.closest('.ai-seo-review-item').find('.ai-seo-schema-result');
        const postId = $button.data('post-id');
        const schemaEnabled = $button.closest('.ai-seo-review-item').find('.schema-enabled-radio:checked').val() === 'yes';

        // Disable button and show spinner
        $button.prop('disabled', true);
        $spinner.css('visibility', 'visible');
        $result.html('');

        // Make AJAX request
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'ai_seo_apply_schema',
                post_id: postId,
                schema_enabled: schemaEnabled,
                nonce: REVIEW_SUGGESTIONS_NONCE
            },
            success: function(response) {
                if (response.success) {
                    $result.html('<div class="ai-seo-ajax-result notice-success"><span class="dashicons"></span>' + response.data.message + '</div>');

                    // Update the UI to reflect the change without requiring a page refresh
                    const $reviewItem = $button.closest('.ai-seo-review-item');
                    if (schemaEnabled) {
                        $reviewItem.find('.schema-enabled-radio[value="yes"]').prop('checked', true);
                    } else {
                        $reviewItem.find('.schema-enabled-radio[value="no"]').prop('checked', true);
                    }
                } else {
                    $result.html('<div class="ai-seo-ajax-result notice-error"><span class="dashicons"></span>' + response.data.message + '</div>');
                }
            },
            error: function() {
                $result.html('<div class="ai-seo-ajax-result notice-error"><span class="dashicons"></span>Error applying schema markup. Please try again.</div>');
            },
            complete: function() {
                $button.prop('disabled', false);
                $spinner.css('visibility', 'hidden');
            }
        });
    });

    /**
     * Review AI Suggestions - Apply Schema Markup for all visible posts
     */
    $('#ai-seo-apply-schema-all').on('click', function(e) {
        e.preventDefault();

        const $button = $(this);
        const $spinner = $button.next('.spinner');
        const $result = $('#ai-seo-schema-result');

        // Get all visible post IDs
        const postIds = [];
        $('.ai-seo-review-item:visible').each(function() {
            postIds.push($(this).data('postid'));
        });

        if (postIds.length === 0) {
            $result.html('<div class="ai-seo-ajax-result notice-warning"><span class="dashicons"></span>No posts visible.</div>');
            return;
        }

        // Get schema enabled state from global checkbox
        const schemaEnabled = $('#ai-seo-global-schema-markup').is(':checked');

        // Disable button and show spinner
        $button.prop('disabled', true);
        $spinner.css('visibility', 'visible');
        $result.html('');

        // Store the original post list to prevent it from disappearing
        const $visiblePosts = $('.ai-seo-review-item:visible').clone(true);

        // Process each post one by one
        let processed = 0;
        let succeeded = 0;
        let failed = 0;

        function processNextPost(index) {
            if (index >= postIds.length) {
                // All posts processed
                $result.html('<div class="ai-seo-ajax-result notice-success"><span class="dashicons"></span>Schema markup ' + (schemaEnabled ? 'applied to' : 'disabled for') + ' ' + succeeded + ' posts. ' + (failed > 0 ? failed + ' posts failed.' : '') + '</div>');
                $button.prop('disabled', false);
                $spinner.css('visibility', 'hidden');

                // Make sure posts are still visible after processing
                if ($('.ai-seo-review-item:visible').length === 0) {
                    // If posts disappeared, restore them
                    $('.ai-seo-review-items-container').html($visiblePosts);
                }

                return;
            }

            const postId = postIds[index];

            // Make AJAX request for this post
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'ai_seo_apply_schema',
                    post_id: postId,
                    schema_enabled: schemaEnabled,
                    nonce: REVIEW_SUGGESTIONS_NONCE,
                    prevent_refresh: true // Add flag to prevent page state changes
                },
                success: function(response) {
                    if (response.success) {
                        succeeded++;

                        // Update UI to show schema markup is enabled/disabled for this post
                        const $reviewItem = $('.ai-seo-review-item[data-postid="' + postId + '"]');
                        $reviewItem.find('.schema-enabled-radio[value="' + (schemaEnabled ? 'yes' : 'no') + '"]').prop('checked', true);
                    } else {
                        failed++;
                    }

                    processed++;
                    $result.html('<div class="ai-seo-ajax-result notice-info"><span class="dashicons"></span>Processing... ' + processed + ' of ' + postIds.length + ' posts</div>');

                    // Process next post
                    processNextPost(index + 1);
                },
                error: function() {
                    failed++;
                    processed++;
                    $result.html('<div class="ai-seo-ajax-result notice-info"><span class="dashicons"></span>Processing... ' + processed + ' of ' + postIds.length + ' posts</div>');

                    // Process next post
                    processNextPost(index + 1);
                }
            });
        }

        // Start processing
        processNextPost(0);
    });

    /**
     * Review AI Suggestions - Apply Schema Markup when using "Apply Checked" button
     */
    $('.ai-seo-apply-single-btn, #ai-seo-apply-all-visible').on('click', function(e) {
        // This code will run when the "Apply Checked" button is clicked
        // We need to check if schema markup is one of the selected elements

        const $reviewItem = $(this).closest('.ai-seo-review-item');
        const isApplyAll = $(this).attr('id') === 'ai-seo-apply-all-visible';

        // If it's "Apply All Visible", we need to check all visible review items
        const $items = isApplyAll ? $('.ai-seo-review-item:visible') : $reviewItem;

        $items.each(function() {
            const $item = $(this);
            const $schemaCheckbox = $item.find('.ai-seo-apply-element-cb[value="schema"]');

            // If schema checkbox is checked, we need to apply schema markup
            if ($schemaCheckbox.is(':checked')) {
                const postId = $item.data('post-id');
                const schemaEnabled = $item.find('.schema-enabled-radio:checked').val() === 'yes';

                // Make AJAX request to apply schema markup
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'ai_seo_apply_schema',
                        post_id: postId,
                        schema_enabled: schemaEnabled,
                        nonce: REVIEW_SUGGESTIONS_NONCE
                    },
                    success: function(response) {
                        if (response.success) {
                            $item.find('.ai-seo-schema-result').html('<div class="ai-seo-ajax-result notice-success"><span class="dashicons"></span>' + response.data.message + '</div>');
                        } else {
                            $item.find('.ai-seo-schema-result').html('<div class="ai-seo-ajax-result notice-error"><span class="dashicons"></span>' + response.data.message + '</div>');
                        }
                    },
                    error: function() {
                        $item.find('.ai-seo-schema-result').html('<div class="ai-seo-ajax-result notice-error"><span class="dashicons"></span>Error applying schema markup. Please try again.</div>');
                    }
                });
            }
        });
    });

    /**
     * Manual Process - Include schema markup checkbox state when processing posts
     */
    $('#ai-seo-process-batch-now, #ai-seo-schedule-manual-batch').on('click', function(e) {
        // This code will run when the "Process Immediate" or "Process Background" buttons are clicked
        // We need to include the schema markup checkbox state in the AJAX request

        const isProcessNow = $(this).attr('id') === 'ai-seo-process-batch-now';
        const action = isProcessNow ? 'ai_seo_process_batch_now' : 'ai_seo_schedule_manual_batch';

        // Get selected post IDs and their schema markup checkbox states
        const selectedPostIds = [];
        const schemaSettings = {};

        // Get global schema markup checkbox state
        const globalSchemaEnabled = $('#ai-seo-manual-schema-markup').is(':checked');

        $('.ai-seo-manual-select:checked').each(function() {
            const postId = $(this).val();
            selectedPostIds.push(postId);

            // Use global schema markup checkbox state for all selected posts
            schemaSettings[postId] = globalSchemaEnabled;
        });

        // Store the original post list to prevent it from disappearing
        const $visiblePosts = $('.ai-seo-post-list tr:visible').clone(true);

        // Add schema settings to the original AJAX request data
        const originalData = {
            action: action,
            post_ids: selectedPostIds,
            nonce: MANUAL_PROCESS_NONCE,
            schema_settings: JSON.stringify(schemaSettings),
            prevent_refresh: true // Add flag to prevent page state changes
        };

        // Store the original AJAX request data for later use
        $(this).data('ajax-data', originalData);

        // Override the default AJAX handler for the Process Immediate button
        if (isProcessNow) {
            e.preventDefault();

            const $button = $(this);
            const $spinner = $button.next('.spinner');
            const $result = $('#ai-seo-batch-result');

            // Disable button and show spinner
            $button.prop('disabled', true);
            $spinner.css('visibility', 'visible');
            $result.html('');

            // Make AJAX request
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: originalData,
                success: function(response) {
                    if (response.success) {
                        let successCount = 0;
                        let errorCount = 0;
                        let skippedCount = 0;

                        // Count results by status
                        for (const postId in response.data.results) {
                            const result = response.data.results[postId];
                            if (result.status === 'success') successCount++;
                            else if (result.status === 'error') errorCount++;
                            else if (result.status === 'skipped') skippedCount++;
                        }

                        // Show result message
                        $result.html(
                            '<div class="ai-seo-ajax-result notice-success">' +
                            '<span class="dashicons"></span>' +
                            'Processed ' + successCount + ' posts successfully. ' +
                            (errorCount > 0 ? errorCount + ' posts failed. ' : '') +
                            (skippedCount > 0 ? skippedCount + ' posts skipped. ' : '') +
                            '</div>'
                        );

                        // Update UI to show schema markup is enabled for selected posts
                        if (globalSchemaEnabled) {
                            selectedPostIds.forEach(function(postId) {
                                $('.ai-seo-schema-markup-cb[data-post-id="' + postId + '"]').prop('checked', true);
                            });
                        }

                        // Make sure posts are still visible after processing
                        if ($('.ai-seo-post-list tr:visible').length === 0) {
                            // If posts disappeared, restore them
                            $('.ai-seo-post-list').html($visiblePosts);
                        }
                    } else {
                        $result.html('<div class="ai-seo-ajax-result notice-error"><span class="dashicons"></span>' + response.data.message + '</div>');
                    }
                },
                error: function() {
                    $result.html('<div class="ai-seo-ajax-result notice-error"><span class="dashicons"></span>Error processing posts. Please try again.</div>');
                },
                complete: function() {
                    $button.prop('disabled', false);
                    $spinner.css('visibility', 'hidden');
                }
            });

            return false; // Prevent default form submission
        }
    });
});
